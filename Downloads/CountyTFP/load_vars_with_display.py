# ==============================================
# 显示并选择性加载保存的变量
# ==============================================

import pandas as pd
import geopandas as gpd
import pickle
import os

print("=== 从 county_filter_results.pkl 显示并加载变量 ===")

# 1. 加载文件并显示内容
pkl_file = "notebooks/shared_data/county_filter_results.pkl"

if os.path.exists(pkl_file):
    try:
        with open(pkl_file, 'rb') as f:
            loaded_vars = pickle.load(f)
        
        print(f"✅ 成功读取文件: {pkl_file}")
        print(f"   文件大小: {os.path.getsize(pkl_file)/1024/1024:.2f} MB")
        
        # 2. 显示所有可用变量
        print(f"\n📋 文件中包含的变量:")
        
        if isinstance(loaded_vars, dict):
            for i, (var_name, var_data) in enumerate(loaded_vars.items(), 1):
                print(f"\n   {i}. 变量名: {var_name}")
                print(f"      类型: {type(var_data).__name__}")
                
                if hasattr(var_data, '__len__'):
                    print(f"      长度: {len(var_data)}")
                    
                    if hasattr(var_data, 'columns'):
                        print(f"      列数: {len(var_data.columns)}")
                        
                        # 显示关键信息
                        if 'filtered' in var_name.lower():
                            print(f"      ⭐ 这是过滤后的县级数据")
                            if 'data_source' in var_data.columns:
                                print(f"      ⚠️  包含 data_source 列")
                                source_counts = var_data['data_source'].value_counts()
                                for source, count in source_counts.items():
                                    print(f"         {source}: {count} 条")
                            else:
                                print(f"      ✅ 纯净的过滤数据")
                                
                        elif 'removed' in var_name.lower():
                            print(f"      📝 这是被移除的区县信息")
                            
                        # 显示前几列
                        print(f"      列名 (前8个): {list(var_data.columns)[:8]}")
                        
                elif isinstance(var_data, list):
                    print(f"      列表长度: {len(var_data)}")
                    if len(var_data) > 0:
                        print(f"      元素类型: {type(var_data[0]).__name__}")
                        if isinstance(var_data[0], dict):
                            print(f"      第一个元素的键: {list(var_data[0].keys())}")
                
                else:
                    print(f"      值: {str(var_data)[:100]}...")
        
        # 3. 提供选择性加载选项
        print(f"\n🔧 加载选项:")
        print(f"   1. 全部加载到当前环境")
        print(f"   2. 只加载 filtered_county_gdf")
        print(f"   3. 加载并清理数据")
        print(f"   4. 预览而不加载")
        
        # 选项1: 全部加载
        print(f"\n📄 选项1: 全部加载")
        print(f"   ```python")
        print(f"   # 全部加载到全局变量")
        print(f"   for var_name, var_data in loaded_vars.items():")
        print(f"       globals()[var_name] = var_data")
        print(f"       print(f'✅ 加载 {{var_name}}: {{len(var_data) if hasattr(var_data, \"__len__\") else type(var_data).__name__}}')")
        print(f"   ```")
        
        # 选项2: 只加载 filtered_county_gdf
        filtered_keys = [k for k in loaded_vars.keys() if 'filtered' in k.lower()]
        if filtered_keys:
            filtered_key = filtered_keys[0]
            filtered_data = loaded_vars[filtered_key]
            
            print(f"\n📄 选项2: 只加载 {filtered_key}")
            print(f"   记录数: {len(filtered_data)}")
            print(f"   ```python")
            print(f"   filtered_county_gdf = loaded_vars['{filtered_key}']")
            print(f"   print(f'✅ 加载 filtered_county_gdf: {{len(filtered_county_gdf)}} 条记录')")
            print(f"   ```")
            
            # 选项3: 加载并清理
            if hasattr(filtered_data, 'columns') and 'data_source' in filtered_data.columns:
                print(f"\n📄 选项3: 加载并清理数据")
                print(f"   ```python")
                print(f"   # 加载并清理 data_source 列")
                print(f"   filtered_county_gdf = loaded_vars['{filtered_key}']")
                print(f"   ")
                print(f"   if 'data_source' in filtered_county_gdf.columns:")
                print(f"       print('清理前:', filtered_county_gdf['data_source'].value_counts().to_dict())")
                print(f"       ")
                print(f"       # 如果有 original_filtered，只保留这部分")
                print(f"       if 'original_filtered' in filtered_county_gdf['data_source'].values:")
                print(f"           filtered_county_gdf = filtered_county_gdf[")
                print(f"               filtered_county_gdf['data_source'] == 'original_filtered'")
                print(f"           ].drop(columns=['data_source']).copy()")
                print(f"           print('✅ 提取原始过滤数据')")
                print(f"       else:")
                print(f"           # 否则直接删除 data_source 列")
                print(f"           filtered_county_gdf = filtered_county_gdf.drop(columns=['data_source'])")
                print(f"           print('✅ 删除 data_source 列')")
                print(f"   ")
                print(f"   print(f'清理后: {{len(filtered_county_gdf)}} 条记录')")
                print(f"   ```")
        
        # 选项4: 预览
        print(f"\n📄 选项4: 预览数据")
        for var_name in loaded_vars.keys():
            if hasattr(loaded_vars[var_name], 'head'):
                print(f"   ```python")
                print(f"   # 预览 {var_name}")
                print(f"   print('=== {var_name} ===') ")
                print(f"   print(loaded_vars['{var_name}'].head())")
                print(f"   print('形状:', loaded_vars['{var_name}'].shape)")
                print(f"   ```")
        
        # 额外选项: 备份当前变量
        print(f"\n⚠️  安全选项: 备份当前变量")
        print(f"   ```python")
        print(f"   # 备份当前变量（如果需要）")
        print(f"   if 'filtered_county_gdf' in globals():")
        print(f"       filtered_county_gdf_backup = filtered_county_gdf.copy()")
        print(f"       print(f'✅ 已备份当前 filtered_county_gdf: {{len(filtered_county_gdf_backup)}} 条')")
        print(f"   ```")
        
        # 一键恢复脚本
        print(f"\n🚀 推荐的恢复流程:")
        print(f"   ```python")
        print(f"   # 1. 备份当前数据")
        print(f"   if 'filtered_county_gdf' in globals():")
        print(f"       filtered_county_gdf_current = filtered_county_gdf.copy()")
        print(f"   ")
        print(f"   # 2. 加载保存的数据")
        if filtered_keys:
            print(f"   filtered_county_gdf_saved = loaded_vars['{filtered_keys[0]}']")
            print(f"   ")
            print(f"   # 3. 检查并清理")
            print(f"   if 'data_source' in filtered_county_gdf_saved.columns:")
            print(f"       if 'original_filtered' in filtered_county_gdf_saved['data_source'].values:")
            print(f"           filtered_county_gdf = filtered_county_gdf_saved[")
            print(f"               filtered_county_gdf_saved['data_source'] == 'original_filtered'")
            print(f"           ].drop(columns=['data_source']).copy()")
            print(f"       else:")
            print(f"           filtered_county_gdf = filtered_county_gdf_saved.drop(columns=['data_source'])")
            print(f"   else:")
            print(f"       filtered_county_gdf = filtered_county_gdf_saved.copy()")
            print(f"   ")
            print(f"   # 4. 验证结果")
            print(f"   print(f'恢复后 filtered_county_gdf: {{len(filtered_county_gdf)}} 条记录')")
            print(f"   print(f'列名: {{list(filtered_county_gdf.columns)[:5]}}')")
        print(f"   ```")
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")

else:
    print(f"❌ 文件不存在: {pkl_file}")
    
    # 检查其他位置
    alternative_paths = [
        "shared_data/county_filter_results.pkl",
        "notebooks/county_filter_results.pkl", 
        "county_filter_results.pkl"
    ]
    
    for path in alternative_paths:
        if os.path.exists(path):
            print(f"   ✅ 找到替代文件: {path}")
            break

print(f"\n=== 显示完成 ===") 