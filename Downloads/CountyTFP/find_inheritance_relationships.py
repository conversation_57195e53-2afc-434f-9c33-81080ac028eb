import pandas as pd
import networkx as nx
from collections import defaultdict
import os
import matplotlib.pyplot as plt

# 设置基础路径
BASE_DIR = '/Users/<USER>/Downloads/CountyTFP'

def plot_id_timeline(df, province_code='13'):
    """
    绘制指定省份的ID持续时间图
    
    参数:
    - df: 包含countyid和year列的DataFrame
    - province_code: 省份代码（默认'13'为河北省）
    """
    # 确保countyid是字符串格式
    df['countyid'] = df['countyid'].astype(str).str.zfill(6)
    
    # 筛选指定省份的数据
    province_df = df[df['countyid'].str.startswith(province_code)]
    
    # 获取每个ID的最早和最晚年份
    id_timeline = {}
    for county_id in province_df['countyid'].unique():
        years = province_df[province_df['countyid'] == county_id]['year'].unique()
        if len(years) > 0:
            id_timeline[county_id] = {
                'start_year': min(years),
                'end_year': max(years)
            }
    
    # 转换为DataFrame以便绘图
    timeline_data = []
    for county_id, timeline in id_timeline.items():
        timeline_data.append({
            'countyid': county_id,
            'start_year': timeline['start_year'],
            'end_year': timeline['end_year'],
            'duration': timeline['end_year'] - timeline['start_year'] + 1
        })
    
    timeline_df = pd.DataFrame(timeline_data)
    
    # 按开始年份排序
    timeline_df = timeline_df.sort_values('start_year')
    
    # 创建图形
    plt.figure(figsize=(15, 8))
    
    # 绘制水平条形图
    y_pos = range(len(timeline_df))
    plt.barh(y_pos, timeline_df['duration'], 
             left=timeline_df['start_year'],
             height=0.8,
             alpha=0.6,
             color='skyblue')
    
    # 添加ID标签
    plt.yticks(y_pos, timeline_df['countyid'], fontsize=8)
    
    # 设置标题和轴标签
    plt.title(f'河北省({province_code})各县区ID持续时间', fontsize=12)
    plt.xlabel('年份', fontsize=10)
    plt.ylabel('区划代码', fontsize=10)
    
    # 添加网格线
    plt.grid(True, axis='x', linestyle='--', alpha=0.7)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    output_path = os.path.join(BASE_DIR, 'data', 'processed', f'hebei_id_timeline.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"图片已保存至: {output_path}")
    
    # 显示图片
    plt.show()
    
    # 返回数据供进一步分析
    return timeline_df

def find_potential_inheritance_relationships(df, province_code='13', time_window=5):
    """
    查找潜在的继承关系
    
    参数:
    - df: 包含countyid和year列的DataFrame
    - province_code: 省份代码（默认'13'为河北省）
    - time_window: 时间窗口（默认5年）
    
    返回:
    - 潜在的继承关系列表
    """
    # 确保countyid是字符串格式
    df['countyid'] = df['countyid'].astype(str).str.zfill(6)
    
    # 筛选指定省份的数据
    province_df = df[df['countyid'].str.startswith(province_code)]
    
    # 获取每个ID的最早和最晚年份
    id_timeline = {}
    for county_id in province_df['countyid'].unique():
        years = province_df[province_df['countyid'] == county_id]['year'].unique()
        if len(years) > 0:
            id_timeline[county_id] = {
                'start_year': min(years),
                'end_year': max(years)
            }
    
    # 按结束年份排序
    sorted_ids = sorted(id_timeline.items(), key=lambda x: x[1]['end_year'])
    
    # 查找潜在的继承关系
    potential_relationships = []
    
    for i, (id1, timeline1) in enumerate(sorted_ids):
        end_year1 = timeline1['end_year']
        
        # 查找结束时间在时间窗口内的其他ID
        for id2, timeline2 in sorted_ids[i+1:]:
            start_year2 = timeline2['start_year']
            
            # 检查时间窗口
            if 0 <= start_year2 - end_year1 <= time_window:
                potential_relationships.append({
                    'old_id': id1,
                    'new_id': id2,
                    'old_end_year': end_year1,
                    'new_start_year': start_year2,
                    'time_gap': start_year2 - end_year1
                })
    
    return potential_relationships

def find_complete_lineage_for_code(graph, target_code):
    """
    找到指定代码的完整演化链条
    返回: 按年份排序的完整链条节点列表
    """
    # 1. 找到该代码在图中的所有历史节点
    historical_nodes = [node for node in graph.nodes() if node[0] == target_code]
    
    if not historical_nodes:
        return [], f"代码 {target_code} 在历史图中未找到"
    
    # 2. 收集所有相关的演化链条节点
    all_lineage_nodes = set()
    
    for hist_node in historical_nodes:
        try:
            # 获取祖先节点（历史上的前身）
            ancestors = nx.ancestors(graph, hist_node)
            # 获取后代节点（历史上的后续）
            descendants = nx.descendants(graph, hist_node)
            # 合并：祖先 + 自身 + 后代
            lineage_nodes = ancestors | {hist_node} | descendants
            all_lineage_nodes.update(lineage_nodes)
            
        except nx.NetworkXError as e:
            print(f"  处理节点 {hist_node} 时发生NetworkX错误: {e}")
            continue
    
    if not all_lineage_nodes:
        return [], f"代码 {target_code} 无法构建演化链条"
    
    # 3. 按年份排序
    sorted_lineage = sorted(list(all_lineage_nodes), key=lambda x: int(x[2]))
    
    return sorted_lineage, "success"

def verify_inheritance_relationship(complete_graph, old_id, new_id, old_end_year, new_start_year):
    """
    使用complete graph验证继承关系
    
    参数:
    - complete_graph: NetworkX图对象
    - old_id: 旧ID
    - new_id: 新ID
    - old_end_year: 旧ID结束年份
    - new_start_year: 新ID开始年份
    
    返回:
    - 验证结果字典
    """
    # 获取两个ID的完整演化链条
    old_lineage, old_status = find_complete_lineage_for_code(complete_graph, old_id)
    new_lineage, new_status = find_complete_lineage_for_code(complete_graph, new_id)
    
    # 检查是否在同一个演化链条上
    old_lineage_codes = {node[0] for node in old_lineage}
    new_lineage_codes = {node[0] for node in new_lineage}
    
    # 如果两个链条有交集，说明它们在同一个演化链条上
    is_in_same_lineage = bool(old_lineage_codes & new_lineage_codes)
    
    # 分析结果
    result = {
        'old_id': old_id,
        'new_id': new_id,
        'old_lineage_found': old_status == 'success',
        'new_lineage_found': new_status == 'success',
        'old_lineage_size': len(old_lineage),
        'new_lineage_size': len(new_lineage),
        'is_in_same_lineage': is_in_same_lineage,
        'time_gap': new_start_year - old_end_year,
        'shared_codes': list(old_lineage_codes & new_lineage_codes) if is_in_same_lineage else []
    }
    
    return result

def main():
    # 设置文件路径
    merged_io_tfp_path = os.path.join(BASE_DIR, 'data', 'processed', 'merged_io_tfp.parquet')
    
    # 读取数据
    print("正在读取数据...")
    print(f"数据文件路径: {merged_io_tfp_path}")
    df = pd.read_parquet(merged_io_tfp_path)
    
    # 绘制河北省ID持续时间图
    print("\n正在绘制河北省ID持续时间图...")
    timeline_df = plot_id_timeline(df)
    
    # 查找潜在的继承关系
    print("\n正在查找潜在的继承关系...")
    potential_relationships = find_potential_inheritance_relationships(df)
    
    print(f"\n找到 {len(potential_relationships)} 个潜在的继承关系")
    
    # 验证继承关系
    print("\n正在验证继承关系...")
    verified_relationships = []
    
    for rel in potential_relationships:
        verification = verify_inheritance_relationship(
            complete_graph,
            rel['old_id'],
            rel['new_id'],
            rel['old_end_year'],
            rel['new_start_year']
        )
        verified_relationships.append(verification)
    
    # 输出结果
    print("\n验证结果:")
    for rel in verified_relationships:
        print(f"\n旧ID: {rel['old_id']} -> 新ID: {rel['new_id']}")
        print(f"时间间隔: {rel['time_gap']}年")
        print(f"旧ID演化链条大小: {rel['old_lineage_size']}")
        print(f"新ID演化链条大小: {rel['new_lineage_size']}")
        print(f"是否在同一演化链条: {'是' if rel['is_in_same_lineage'] else '否'}")
        if rel['is_in_same_lineage']:
            print(f"共享的代码: {', '.join(rel['shared_codes'])}")

if __name__ == "__main__":
    main() 