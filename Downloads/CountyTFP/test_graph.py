import networkx as nx
import pandas as pd
import geopandas as gpd
import os
from pathlib import Path

def find_id_nodes_in_time_range(target_code, year=None, time_window=3):
    """
    找到与目标代码相关的所有节点（不考虑时间窗口）
    
    Args:
        target_code: 目标代码
        year: 目标年份（可选）
        time_window: 时间窗口（可选，默认3年）
    
    Returns:
        set: 所有相关节点的集合
    """
    # 确保target_code是字符串
    target_code = str(target_code)
    
    # 找到所有与目标代码相关的节点
    related_nodes = set()
    
    # 遍历所有节点
    for node in G.nodes():
        # 检查是否存在路径关系
        if nx.has_path(G, target_code, node) or nx.has_path(G, node, target_code):
            related_nodes.add(node)
    
    return related_nodes

def build_complete_change_graph(transitions_file):
    """构建完整的变更图"""
    G = nx.DiGraph()
    
    # 读取变更记录
    with open(transitions_file, 'r', encoding='utf-8') as f:
        for line in f:
            parts = line.strip().split(' -> ')
            if len(parts) == 2:
                old_id = parts[0].split(': ')[1]
                new_id = parts[1]
                G.add_edge(old_id, new_id)
    
    return G

def main():
    # 使用绝对路径
    base_dir = "/Users/<USER>/Downloads/CountyTFP"
    transitions_file = os.path.join(base_dir, "notebooks/exploratory/all_transitions_outputs.txt")
    
    # 读取变迁记录
    transitions = []
    with open(transitions_file, 'r', encoding='utf-8') as f:
        for line in f:
            parts = line.strip().split(' -> ')
            if len(parts) == 2:
                transitions.append((parts[0], parts[1]))

    # 构建有向图
    G = nx.DiGraph()
    for old_id, new_id in transitions:
        G.add_edge(old_id, new_id)

    # 测试基本功能
    print("图的基本信息：")
    print(f"节点数: {G.number_of_nodes()}")
    print(f"边数: {G.number_of_edges()}")

    # 测试获取节点
    test_id = "320121"  # 测试用的ID
    print(f"\n测试ID {test_id} 的信息：")
    print(f"后继节点: {list(G.successors(test_id))}")
    print(f"前驱节点: {list(G.predecessors(test_id))}")

    # 测试路径检查
    if G.has_node(test_id):
        # 获取所有可达节点
        reachable = nx.descendants(G, test_id)
        print(f"\n从 {test_id} 可达的节点: {list(reachable)[:5]}...")
        
        # 获取所有可达该节点的节点
        ancestors = nx.ancestors(G, test_id)
        print(f"可达 {test_id} 的节点: {list(ancestors)[:5]}...")

    # 读取数据
    gis_data = gpd.read_file(Path(base_dir) / 'data/raw/CountyGIS/CountyGIS.geojson')
    econ_data = pd.read_csv(Path(base_dir) / 'data/raw/CountyEcon/CountyEcon.csv')

    # 确保列名一致
    gis_data = gis_data.rename(columns={
        'countyid': 'CountyID',
        'countyname': 'CountyName'
    })

    # 逐年匹配
    years = sorted(econ_data['Year'].unique())
    for year in years:
        print(f"\n处理{year}年的数据:")
        
        # 获取当年的经济数据
        year_econ = econ_data[econ_data['Year'] == year]
        year_gis = gis_data[gis_data['Year'] == year]
        
        # 1. 直接ID匹配
        direct_matches = year_econ[year_econ['CountyID'].isin(year_gis['CountyID'])]
        print(f"直接ID匹配数量: {len(direct_matches)}")
        
        # 2. 名称匹配
        name_matches = []
        for _, econ_row in year_econ.iterrows():
            if econ_row['CountyID'] not in direct_matches['CountyID'].values:
                gis_matches = year_gis[year_gis['CountyName'] == econ_row['CountyName']]
                if len(gis_matches) == 1:
                    name_matches.append((econ_row['CountyID'], gis_matches.iloc[0]['CountyID']))
        print(f"名称匹配数量: {len(name_matches)}")
        
        # 3. 变迁链匹配
        lineage_matches = []
        for _, econ_row in year_econ.iterrows():
            if (econ_row['CountyID'] not in direct_matches['CountyID'].values and 
                econ_row['CountyID'] not in [m[0] for m in name_matches]):
                # 使用已定义的函数查找相关节点
                related_nodes = find_id_nodes_in_time_range(G, econ_row['CountyID'])
                if related_nodes:
                    for gis_id in year_gis['CountyID']:
                        if gis_id in related_nodes:
                            lineage_matches.append((econ_row['CountyID'], gis_id))
                            break
        print(f"变迁链匹配数量: {len(lineage_matches)}")
        
        # 统计总匹配数
        total_matches = len(direct_matches) + len(name_matches) + len(lineage_matches)
        print(f"总匹配数: {total_matches}")
        print(f"匹配率: {total_matches/len(year_econ):.2%}")

if __name__ == "__main__":
    main() 