#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析移除市辖区的过去五年名称记录
基于笔记本中的mapping table构建和使用系统
"""

import pandas as pd
import numpy as np
import networkx as nx
from collections import defaultdict, Counter
import ast
from datetime import datetime

def parse_transition_line(line):
    """解析变迁文件中的行数据"""
    try:
        return ast.literal_eval(line.strip())
    except (ValueError, SyntaxError):
        return None

def load_mapping_graph(transitions_file_path):
    """加载历史变迁图"""
    print(f"正在加载历史变迁图: {transitions_file_path}")
    
    all_documented_transitions = []
    try:
        with open(transitions_file_path, 'r', encoding='utf-8') as f:
            for line in f:
                if line.startswith('#'):
                    continue
                transition = parse_transition_line(line)
                if transition:
                    all_documented_transitions.append(transition)
    except FileNotFoundError:
        print(f"错误: 文件未找到 {transitions_file_path}")
        return None
    
    # 构建基础图
    G = nx.DiGraph()
    for src_node, tgt_node, type_str in all_documented_transitions:
        G.add_edge(src_node, tgt_node, type=type_str)
    
    print(f"成功加载历史变迁图: {G.number_of_nodes()} 个节点, {G.number_of_edges()} 条边")
    return G

def identify_district_codes(graph, district_suffix='区'):
    """识别所有市辖区代码"""
    district_codes = set()
    district_records = []
    
    for node in graph.nodes():
        code, name, year = node
        if name.endswith(district_suffix):
            district_codes.add(code)
            district_records.append({
                'code': code,
                'name': name,
                'year': int(year),
                'node': node
            })
    
    print(f"识别到 {len(district_codes)} 个不同的市辖区代码")
    print(f"共 {len(district_records)} 条市辖区历史记录")
    
    return district_codes, district_records

def analyze_district_name_evolution(graph, district_codes, analysis_years=5):
    """分析市辖区在指定年份内的名称演化"""
    
    # 计算分析的年份范围
    current_year = datetime.now().year
    target_years = list(range(current_year - analysis_years, current_year + 1))
    print(f"分析年份范围: {target_years[0]} - {target_years[-1]}")
    
    district_evolution = {}
    
    for district_code in district_codes:
        print(f"\n正在分析区代码: {district_code}")
        
        # 找到该代码的所有历史节点
        historical_nodes = [node for node in graph.nodes() if node[0] == district_code]
        
        if not historical_nodes:
            continue
            
        # 获取完整的演化谱系
        all_lineage_nodes = set()
        for hist_node in historical_nodes:
            try:
                ancestors = nx.ancestors(graph, hist_node)
                descendants = nx.descendants(graph, hist_node)
                lineage_nodes = ancestors | {hist_node} | descendants
                all_lineage_nodes.update(lineage_nodes)
            except nx.NetworkXError:
                continue
        
        # 筛选目标年份范围内的记录
        relevant_records = []
        for node in all_lineage_nodes:
            code, name, year_str = node
            year = int(year_str)
            if year in target_years:
                relevant_records.append({
                    'code': code,
                    'name': name,
                    'year': year,
                    'is_original_code': (code == district_code),
                    'is_district': name.endswith('区')
                })
        
        if relevant_records:
            # 按年份排序
            relevant_records.sort(key=lambda x: x['year'])
            district_evolution[district_code] = relevant_records
            
            print(f"  找到 {len(relevant_records)} 条相关记录")
            for record in relevant_records:
                marker = "★" if record['is_original_code'] else " "
                district_marker = "🏛" if record['is_district'] else " "
                print(f"    {marker}{district_marker} {record['year']}: {record['code']} - {record['name']}")
    
    return district_evolution

def generate_summary_report(district_evolution, analysis_years=5):
    """生成汇总报告"""
    current_year = datetime.now().year
    
    print(f"\n{'='*80}")
    print(f"移除市辖区过去{analysis_years}年名称记录分析报告")
    print(f"分析时间范围: {current_year - analysis_years} - {current_year}")
    print(f"{'='*80}")
    
    # 统计总体情况
    total_districts = len(district_evolution)
    total_records = sum(len(records) for records in district_evolution.values())
    
    print(f"\n📊 总体统计:")
    print(f"  分析的市辖区数量: {total_districts}")
    print(f"  总历史记录数: {total_records}")
    
    # 按年份统计
    year_stats = defaultdict(int)
    name_changes = []
    code_changes = []
    
    for district_code, records in district_evolution.items():
        prev_record = None
        for record in records:
            year_stats[record['year']] += 1
            
            if prev_record:
                if prev_record['name'] != record['name']:
                    name_changes.append({
                        'year': record['year'],
                        'original_code': district_code,
                        'from_name': prev_record['name'],
                        'to_name': record['name'],
                        'from_code': prev_record['code'],
                        'to_code': record['code']
                    })
                
                if prev_record['code'] != record['code']:
                    code_changes.append({
                        'year': record['year'],
                        'original_code': district_code,
                        'from_code': prev_record['code'],
                        'to_code': record['code'],
                        'name': record['name']
                    })
            
            prev_record = record
    
    print(f"\n📅 按年份分布:")
    for year in sorted(year_stats.keys()):
        print(f"  {year}年: {year_stats[year]} 条记录")
    
    print(f"\n🔄 名称变化情况:")
    print(f"  发现名称变化: {len(name_changes)} 次")
    for change in name_changes[:10]:  # 显示前10个
        print(f"    {change['year']}: {change['original_code']} - {change['from_name']} → {change['to_name']}")
    if len(name_changes) > 10:
        print(f"    ... 还有 {len(name_changes) - 10} 个名称变化")
    
    print(f"\n🏗️ 代码变化情况:")
    print(f"  发现代码变化: {len(code_changes)} 次")
    for change in code_changes[:10]:  # 显示前10个
        print(f"    {change['year']}: {change['from_code']} → {change['to_code']} ({change['name']})")
    if len(code_changes) > 10:
        print(f"    ... 还有 {len(code_changes) - 10} 个代码变化")
    
    # 详细案例分析
    print(f"\n🔍 详细案例分析 (前5个最复杂的演化链条):")
    complex_cases = sorted(district_evolution.items(), 
                          key=lambda x: len(x[1]), reverse=True)[:5]
    
    for i, (district_code, records) in enumerate(complex_cases, 1):
        print(f"\n  案例 {i}: 区代码 {district_code}")
        print(f"    记录数量: {len(records)}")
        print(f"    演化轨迹:")
        for j, record in enumerate(records):
            marker = "★" if record['is_original_code'] else "  "
            print(f"      {j+1}. {record['year']}: {marker} {record['code']} - {record['name']}")
    
    return {
        'total_districts': total_districts,
        'total_records': total_records,
        'year_stats': dict(year_stats),
        'name_changes': name_changes,
        'code_changes': code_changes,
        'complex_cases': complex_cases
    }

def main():
    """主函数"""
    print("🏛️ 开始分析移除市辖区的过去五年名称记录")
    print("基于mapping table构建和使用系统\n")
    
    # 1. 加载历史变迁图
    transitions_file = "all_transitions_output.txt"
    graph = load_mapping_graph(transitions_file)
    
    if graph is None:
        print("❌ 无法加载历史变迁图，程序退出")
        return
    
    # 2. 识别市辖区代码
    district_codes, district_records = identify_district_codes(graph)
    
    if not district_codes:
        print("❌ 未找到任何市辖区记录，程序退出")
        return
    
    # 3. 分析过去5年的名称演化
    district_evolution = analyze_district_name_evolution(graph, district_codes, analysis_years=5)
    
    # 4. 生成汇总报告
    summary = generate_summary_report(district_evolution, analysis_years=5)
    
    # 5. 保存结果到文件
    output_file = "removed_districts_analysis_report.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("移除市辖区过去五年名称记录分析报告\n")
        f.write("="*50 + "\n\n")
        
        f.write(f"总体统计:\n")
        f.write(f"  分析的市辖区数量: {summary['total_districts']}\n")
        f.write(f"  总历史记录数: {summary['total_records']}\n\n")
        
        f.write("按年份分布:\n")
        for year in sorted(summary['year_stats'].keys()):
            f.write(f"  {year}年: {summary['year_stats'][year]} 条记录\n")
        
        f.write(f"\n名称变化 ({len(summary['name_changes'])} 次):\n")
        for change in summary['name_changes']:
            f.write(f"  {change['year']}: {change['original_code']} - {change['from_name']} → {change['to_name']}\n")
        
        f.write(f"\n代码变化 ({len(summary['code_changes'])} 次):\n")
        for change in summary['code_changes']:
            f.write(f"  {change['year']}: {change['from_code']} → {change['to_code']} ({change['name']})\n")
    
    print(f"\n✅ 分析完成！详细报告已保存到: {output_file}")

if __name__ == "__main__":
    main() 