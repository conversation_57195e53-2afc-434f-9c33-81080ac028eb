# 本项目使用的 diff 规范

## 概述

这套 diff 规范名为 `area-diff`，基于 `git-diff` 修改而来，旨在精确描述新旧区划代码间的对应关系。这套规范的制定始终遵从以下四点原则：

- 逻辑严密：以行政区域的相交来定义新旧区划代码间的对应关系。
- 语法简洁：分隔符均为单个字符，在保证表意清晰的前提下尽可能少。
- 易于维护：语法符合直觉，使用区划名称表示对应关系，便于数据的录入和校对。
- 利于分析：利用处理后的数据，通过不太复杂的算法能够分析出代码间的对应关系并清晰地呈现。

## 背景

1. `data` 目录下的每一个文件称为一个**区划代码数据表**，简称**数据表**。数据表的每一行称为一条**区划代码记录**，简称**记录**。一条记录由**代码**和**名称**组成，由一个制表符 `\t` 隔开。每一条记录都与一定范围的行政区域相对应，称为该记录**对应的区域**。
1. 存在一条唯一且不变的**根记录**，其代码为 `000000`，名称为 `中华人民共和国`，对应的区域为中华人民共和国全境，包括香港特别行政区、澳门特别行政区和台湾省。
1. 除根记录以外，代码的后四位数字均为 0 的记录称为**一级**（省级）记录，非一级记录且代码的后两位数字均为 0 的记录称为**二级**（地级）记录，非一、二级记录的其余记录称为**三级**（县级）记录。两条记录**同级**，当且仅当其同时为一级、二级或三级记录。
1. 在同一个数据表中，成立以下关系：代码的前四位数字与某条二级记录相同的三级记录称为该条二级记录的**子记录**，该条二级记录称为这些三级记录的**父记录**；二级记录和无二级父记录的三级记录，若其代码的前两位数字与某条一级记录相同，则称为该条一级记录的**子记录**，该条一级记录称为这些二级记录和三级记录的**父记录**。根记录为所有一级记录的父记录。
1. 一条记录**对应的叶区域**存在，当且仅当其满足以下两种情况之一：

    - 其没有子记录，此时其对应的区域即为其对应的叶区域。
    - 其所有子记录对应的区域不能完全覆盖其对应的区域，此时其对应的区域中未被其子记录对应的区域覆盖的部分即为其对应的叶区域。

1. `diff` 目录下的每一个文件称为一个**差异表**，包含相应的两个数据表中记录的差异，其中作为相对参考的数据表称为**源表**，与之相对的数据表称为**目标表**。差异表的**原始内容**是通过对相应的数据表执行 `git diff -U0 --no-index` 并去除无关或重复行后得到的。差异表中内容与原始内容完全一致的行称为**原始行**。

## 详细规则

1. 差异表的每一非空行分为四种类型，由其首字符决定，分别为：**删除行** (`-`), **增加行** (`+`), **转移行** (`=`) 及**注释行** (`#`)。其中，转移行和非原始行的删除行、增加行统称为**变更行**。
1. 对差异表的任何修改都应当遵守以下规则：

    - 差异表的原始内容由于地区重名而不完善时，可以向差异表中添加删除行或增加行。
    - 除非上一条规则适用，或对相应的数据表进行了订正，否则**不得**删除差异表中包含原始内容的行，修改其中的原始内容，或向其中添加删除行或增加行。
    - 修改差异表中包含原始内容的行时，**必须**将其中的原始内容始终保持在行首，只修改原始内容之后到行尾的内容。
    - 差异表中的所有行不区分先后，因此可以任意排序，但需注意保持一定的结构。
    - 差异表中的所有行的语法**必须**严格遵守随后的语法规范。

1. 每一变更行在相应的数据表中都有其对应的记录，称为**该行的记录**。删除行的记录位于源表中。增加行的记录位于目标表中。转移行的记录同时位于源表和目标表中。
1. 变更行由其首字符、其记录和其**属性**依序连接而成。变更行的属性具有确定的语法，用于说明该行的记录与相关记录所对应的区域的相交关系。下文中讨论一行的属性时，均假定该行为变更行。
1. 删除行的属性以字符 `>` 起始，后接一个或多个以字符 `,` 分隔的**记录选择器**。增加行的属性以字符 `<` 起始，其后的语法与删除行相同。转移行的属性语法，要么与删除行相同，要么与增加行相同。
1. 记录选择器有五种类型，分别为：**指定名称**、**当前名称**、**当前代码**、**父代码**和**存疑**。其中，前四种选择器统称为**普通**选择器。各选择器的定义如下：

    - **指定名称**选择器的值为文本，选择名称与其值相同的，与当前行的记录距离最小的记录。在其值后接 `(父记录名称)`，可得到额外指定父记录名称的该选择器的变种。
    - **当前名称**选择器的值为 `#`，选择与当前行的记录具有相同名称且距离最小的记录。在其值后接 `(父记录名称)`，同样可得到额外指定父记录名称的该选择器的变种。
    - **当前代码**选择器的值为 `.`，选择与当前行的记录具有相同代码的记录。
    - **父代码**选择器的值为 `..`，选择与当前行的记录的父记录具有相同代码的记录。
    - **存疑**选择器的值由一普通选择器的值后接一**存疑标志**组成。存疑标志的可用值为 `?` 和 `!`，其中 `?` 指示该选择器无效并将其禁用，一般用于找不到相关记录时；而 `!` 指示该选择器有效，其选择的记录与内含的普通选择器相同，一般用于能找到相关记录却没有官方解释，但有充分的理由启用该选择器时。

    两条记录间距离的可能取值为：

    - 1（代码的前四位数字相同，且父记录的名称相同）
    - 2（不符合距离为 1 的条件，且代码的前两位数字相同）
    - 3（代码的前两位数字不同）

    变更表中每一有效的记录选择器选择的记录必须唯一。遵守本规范的实现，在某一有效的选择器选择的记录不唯一时，应当终止运行并提醒用户通知变更表的维护者。

1. 删除（增加）行的属性，若该行的记录无子记录，则**直接**选择目标（源）表中所有**对应的叶区域**与该行的记录对应的区域相交的记录；若该行的记录有子记录，则**按以下优先顺序依次**选择目标（源）表中所有**对应的区域**与该行的记录对应的区域相交的记录，直至该行的记录对应的区域被已选择的记录对应的区域完全覆盖为止。

    1. 与该行的记录同级。
    1. 为三级记录且这样的记录不多于 3 个。
    1. 为一级记录。

    通过改变转移行的首字符，可得到与之对应的删除行或增加行。转移行的属性所选择的记录，是从其对应的删除行或增加行的属性所选择的记录中去除该行自身的记录后的结果。

## 注意事项

1. 在将区划变更情况的文字描述录入差异表时，需要额外考虑转移的存在，这可以通过在页面中搜索关键词“划归”、“划入”、“并入”并手动筛选得到。每一年的转移行录入完毕后，应当按年份记录在 [diff-notes.md](diff-notes.md) 中，若没有则记录“无”。

## 例子

- 撤销崇川区、港闸区，设立新的南通市崇川区，以原崇川区、港闸区的行政区域为新的崇川区的行政区域。

    ```diff
    -320602	崇川区>#
    -320611	港闸区>崇川区
    +320613	崇川区<#,港闸区
    ```

- 邢台市桥西区更名为信都区。

    ```diff
    -130503	桥西区>.
    +130503	信都区<.
    ```

- 将磁县高臾镇、光禄镇、辛庄营乡、花官营乡、台城乡划归邯郸市邯山区管辖，将磁县林坛镇、南城乡划归邯郸市复兴区管辖。

    将巴音郭楞蒙古自治州和静县、焉耆回族自治县、博湖县、和硕县、若羌县、且末县的部分区域划归铁门关市管辖。

    ```diff
    =130427	磁县>邯山区,复兴区
    =659006	铁门关市<和静县,焉耆回族自治县,博湖县,和硕县,若羌县,且末县
    ```

- 更多的例子：参见已完成的差异表（尤其是 2004-2005 年）和相应的文字描述。
