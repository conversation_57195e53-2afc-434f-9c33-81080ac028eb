This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023) (preloaded format=pdflatex 2023.7.31)  3 JUN 2025 13:55
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**README.md
(./README.md
LaTeX2e <2022-11-01> patch level 1
L3 programming layer <2023-02-22>
./README.md:1: You can't use `macro parameter character #' in vertical mode.
l.1 #
      areacodes
? 
./README.md:1: Emergency stop.
l.1 
    
End of file on the terminal!

 
Here is how much of TeX's memory you used:
 17 strings out of 476025
 380 string characters out of 5790017
 1849388 words of memory out of 5000000
 20569 multiletter control sequences out of 15000+600000
 512287 words of font info for 32 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 13i,0n,12p,52b,10s stack positions out of 10000i,1000n,20000p,200000b,200000s
./README.md:1:  ==> Fatal error occurred, no output PDF file produced!
