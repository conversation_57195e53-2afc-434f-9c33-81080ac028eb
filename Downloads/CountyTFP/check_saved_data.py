# ==============================================
# 检查 quick_save_all 保存的数据
# ==============================================

import pandas as pd
import geopandas as gpd
import pickle
import os

print("=== 检查 quick_save_all 保存的数据 ===")

# 1. 检查保存的文件
shared_data_path = "notebooks/shared_data"
if os.path.exists(shared_data_path):
    print(f"1. 检查 {shared_data_path} 目录中的文件:")
    files = os.listdir(shared_data_path)
    for f in sorted(files):
        file_path = os.path.join(shared_data_path, f)
        if os.path.isfile(file_path):
            file_size = os.path.getsize(file_path)
            print(f"   {f}: {file_size/1024:.2f} KB")
else:
    print(f"❌ {shared_data_path} 目录不存在")

# 2. 检查 filtered_county_gdf.geojson
filtered_file = os.path.join(shared_data_path, "filtered_county_gdf.geojson")
if os.path.exists(filtered_file):
    print(f"\n2. 检查保存的 filtered_county_gdf.geojson:")
    try:
        saved_filtered = gpd.read_file(filtered_file)
        print(f"   保存的记录数: {len(saved_filtered)}")
        print(f"   保存的列: {list(saved_filtered.columns)}")
        
        # 检查是否有data_source列
        if 'data_source' in saved_filtered.columns:
            print(f"   ⚠️  保存的数据包含 'data_source' 列!")
            print(f"   data_source 分布:")
            print(saved_filtered['data_source'].value_counts().to_dict())
            
            # 如果有data_source列，统计original_filtered的数量
            if 'original_filtered' in saved_filtered['data_source'].values:
                original_count = len(saved_filtered[saved_filtered['data_source'] == 'original_filtered'])
                print(f"   原始过滤数据: {original_count} 条")
        else:
            print(f"   ✅ 保存的数据没有 'data_source' 列，应该是原始版本")
            
    except Exception as e:
        print(f"   ❌ 读取保存文件失败: {e}")
else:
    print(f"\n2. ❌ filtered_county_gdf.geojson 文件不存在")

# 3. 检查 pickle 文件
pickle_files = [f for f in os.listdir(shared_data_path) if f.endswith('.pkl')]
if pickle_files:
    print(f"\n3. 检查 pickle 文件:")
    for pkl_file in pickle_files:
        pkl_path = os.path.join(shared_data_path, pkl_file)
        try:
            with open(pkl_path, 'rb') as f:
                data = pickle.load(f)
            
            print(f"   {pkl_file}:")
            if hasattr(data, '__len__') and hasattr(data, 'columns'):
                print(f"     类型: {type(data).__name__}")
                print(f"     记录数: {len(data)}")
                if 'data_source' in data.columns:
                    print(f"     ⚠️  包含 'data_source' 列")
                    print(f"     data_source 分布: {data['data_source'].value_counts().to_dict()}")
                else:
                    print(f"     ✅ 没有 'data_source' 列")
            else:
                print(f"     类型: {type(data).__name__}")
                if isinstance(data, (list, dict)):
                    print(f"     长度: {len(data)}")
                    
        except Exception as e:
            print(f"   ❌ 读取 {pkl_file} 失败: {e}")
else:
    print(f"\n3. 没有找到 .pkl 文件")

# 4. 分析问题并提供解决方案
print(f"\n4. 问题分析和解决方案:")

# 检查当前内存中的变量
current_filtered_len = len(filtered_county_gdf) if 'filtered_county_gdf' in globals() else 0
has_data_source = 'data_source' in filtered_county_gdf.columns if 'filtered_county_gdf' in globals() else False

print(f"   当前内存中 filtered_county_gdf: {current_filtered_len} 条记录")
print(f"   是否包含 data_source 列: {has_data_source}")

if current_filtered_len > 1600:  # 超过预期的1570
    print(f"\n⚠️  问题确认: filtered_county_gdf 被错误修改!")
    print(f"   预期记录数: ~1570")
    print(f"   当前记录数: {current_filtered_len}")
    
    print(f"\n📋 解决方案:")
    
    # 方案1: 如果保存的文件是正确的
    if os.path.exists(filtered_file):
        try:
            saved_filtered = gpd.read_file(filtered_file)
            if len(saved_filtered) < current_filtered_len and 'data_source' not in saved_filtered.columns:
                print(f"   ✅ 方案1: 从保存文件恢复原始数据")
                print(f"   ```python")
                print(f"   filtered_county_gdf = gpd.read_file('{filtered_file}')")
                print(f"   print(f'恢复后: {{len(filtered_county_gdf)}} 条记录')")
                print(f"   ```")
            else:
                print(f"   ❌ 保存的文件也是错误版本")
        except:
            pass
    
    # 方案2: 从当前数据中提取原始部分
    if has_data_source:
        print(f"   ✅ 方案2: 从当前数据中提取原始部分")
        print(f"   ```python")
        print(f"   # 提取原始过滤数据")
        print(f"   filtered_county_gdf_original = filtered_county_gdf[")
        print(f"       filtered_county_gdf['data_source'] == 'original_filtered'")
        print(f"   ].drop(columns=['data_source']).copy()")
        print(f"   ")
        print(f"   # 重新赋值")
        print(f"   filtered_county_gdf = filtered_county_gdf_original")
        print(f"   print(f'恢复后: {{len(filtered_county_gdf)}} 条记录')")
        print(f"   ```")
    
    # 方案3: 重新执行过滤操作
    print(f"   ✅ 方案3: 重新执行过滤操作")
    print(f"   ```python")
    print(f"   # 重新执行市辖区过滤")
    print(f"   filtered_county_gdf, removed_districts_info = remove_districts_within_city_centers(")
    print(f"       county_gdf, city_gdf)")
    print(f"   print(f'重新过滤后: {{len(filtered_county_gdf)}} 条记录')")
    print(f"   ```")
    
    # 方案4: 重新保存正确版本
    print(f"   ✅ 方案4: 恢复后重新保存")
    print(f"   ```python")
    print(f"   # 恢复数据后，重新保存正确版本")
    print(f"   filtered_county_gdf.to_file('{filtered_file}', driver='GeoJSON')")
    print(f"   ```")

else:
    print(f"   ✅ 数据数量正常")

print(f"\n=== 检查完成 ===") 