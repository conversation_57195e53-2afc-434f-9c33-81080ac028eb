#%% md
# 县级全要素生产率分析：主要发现

本笔记本汇总了项目的主要分析发现，展示关键结果和可视化。
#%%
# 导入必要的库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
import sys
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# 添加项目根目录到路径
sys.path.append('..')

# 设置可视化样式
plt.style.use('seaborn-v0_8-whitegrid')
sns.set(font_scale=1.2)
%matplotlib inline
#%% md
## 1. 加载分析结果
#%%
# 加载模型比较结果
model_comparison = pd.read_csv('../results/tables/model_comparison.csv')
model_comparison
#%%
# 加载特征重要性
feature_importance = pd.read_csv('../results/tables/random_forest_feature_importance.csv')
feature_importance.head(10)
#%%
# 加载OLS回归系数
ols_coefficients = pd.read_csv('../results/tables/ols_coefficients.csv')
ols_coefficients[ols_coefficients['significant']].head(10)
#%% md
## 2. 主要发现可视化
#%%
# 模型性能比较
plt.figure(figsize=(12, 6))
sns.barplot(x='model', y='test_r2', data=model_comparison)
plt.title('模型比较: 测试集 R²')
plt.ylim(0, 1)
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()
#%%
# 特征重要性可视化
plt.figure(figsize=(12, 8))
sns.barplot(x='importance', y='feature', data=feature_importance.head(15))
plt.title('随机森林模型: 特征重要性 (Top 15)')
plt.tight_layout()
plt.show()
#%%
# 显著回归系数可视化
significant_coef = ols_coefficients[ols_coefficients['significant']].copy()
significant_coef = significant_coef[significant_coef['variable'] != 'const']
significant_coef = significant_coef.sort_values('coefficient', ascending=False)

plt.figure(figsize=(12, 8))
sns.barplot(x='coefficient', y='variable', data=significant_coef)
plt.title('OLS回归: 显著变量系数')
plt.axvline(x=0, color='r', linestyle='--')
plt.tight_layout()
plt.show()
#%% md
## 3. 银行服务可及性分析
#%%
# 加载银行服务相关图表
from IPython.display import Image, display

# 显示银行网点密度地图
try:
    display(Image('../results/figures/bank_density_features_bank_density_per_sqkm_choropleth.png'))
except:
    print("银行网点密度地图文件不存在")
#%%
# 显示银行服务与生产率关系图
try:
    display(Image('../results/figures/financial_inclusion_features_banks_per_10k_people_scatter.png'))
except:
    print("银行服务与生产率关系图文件不存在")
#%% md
## 4. 农业生产效率分析
#%%
# 显示农业效率地图
try:
    display(Image('../results/figures/agriculture_features_simple_tfp_choropleth.png'))
except:
    print("农业效率地图文件不存在")
#%%
# 显示季节性变化图
try:
    display(Image('../results/figures/agriculture_features_seasonal_ratio_time_series.png'))
except:
    print("季节性变化图文件不存在")
#%% md
## 5. 地理因素影响分析
#%%
# 显示空间聚类地图
try:
    display(Image('../results/figures/terrain_features_spatial_cluster.png'))
except:
    print("空间聚类地图文件不存在")
#%%
# 显示地形特征与生产率关系图
try:
    display(Image('../results/figures/terrain_features_area_sqkm_scatter.png'))
except:
    print("地形特征与生产率关系图文件不存在")
#%% md
## 6. 综合生产率模型
#%%
# 显示实际值vs预测值图
try:
    display(Image('../results/figures/random_forest_actual_vs_predicted.png'))
except:
    print("实际值vs预测值图文件不存在")
#%% md
## 7. 主要结论

根据我们的分析，得出以下主要结论：

1. **银行服务可及性影响**
   - 银行网点密度与县级全要素生产率呈正相关
   - 每10,000人拥有的银行网点数是预测生产率的重要指标
   - 金融深度（贷款额/GDP比率）对生产率有显著影响

2. **农业生产效率发现**
   - 机械化水平是影响农业生产效率的关键因素
   - 季节性变化对农业生产率有明显影响
   - 化肥使用强度与产出呈非线性关系

3. **地理因素影响**
   - 县域面积与生产率存在一定关系
   - 空间聚类分析显示生产率具有明显的地理集聚特征
   - 地形复杂度对生产效率有负面影响

4. **综合模型表现**
   - 随机森林模型在预测全要素生产率方面表现最佳
   - 模型解释力达到了XX%（R²值）
   - 最重要的预测因素包括：[列出3-5个最重要因素]
#%% md
## 8. 政策建议

基于我们的研究发现，提出以下政策建议：

1. **金融服务改善**
   - 增加农村地区银行网点覆盖
   - 发展普惠金融服务，提高贷款可得性
   - 针对生产效率低的地区提供定向金融支持

2. **农业现代化**
   - 加大农业机械化投入
   - 优化化肥使用，推广科学施肥
   - 发展季节性农业规划，减少季节性波动

3. **区域协调发展**
   - 针对地理条件不利地区制定差异化支持政策
   - 促进区域间资源优化配置
   - 加强空间集聚区域的产业链协同
#%% md
## 9. 未来研究方向

本研究还可以在以下方向进一步拓展：

1. 纳入更多时间维度数据，进行面板数据分析
2. 探索空间计量经济学模型，更好地捕捉空间溢出效应
3. 引入更多微观层面数据，如企业层面的生产率数据
4. 研究政策干预的因果效应
5. 开发更精细的地区分类方法，提高政策针对性