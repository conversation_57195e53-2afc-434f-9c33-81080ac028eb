#%% md
# 数据清洗与预处理

本notebook用于对数据进行清洗和预处理，包括处理缺失值、异常值，以及进行必要的数据转换。
#%%
# 导入必要的库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
import sys

# 添加项目根目录到路径，以便导入自定义模块
sys.path.append('..')

# 导入自定义模块
from src.data.load_data import load_io_data, load_psbc_data, load_result_data
from src.data.process_data import clean_data, handle_missing_values, normalize_data
from src.utils.helpers import save_dataframe, detect_outliers

# 设置可视化样式
plt.style.use('seaborn-v0_8-whitegrid')
sns.set(font_scale=1.2)
%matplotlib inline
#%% md
## 加载数据
#%%
# 加载IO.dta数据
io_data = load_io_data()
print("IO数据加载完成，形状:", io_data.shape)

# 加载PSBC.xlsx数据
psbc_data = load_psbc_data()
print("PSBC数据加载完成，形状:", psbc_data.shape)

# 加载result.csv数据
result_data = load_result_data()
print("Result数据加载完成，形状:", result_data.shape)
#%% md
## 基本数据清洗
#%%
# 清洗IO数据
io_clean = clean_data(io_data, drop_na=False)
print("IO数据清洗完成，形状:", io_clean.shape)
print(f"删除了 {io_data.shape[0] - io_clean.shape[0]} 行重复数据")
#%%
# 清洗PSBC数据
psbc_clean = clean_data(psbc_data, drop_na=False)
print("PSBC数据清洗完成，形状:", psbc_clean.shape)
print(f"删除了 {psbc_data.shape[0] - psbc_clean.shape[0]} 行重复数据")
#%%
# 清洗Result数据
result_clean = clean_data(result_data, drop_na=False)
print("Result数据清洗完成，形状:", result_clean.shape)
print(f"删除了 {result_data.shape[0] - result_clean.shape[0]} 行重复数据")
#%% md
## 处理缺失值
#%%
# 检查IO数据的缺失值
io_missing = io_clean.isnull().sum()
io_missing = io_missing[io_missing > 0]
if len(io_missing) > 0:
    print("IO数据缺失值:")
    print(io_missing)
    
    # 处理缺失值
    io_filled = handle_missing_values(io_clean, strategy='median')
    print("\nIO数据缺失值处理后:")
    print(io_filled.isnull().sum().sum(), "个缺失值")
else:
    print("IO数据没有缺失值")
    io_filled = io_clean
#%%
# 检查PSBC数据的缺失值
psbc_missing = psbc_clean.isnull().sum()
psbc_missing = psbc_missing[psbc_missing > 0]
if len(psbc_missing) > 0:
    print("PSBC数据缺失值:")
    print(psbc_missing)
    
    # 处理缺失值
    psbc_filled = handle_missing_values(psbc_clean, strategy='median')
    print("\nPSBC数据缺失值处理后:")
    print(psbc_filled.isnull().sum().sum(), "个缺失值")
else:
    print("PSBC数据没有缺失值")
    psbc_filled = psbc_clean
#%%
# 检查Result数据的缺失值
result_missing = result_clean.isnull().sum()
result_missing = result_missing[result_missing > 0]
if len(result_missing) > 0:
    print("Result数据缺失值:")
    print(result_missing)
    
    # 处理缺失值
    result_filled = handle_missing_values(result_clean, strategy='median')
    print("\nResult数据缺失值处理后:")
    print(result_filled.isnull().sum().sum(), "个缺失值")
else:
    print("Result数据没有缺失值")
    result_filled = result_clean
#%% md
## 处理异常值
#%%
# 这里添加异常值检测和处理代码
# 以下代码仅为示例，需要根据实际数据调整

# 示例：检测IO数据中数值列的异常值
numeric_cols = io_filled.select_dtypes(include=['number']).columns

# 选择前几个数值列作为示例
sample_cols = numeric_cols[:3]

for col in sample_cols:
    # 使用IQR方法检测异常值
    outliers = detect_outliers(io_filled, col, method='iqr', threshold=1.5)
    outlier_count = outliers.sum()
    
    print(f"列 '{col}' 中检测到 {outlier_count} 个异常值 ({outlier_count/len(io_filled)*100:.2f}%)")
    
    # 可视化异常值
    plt.figure(figsize=(10, 6))
    plt.boxplot(io_filled[col].dropna())
    plt.title(f"'{col}' 的箱线图")
    plt.grid(True, alpha=0.3)
    plt.show()
    
    # 处理异常值（示例：将异常值替换为上下限）
    if outlier_count > 0:
        Q1 = io_filled[col].quantile(0.25)
        Q3 = io_filled[col].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        # 替换异常值
        io_filled.loc[io_filled[col] < lower_bound, col] = lower_bound
        io_filled.loc[io_filled[col] > upper_bound, col] = upper_bound
        
        print(f"已将 '{col}' 中的异常值替换为上下限值")
        
        # 验证处理后的结果
        outliers_after = detect_outliers(io_filled, col, method='iqr', threshold=1.5)
        print(f"处理后，列 '{col}' 中还有 {outliers_after.sum()} 个异常值\n")
#%% md
## 数据标准化/归一化
#%%
# 标准化IO数据中的数值列
io_numeric_cols = io_filled.select_dtypes(include=['number']).columns
io_normalized = normalize_data(io_filled, method='standard', columns=io_numeric_cols)

# 查看标准化前后的对比
sample_col = io_numeric_cols[0]  # 选择第一个数值列作为示例

fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))

# 标准化前
sns.histplot(io_filled[sample_col].dropna(), kde=True, ax=ax1)
ax1.set_title(f'{sample_col} - 标准化前')
ax1.set_xlabel(sample_col)
ax1.set_ylabel('频数')

# 标准化后
sns.histplot(io_normalized[sample_col].dropna(), kde=True, ax=ax2)
ax2.set_title(f'{sample_col} - 标准化后')
ax2.set_xlabel(f'{sample_col} (标准化)')
ax2.set_ylabel('频数')

plt.tight_layout()
plt.show()
#%% md
## 保存处理后的数据
#%%
# 保存处理后的IO数据
save_dataframe(io_filled, 'io_cleaned', directory='../data/processed', format='csv')
save_dataframe(io_normalized, 'io_normalized', directory='../data/processed', format='csv')

# 保存处理后的PSBC数据
save_dataframe(psbc_filled, 'psbc_cleaned', directory='../data/processed', format='csv')

# 保存处理后的Result数据
save_dataframe(result_filled, 'result_cleaned', directory='../data/processed', format='csv')
#%% md
## 数据清洗总结
#%% md
在这里总结数据清洗和预处理的主要步骤和结果：

1. 基本清洗：删除重复行
2. 缺失值处理：使用中位数填充
3. 异常值处理：使用IQR方法检测并替换异常值
4. 数据标准化：对数值列进行标准化处理

处理后的数据已保存到processed目录，可用于后续分析和建模。