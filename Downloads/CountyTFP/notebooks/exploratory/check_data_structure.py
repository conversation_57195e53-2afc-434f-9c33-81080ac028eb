#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查IO.dta和county_tfp_0329.dta数据结构
确认ID和时间变量，评估merge可能性
"""

import pandas as pd
import numpy as np

def analyze_dta_file(file_path, file_name):
    """分析.dta文件的数据结构"""
    print(f"\n{'='*80}")
    print(f"分析文件: {file_name}")
    print(f"路径: {file_path}")
    print(f"{'='*80}")
    
    try:
        # 读取.dta文件
        df = pd.read_stata(file_path)
        print(f"✓ 成功读取文件")
        
        # 基本信息
        print(f"\n【基本信息】")
        print(f"  数据行数: {len(df):,}")
        print(f"  列数: {len(df.columns)}")
        print(f"  内存使用: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
        
        # 列信息
        print(f"\n【列信息】")
        print(f"  列名: {list(df.columns)}")
        
        # 数据类型
        print(f"\n【数据类型】")
        for col in df.columns:
            print(f"  {col}: {df[col].dtype}")
        
        # 寻找潜在的ID列
        print(f"\n【潜在ID列分析】")
        potential_id_cols = []
        for col in df.columns:
            if any(keyword in col.lower() for keyword in ['id', 'code', 'county', 'city', 'region']):
                potential_id_cols.append(col)
                unique_count = df[col].nunique()
                total_count = len(df)
                print(f"  {col}: {unique_count:,} 个唯一值 / {total_count:,} 总行数 (比例: {unique_count/total_count:.3f})")
                print(f"    样本值: {df[col].head().tolist()}")
                print(f"    缺失值: {df[col].isnull().sum()}")
        
        # 寻找潜在的时间列
        print(f"\n【潜在时间列分析】")
        potential_time_cols = []
        for col in df.columns:
            if any(keyword in col.lower() for keyword in ['year', 'time', 'date', 'period']):
                potential_time_cols.append(col)
                unique_vals = sorted(df[col].dropna().unique())
                print(f"  {col}: {len(unique_vals)} 个唯一值")
                print(f"    值范围: {unique_vals}")
                print(f"    缺失值: {df[col].isnull().sum()}")
        
        # 检查panel数据结构 (如果同时有ID和时间列)
        if potential_id_cols and potential_time_cols:
            print(f"\n【Panel数据结构检查】")
            for id_col in potential_id_cols:
                for time_col in potential_time_cols:
                    # 检查ID-时间组合的唯一性
                    combo_unique = df.groupby([id_col, time_col]).size()
                    duplicates = combo_unique[combo_unique > 1]
                    
                    print(f"  ID列({id_col}) × 时间列({time_col}):")
                    print(f"    组合总数: {len(combo_unique):,}")
                    print(f"    重复组合: {len(duplicates)}")
                    if len(duplicates) > 0:
                        print(f"    重复示例: {duplicates.head()}")
        
        # 数据预览
        print(f"\n【数据预览】")
        print(df.head())
        
        return df, potential_id_cols, potential_time_cols
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return None, [], []

# 分析两个文件
print("开始分析数据文件...")

# 文件1: IO.dta
io_df, io_id_cols, io_time_cols = analyze_dta_file(
    '/Users/<USER>/Downloads/CountyTFP/data/raw/IO.dta', 
    'IO.dta'
)

# 文件2: county_tfp_0329.dta  
tfp_df, tfp_id_cols, tfp_time_cols = analyze_dta_file(
    '/Users/<USER>/Downloads/CountyTFP/data/raw/county_tfp_0329.dta', 
    'county_tfp_0329.dta'
)

# 总结分析
print(f"\n{'='*80}")
print("总结分析")
print(f"{'='*80}")

if io_df is not None and tfp_df is not None:
    print(f"\n【文件对比】")
    print(f"IO.dta: {len(io_df):,} 行, {len(io_df.columns)} 列")
    print(f"county_tfp_0329.dta: {len(tfp_df):,} 行, {len(tfp_df.columns)} 列")
    
    print(f"\n【ID列对比】")
    print(f"IO.dta 潜在ID列: {io_id_cols}")
    print(f"county_tfp_0329.dta 潜在ID列: {tfp_id_cols}")
    
    print(f"\n【时间列对比】")
    print(f"IO.dta 潜在时间列: {io_time_cols}")
    print(f"county_tfp_0329.dta 潜在时间列: {tfp_time_cols}")
    
    # 检查是否可以merge
    print(f"\n【Merge可行性评估】")
    if io_id_cols and tfp_id_cols and io_time_cols and tfp_time_cols:
        print("✓ 两个文件都具备ID和时间变量，理论上可以进行merge")
        
        # 检查具体的overlap
        for io_id in io_id_cols:
            for tfp_id in tfp_id_cols:
                io_ids = set(io_df[io_id].dropna().astype(str))
                tfp_ids = set(tfp_df[tfp_id].dropna().astype(str))
                overlap = len(io_ids & tfp_ids)
                print(f"  {io_id} vs {tfp_id}: {overlap} 个共同ID")
                
        for io_time in io_time_cols:
            for tfp_time in tfp_time_cols:
                io_times = set(io_df[io_time].dropna())
                tfp_times = set(tfp_df[tfp_time].dropna())
                overlap = len(io_times & tfp_times)
                print(f"  {io_time} vs {tfp_time}: {overlap} 个共同时间点")
    else:
        print("❌ 缺少必要的ID或时间变量，无法进行标准merge") 