#%% md
# 数据可视化

本notebook用于创建各种数据可视化，以便更好地理解数据特征和关系。
#%%
# 导入必要的库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import os
import sys

# 添加项目根目录到路径，以便导入自定义模块
sys.path.append('..')

# 导入自定义模块
from src.visualization.visualize import (
    plot_distribution, plot_correlation_matrix, plot_scatter, 
    plot_time_series, create_interactive_scatter, save_figure
)

# 设置可视化样式
plt.style.use('seaborn-v0_8-whitegrid')
sns.set(font_scale=1.2)
%matplotlib inline
#%% md
## 加载处理后的数据
#%%
# 加载处理后的数据
io_data = pd.read_csv('../data/processed/io_cleaned.csv')
io_norm = pd.read_csv('../data/processed/io_normalized.csv')
psbc_data = pd.read_csv('../data/processed/psbc_cleaned.csv')
result_data = pd.read_csv('../data/processed/result_cleaned.csv')

print("数据加载完成:")
print(f"IO数据: {io_data.shape}")
print(f"标准化IO数据: {io_norm.shape}")
print(f"PSBC数据: {psbc_data.shape}")
print(f"Result数据: {result_data.shape}")
#%% md
## 单变量分布可视化
#%%
# 选择IO数据中的几个重要数值列进行分布可视化
# 注意：需要根据实际数据调整列名
numeric_cols = io_data.select_dtypes(include=['number']).columns[:5]  # 选择前5个数值列作为示例

for col in numeric_cols:
    fig = plot_distribution(io_data, col, save=True, filename=f"io_{col}_distribution")
    plt.show()
#%%
# 选择PSBC数据中的几个重要数值列进行分布可视化
# 注意：需要根据实际数据调整列名
numeric_cols = psbc_data.select_dtypes(include=['number']).columns[:5]  # 选择前5个数值列作为示例

for col in numeric_cols:
    fig = plot_distribution(psbc_data, col, save=True, filename=f"psbc_{col}_distribution")
    plt.show()
#%%
# 选择Result数据中的几个重要数值列进行分布可视化
# 注意：需要根据实际数据调整列名
numeric_cols = result_data.select_dtypes(include=['number']).columns[:5]  # 选择前5个数值列作为示例

for col in numeric_cols:
    fig = plot_distribution(result_data, col, save=True, filename=f"result_{col}_distribution")
    plt.show()
#%% md
## 相关性分析
#%%
# IO数据相关性矩阵
io_numeric = io_data.select_dtypes(include=['number'])
# 如果列太多，可以选择部分重要列
if io_numeric.shape[1] > 15:
    io_numeric = io_numeric.iloc[:, :15]  # 选择前15列作为示例

fig = plot_correlation_matrix(io_numeric, method='pearson', figsize=(12, 10), 
                             save=True, filename="io_correlation_matrix")
plt.show()
#%%
# PSBC数据相关性矩阵
psbc_numeric = psbc_data.select_dtypes(include=['number'])
# 如果列太多，可以选择部分重要列
if psbc_numeric.shape[1] > 15:
    psbc_numeric = psbc_numeric.iloc[:, :15]  # 选择前15列作为示例

fig = plot_correlation_matrix(psbc_numeric, method='pearson', figsize=(12, 10), 
                             save=True, filename="psbc_correlation_matrix")
plt.show()
#%%
# Result数据相关性矩阵
result_numeric = result_data.select_dtypes(include=['number'])
# 如果列太多，可以选择部分重要列
if result_numeric.shape[1] > 15:
    result_numeric = result_numeric.iloc[:, :15]  # 选择前15列作为示例

fig = plot_correlation_matrix(result_numeric, method='pearson', figsize=(12, 10), 
                             save=True, filename="result_correlation_matrix")
plt.show()
#%% md
## 散点图分析
#%%
# 选择IO数据中的两个相关性较高的变量绘制散点图
# 注意：需要根据实际数据和相关性分析结果调整列名
# 这里假设x_col和y_col是两个相关性较高的变量
io_corr = io_numeric.corr()
# 获取相关系数绝对值最大的列对（排除自身相关）
corr_pairs = []
for i in range(len(io_corr.columns)):
    for j in range(i+1, len(io_corr.columns)):
        corr_pairs.append((io_corr.columns[i], io_corr.columns[j], abs(io_corr.iloc[i, j])))

# 按相关系数绝对值排序
corr_pairs.sort(key=lambda x: x[2], reverse=True)

# 选择相关性最高的前3对变量
for i in range(min(3, len(corr_pairs))):
    x_col, y_col, corr_val = corr_pairs[i]
    print(f"变量 {x_col} 和 {y_col} 的相关系数: {corr_val:.4f}")
    
    fig = plot_scatter(io_data, x_col, y_col, 
                      title=f"{y_col} vs {x_col} (相关系数: {corr_val:.4f})", 
                      save=True, filename=f"io_scatter_{x_col}_{y_col}")
    plt.show()
#%% md
## 交互式可视化
#%%
# 创建交互式散点图
# 注意：需要根据实际数据调整列名
# 这里假设x_col, y_col, color_col是三个有意义的变量
if len(corr_pairs) > 0:
    x_col, y_col, _ = corr_pairs[0]
    
    # 选择一个分类变量作为颜色编码（如果有的话）
    categorical_cols = io_data.select_dtypes(include=['object', 'category']).columns
    color_col = categorical_cols[0] if len(categorical_cols) > 0 else None
    
    fig = create_interactive_scatter(
        io_data, x_col, y_col, color=color_col,
        title=f"交互式散点图: {y_col} vs {x_col}",
        labels={x_col: x_col, y_col: y_col}
    )
    
    fig.show()
#%% md
## 多变量可视化
#%%
# 创建成对关系图
# 选择IO数据中的几个重要变量
# 注意：需要根据实际数据调整列名
if len(io_numeric.columns) >= 4:
    selected_cols = list(io_numeric.columns[:4])  # 选择前4个数值列作为示例
    
    plt.figure(figsize=(12, 10))
    sns.pairplot(io_data[selected_cols])
    plt.suptitle("IO数据成对关系图", y=1.02, fontsize=16)
    plt.tight_layout()
    plt.savefig("../results/figures/io_pairplot.png", dpi=300, bbox_inches='tight')
    plt.show()
#%% md
## 分组分析可视化
#%%
# 如果数据中有分类变量，可以进行分组分析
# 注意：需要根据实际数据调整列名
categorical_cols = io_data.select_dtypes(include=['object', 'category']).columns

if len(categorical_cols) > 0 and len(io_numeric.columns) > 0:
    cat_col = categorical_cols[0]  # 选择第一个分类列作为示例
    num_col = io_numeric.columns[0]  # 选择第一个数值列作为示例
    
    # 检查唯一值数量，如果太多则跳过
    if io_data[cat_col].nunique() <= 10:
        plt.figure(figsize=(12, 6))
        sns.boxplot(x=cat_col, y=num_col, data=io_data)
        plt.title(f"{num_col} 按 {cat_col} 分组的箱线图", fontsize=15)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(f"../results/figures/io_boxplot_{cat_col}_{num_col}.png", dpi=300, bbox_inches='tight')
        plt.show()
        
        # 小提琴图
        plt.figure(figsize=(12, 6))
        sns.violinplot(x=cat_col, y=num_col, data=io_data)
        plt.title(f"{num_col} 按 {cat_col} 分组的小提琴图", fontsize=15)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(f"../results/figures/io_violinplot_{cat_col}_{num_col}.png", dpi=300, bbox_inches='tight')
        plt.show()
#%% md
## 时间序列可视化（如果适用）
#%%
# 如果数据中有时间变量，可以创建时间序列图
# 注意：需要根据实际数据调整列名和日期格式

# 示例：假设result_data中有一个名为'date'的日期列和一个名为'value'的数值列
# 实际使用时需要根据数据调整

# 检查是否有日期列（这里简单地检查列名中是否包含'date', 'time', 'year'等关键词）
date_cols = [col for col in result_data.columns if any(keyword in col.lower() for keyword in ['date', 'time', 'year', 'month', 'day'])]

if date_cols and len(result_numeric.columns) > 0:
    date_col = date_cols[0]  # 选择第一个日期列
    value_col = result_numeric.columns[0]  # 选择第一个数值列
    
    # 尝试将日期列转换为datetime格式（如果尚未转换）
    try:
        result_data[date_col] = pd.to_datetime(result_data[date_col])
        
        # 创建时间序列图
        fig = plot_time_series(result_data.sort_values(date_col), date_col, value_col, 
                              title=f"{value_col}随时间的变化", 
                              save=True, filename=f"result_timeseries_{value_col}")
        plt.show()
    except:
        print(f"无法将'{date_col}'列转换为日期格式")
#%% md
## 可视化总结
#%% md
在这里总结数据可视化的主要发现：

1. 变量分布特点
2. 变量之间的相关性
3. 分组分析结果
4. 时间序列模式（如果适用）
5. 其他重要发现

所有可视化结果已保存到results/figures目录，可用于报告和演示。