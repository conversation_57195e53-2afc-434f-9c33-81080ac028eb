#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
追踪特定县代码的完整历史演化链条
目标ID: 441727, 441729, 441730
"""

import networkx as nx
from collections import defaultdict

def find_complete_lineage_for_code(graph, target_code):
    """
    找到指定代码的完整演化链条
    返回: 按年份排序的完整链条节点列表
    """
    # 1. 找到该代码在图中的所有历史节点
    historical_nodes = [node for node in graph.nodes() if node[0] == target_code]
    
    if not historical_nodes:
        return [], f"代码 {target_code} 在历史图中未找到"
    
    # 2. 收集所有相关的演化链条节点
    all_lineage_nodes = set()
    
    for hist_node in historical_nodes:
        try:
            # 获取祖先节点（历史上的前身）
            ancestors = nx.ancestors(graph, hist_node)
            # 获取后代节点（历史上的后续）
            descendants = nx.descendants(graph, hist_node)
            # 合并：祖先 + 自身 + 后代
            lineage_nodes = ancestors | {hist_node} | descendants
            all_lineage_nodes.update(lineage_nodes)
            
        except nx.NetworkXError as e:
            print(f"  处理节点 {hist_node} 时发生NetworkX错误: {e}")
            continue
    
    if not all_lineage_nodes:
        return [], f"代码 {target_code} 无法构建演化链条"
    
    # 3. 按年份排序
    sorted_lineage = sorted(list(all_lineage_nodes), key=lambda x: int(x[2]))
    
    return sorted_lineage, "success"

def display_lineage_chain(target_code, lineage_nodes, status_msg):
    """
    格式化显示演化链条
    """
    print(f"\n{'='*80}")
    print(f"目标代码: {target_code}")
    print(f"状态: {status_msg}")
    print(f"{'='*80}")
    
    if not lineage_nodes:
        print("  无演化链条数据")
        return
    
    print(f"完整演化链条 (共 {len(lineage_nodes)} 个节点):")
    print(f"{'序号':<4} {'代码':<8} {'名称':<20} {'年份':<6} {'备注'}")
    print("-" * 80)
    
    for i, (code, name, year) in enumerate(lineage_nodes, 1):
        # 标记目标代码
        remark = "★ 目标代码" if code == target_code else ""
        print(f"{i:<4} {code:<8} {name:<20} {year:<6} {remark}")
    
    # 统计代码分布
    code_years = defaultdict(list)
    for code, name, year in lineage_nodes:
        code_years[code].append((name, year))
    
    print(f"\n代码分布统计 (共 {len(code_years)} 个不同代码):")
    for code, name_year_list in sorted(code_years.items()):
        years = [ny[1] for ny in name_year_list]
        names = list(set(ny[0] for ny in name_year_list))
        print(f"  {code}: {min(years)}-{max(years)} ({len(years)}年), 名称: {', '.join(names)}")

# 主执行部分
target_ids = ['441727', '441729', '441730']

print("开始追踪指定县代码的完整历史演化链条...")
print(f"目标代码: {', '.join(target_ids)}")
print(f"使用历史变迁图: {complete_graph.number_of_nodes()} 节点, {complete_graph.number_of_edges()} 边")

# 逐个处理每个目标ID
for target_id in target_ids:
    lineage_chain, status = find_complete_lineage_for_code(complete_graph, target_id)
    display_lineage_chain(target_id, lineage_chain, status)

print(f"\n{'='*80}")
print("所有目标代码演化链条追踪完成！")
print(f"{'='*80}") 