#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
探索中国市政府和县政府地理位置GIS数据结构
文件: CN_city_2020.shp, CN_county_2020.shp
"""

import geopandas as gpd
import pandas as pd
import numpy as np

def analyze_shapefile_structure(file_path, data_type_name):
    """
    分析shapefile的数据结构
    """
    print(f"\n{'='*80}")
    print(f"分析 {data_type_name} 数据: {file_path}")
    print(f"{'='*80}")
    
    try:
        # 读取shapefile
        gdf = gpd.read_file(file_path)
        print(f"✓ 成功读取shapefile")
        
        # 基本信息
        print(f"\n【基本信息】")
        print(f"  数据行数: {len(gdf)}")
        print(f"  列数: {len(gdf.columns)}")
        print(f"  坐标系统(CRS): {gdf.crs}")
        print(f"  几何类型: {gdf.geometry.geom_type.unique()}")
        
        # 列信息
        print(f"\n【列信息详情】")
        print(f"{'列名':<15} {'数据类型':<12} {'非空值':<8} {'唯一值':<8} {'示例值'}")
        print("-" * 80)
        
        for col in gdf.columns:
            if col != 'geometry':  # 跳过几何列
                dtype = str(gdf[col].dtype)
                non_null = gdf[col].notna().sum()
                unique_count = gdf[col].nunique()
                
                # 获取示例值
                sample_values = gdf[col].dropna().head(3).tolist()
                sample_str = str(sample_values)[:30] + "..." if len(str(sample_values)) > 30 else str(sample_values)
                
                print(f"{col:<15} {dtype:<12} {non_null:<8} {unique_count:<8} {sample_str}")
        
        # 数据样本
        print(f"\n【数据样本 (前5行，除几何列)】")
        display_cols = [col for col in gdf.columns if col != 'geometry']
        if display_cols:
            sample_df = gdf[display_cols].head()
            print(sample_df.to_string())
        
        # 几何信息
        print(f"\n【几何信息】")
        if not gdf.geometry.empty:
            bounds = gdf.total_bounds
            print(f"  边界范围: 经度 [{bounds[0]:.4f}, {bounds[2]:.4f}], 纬度 [{bounds[1]:.4f}, {bounds[3]:.4f}]")
            print(f"  几何对象样本:")
            for i, geom in enumerate(gdf.geometry.head(2)):
                geom_type = geom.geom_type if geom else "None"
                coord_count = len(geom.coords) if hasattr(geom, 'coords') else "N/A"
                print(f"    行{i+1}: {geom_type}, 坐标点数: {coord_count}")
        
        # 特殊列分析（寻找可能的ID列）
        print(f"\n【潜在ID列分析】")
        potential_id_cols = []
        for col in gdf.columns:
            if col != 'geometry':
                col_name_lower = col.lower()
                if any(keyword in col_name_lower for keyword in ['id', 'code', '代码', '编码']):
                    potential_id_cols.append(col)
                    unique_count = gdf[col].nunique()
                    total_count = len(gdf)
                    completeness = gdf[col].notna().sum() / total_count * 100
                    print(f"  {col}: {unique_count} 唯一值 / {total_count} 总行数 (完整度: {completeness:.1f}%)")
                    
                    # 显示该列的值分布样本
                    if gdf[col].dtype == 'object':
                        sample_values = gdf[col].value_counts().head(5)
                        print(f"    值分布样本: {dict(sample_values)}")
                    else:
                        print(f"    数值范围: {gdf[col].min()} - {gdf[col].max()}")
        
        if not potential_id_cols:
            print("  未发现明显的ID相关列")
        
        return gdf
        
    except Exception as e:
        print(f"✗ 读取文件失败: {e}")
        return None

def compare_with_existing_mapping_data(city_gdf, county_gdf):
    """
    与现有的mapping数据进行比较分析
    """
    print(f"\n{'='*80}")
    print("与现有mapping数据的关联分析")
    print(f"{'='*80}")
    
    # 假设gis变量已存在于环境中
    if 'gis' in globals():
        print(f"现有GIS标准数据: {len(gis)} 条记录")
        gis_codes = set(gis['CountyID'].astype(str))
        print(f"GIS标准代码样本: {list(gis_codes)[:10]}")
    else:
        print("未找到现有的GIS标准数据变量")
        return
    
    # 分析city数据中的潜在代码匹配
    if city_gdf is not None:
        print(f"\n【市级数据代码匹配分析】")
        city_potential_codes = set()
        for col in city_gdf.columns:
            if col.lower() in ['code', 'id', '代码', '编码'] or 'code' in col.lower():
                city_codes = set(city_gdf[col].astype(str))
                matches = city_codes & gis_codes
                print(f"  列 '{col}': {len(matches)} 个代码与GIS标准匹配")
                if matches:
                    print(f"    匹配样本: {list(matches)[:5]}")
    
    # 分析county数据中的潜在代码匹配
    if county_gdf is not None:
        print(f"\n【县级数据代码匹配分析】")
        for col in county_gdf.columns:
            if col.lower() in ['code', 'id', '代码', '编码'] or 'code' in col.lower():
                county_codes = set(county_gdf[col].astype(str))
                matches = county_codes & gis_codes
                print(f"  列 '{col}': {len(matches)} 个代码与GIS标准匹配")
                if matches:
                    print(f"    匹配样本: {list(matches)[:5]}")

# 主执行部分
city_file = '/Users/<USER>/Downloads/CountyTFP/data/raw/CN_city_2020/CN_city_2020.shp'
county_file = '/Users/<USER>/Downloads/CountyTFP/data/raw/CN_county_2020/CN_county_2020.shp'

print("开始分析中国市县政府地理位置GIS数据...")

# 分析市级数据
city_gdf = analyze_shapefile_structure(city_file, "市级政府")

# 分析县级数据  
county_gdf = analyze_shapefile_structure(county_file, "县级政府")

# 与现有mapping数据比较
compare_with_existing_mapping_data(city_gdf, county_gdf)

print(f"\n{'='*80}")
print("GIS数据结构分析完成！")
print(f"{'='*80}") 