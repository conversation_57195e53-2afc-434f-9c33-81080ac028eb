import pandas as pd
import numpy as np
import geopandas as gpd
import matplotlib.pyplot as plt
from datetime import datetime
import pickle

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

def load_final_merged_counties(counties_file='/Users/<USER>/Downloads/CountyTFP/notebooks/shared_data/final_merged_counties.csv'):
    """
    加载final merged counties的ID列表
    """
    try:
        df = pd.read_csv(counties_file)
        # 过滤掉台湾省
        df = df[df['ProvinceID'] != '710000']
        county_ids = df['CountyID'].astype(str).tolist()
        print(f"成功加载 {len(county_ids)} 个县ID（已排除台湾）")
        return county_ids
    except Exception as e:
        print(f"加载县数据失败: {e}")
        return None

def analyze_county_branch_diffusion_corrected(
    input_file='/Users/<USER>/Downloads/CountyTFP/data/processed/psbc.geojson',
    branch_types=None,
    target_counties=None,
    analysis_start_year=1997,  # 分析起始年
    analysis_end_year=2015,    # 分析结束年
    data_start_year=1990,      # 数据加载起始年（包含历史）
    county_id_col='GisCountyID',
    institution_code_col='InstitutionCode',
    open_date_col='OpenDate',
    adjust_pre_2008=False
):
    """
    修正版分析函数：正确处理历史数据
    """
    print(f"=== 开始分析：{branch_types if branch_types else '全部网点'} ===")
    
    # 1. 读取数据
    if input_file.endswith('.geojson'):
        df = gpd.read_file(input_file)
    else:
        df = pd.read_csv(input_file)
    
    print(f"原始数据: {len(df)}条记录")
    
    # 2. 数据清洗
    df = df[df[county_id_col].notna()].copy()
    df[county_id_col] = df[county_id_col].astype(str)
    df[open_date_col] = pd.to_datetime(df[open_date_col], errors='coerce')
    df = df[df[open_date_col].notna()].copy()
    
    # 提取开业年份
    df['open_year'] = df[open_date_col].dt.year
    
    # **关键修改：包含历史数据，从更早年份开始加载**
    df = df[(df['open_year'] >= data_start_year) & (df['open_year'] <= analysis_end_year)].copy()
    print(f"年份筛选({data_start_year}-{analysis_end_year}): {len(df)}条记录，{df[county_id_col].nunique()}个县")
    
    # 3. 筛选目标县
    if target_counties is not None:
        target_counties_str = [str(c) for c in target_counties]
        df = df[df[county_id_col].isin(target_counties_str)].copy()
        print(f"目标县筛选: {len(df)}条记录，{df[county_id_col].nunique()}个县")
    
    # 4. 筛选网点类型
    if branch_types is not None:
        df['branch_type_code'] = df[institution_code_col].astype(str).str[5]
        df = df[df['branch_type_code'].isin(branch_types)].copy()
        print(f"网点类型筛选({branch_types}): {len(df)}条记录，{df[county_id_col].nunique()}个县")
    
    # 5. 应用2008年调整
    if adjust_pre_2008 and branch_types == ['S']:
        before_count = len(df[df['open_year'] < 2007])
        df.loc[df['open_year'] < 2007, 'open_year'] = 2008
        print(f"2007年前调整: {before_count}个S类型网点调整到2008年")
    
    if len(df) == 0:
        print("警告: 筛选后无数据!")
        return None, None
    
    # 6. 计算每个县第一次开设网点的年份（包含全部历史）
    county_first_year = df.groupby(county_id_col)['open_year'].min().reset_index()
    county_first_year.columns = ['county_id', 'first_year']
    
    # 7. 计算分析期间的累积分布
    analysis_years = list(range(analysis_start_year, analysis_end_year + 1))
    annual_stats = []
    
    # 计算分析起始年的历史基础
    base_counties = set(county_first_year[county_first_year['first_year'] <= analysis_start_year]['county_id'])
    print(f"{analysis_start_year}年起始时已有网点的县数: {len(base_counties)}")
    
    cumulative_counties = base_counties.copy()
    
    for year in analysis_years:
        # 该年新开设网点的县
        new_counties_this_year = set(county_first_year[county_first_year['first_year'] == year]['county_id'])
        
        # 更新累积县集合
        cumulative_counties.update(new_counties_this_year)
        
        # 计算统计数据
        new_count = len(new_counties_this_year)
        cumulative_count = len(cumulative_counties)
        
        # 正确的分母
        if target_counties is not None:
            total_potential_counties = len(target_counties)
        else:
            total_potential_counties = df[county_id_col].nunique()
            
        cumulative_ratio = cumulative_count / total_potential_counties
        
        annual_stats.append({
            'year': year,
            'new_counties': new_count,
            'cumulative_counties': cumulative_count,
            'cumulative_ratio': cumulative_ratio
        })
        
        if year in [1997, 2007, 2015]:
            print(f"{year}年: 新增{new_count}县, 累积{cumulative_count}县, 覆盖率{cumulative_ratio:.1%}")
    
    annual_cumulative = pd.DataFrame(annual_stats)
    
    return county_first_year, annual_cumulative

def plot_corrected_diffusion_timeline():
    """
    绘制修正后的扩散时间线图
    """
    print("=== 开始重新分析和绘图 ===")
    
    # 加载县数据
    final_counties = load_final_merged_counties()
    if final_counties is None:
        return
    
    # 分析全部网点（修正版）
    print("\n1. 分析全部网点（修正版）")
    county_first_all, annual_cum_all = analyze_county_branch_diffusion_corrected(
        target_counties=final_counties,
        branch_types=None,  # 全部网点
        analysis_start_year=1997,
        analysis_end_year=2015,
        data_start_year=1990,  # 从1990年开始加载历史数据
        adjust_pre_2008=False
    )
    
    # 分析支行网点（修正版）
    print("\n2. 分析支行网点（修正版）")
    county_first_sub, annual_cum_sub = analyze_county_branch_diffusion_corrected(
        target_counties=final_counties,
        branch_types=['S'],  # 只看支行
        analysis_start_year=1997,
        analysis_end_year=2015,
        data_start_year=1990,  # 从1990年开始加载历史数据
        adjust_pre_2008=True   # 应用2007年前调整
    )
    
    if annual_cum_all is None or annual_cum_sub is None:
        print("数据分析失败!")
        return
    
    # 绘制修正后的时间序列图
    print("\n3. 绘制修正后的时间序列图")
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 12))
    
    # 上图：累积覆盖县数
    ax1.plot(annual_cum_all['year'], annual_cum_all['cumulative_counties'], 
             'o-', linewidth=3, markersize=8, label='全部网点覆盖县数', color='#2E86AB')
    ax1.plot(annual_cum_sub['year'], annual_cum_sub['cumulative_counties'], 
             's-', linewidth=3, markersize=8, label='支行网点覆盖县数 (2007年前调整)', color='#A23B72')
    
    ax1.set_xlabel('年份', fontsize=14)
    ax1.set_ylabel('累积覆盖县数', fontsize=14)
    ax1.set_title('邮储银行县级覆盖扩散时间趋势 (修正版)', fontsize=16, fontweight='bold')
    ax1.legend(fontsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.set_xlim(1996, 2016)
    
    # 添加关键数据标注
    for i, (year, count) in enumerate(zip(annual_cum_all['year'], annual_cum_all['cumulative_counties'])):
        if year in [1997, 2007, 2015]:
            ax1.annotate(f'{count}县', 
                        xy=(year, count), xytext=(10, 10), 
                        textcoords='offset points', fontsize=10,
                        bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
    
    # 下图：覆盖率
    ax2.plot(annual_cum_all['year'], annual_cum_all['cumulative_ratio'] * 100, 
             'o-', linewidth=3, markersize=8, label='全部网点覆盖率', color='#2E86AB')
    ax2.plot(annual_cum_sub['year'], annual_cum_sub['cumulative_ratio'] * 100, 
             's-', linewidth=3, markersize=8, label='支行网点覆盖率 (2007年前调整)', color='#A23B72')
    
    ax2.set_xlabel('年份', fontsize=14)
    ax2.set_ylabel('覆盖率 (%)', fontsize=14)
    ax2.set_title('邮储银行县级覆盖率变化趋势', fontsize=16, fontweight='bold')
    ax2.legend(fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.set_xlim(1996, 2016)
    ax2.set_ylim(0, 105)
    
    # 添加覆盖率标注
    for i, (year, ratio) in enumerate(zip(annual_cum_all['year'], annual_cum_all['cumulative_ratio'])):
        if year in [1997, 2007, 2015]:
            ax2.annotate(f'{ratio*100:.1f}%', 
                        xy=(year, ratio*100), xytext=(10, 10), 
                        textcoords='offset points', fontsize=10,
                        bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgreen', alpha=0.7))
    
    plt.tight_layout()
    plt.show()
    
    # 打印关键统计
    print("\n=== 修正后的关键统计 ===")
    print("全部网点:")
    print(f"  1997年起始: {annual_cum_all.iloc[0]['cumulative_counties']}县 ({annual_cum_all.iloc[0]['cumulative_ratio']:.1%})")
    print(f"  2015年最终: {annual_cum_all.iloc[-1]['cumulative_counties']}县 ({annual_cum_all.iloc[-1]['cumulative_ratio']:.1%})")
    
    print("支行网点:")
    print(f"  1997年起始: {annual_cum_sub.iloc[0]['cumulative_counties']}县 ({annual_cum_sub.iloc[0]['cumulative_ratio']:.1%})")
    print(f"  2015年最终: {annual_cum_sub.iloc[-1]['cumulative_counties']}县 ({annual_cum_sub.iloc[-1]['cumulative_ratio']:.1%})")
    
    return annual_cum_all, annual_cum_sub

# 运行修正版分析和绘图
annual_cum_all_corrected, annual_cum_sub_corrected = plot_corrected_diffusion_timeline()
