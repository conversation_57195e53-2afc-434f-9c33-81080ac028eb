import pandas as pd
import matplotlib.pyplot as plt
import os

# 设置基础路径
BASE_DIR = '/Users/<USER>/Downloads/CountyTFP'

def plot_id_timeline(df, province_code='13'):
    """
    绘制指定省份的ID持续时间图
    
    参数:
    - df: 包含countyid和year列的DataFrame
    - province_code: 省份代码（默认'13'为河北省）
    """
    # 确保countyid是字符串格式
    df['countyid'] = df['countyid'].astype(str).str.zfill(6)
    
    # 筛选指定省份的数据
    province_df = df[df['countyid'].str.startswith(province_code)]
    
    # 获取每个ID的最早和最晚年份
    id_timeline = {}
    for county_id in province_df['countyid'].unique():
        years = province_df[province_df['countyid'] == county_id]['year'].unique()
        if len(years) > 0:
            id_timeline[county_id] = {
                'start_year': min(years),
                'end_year': max(years)
            }
    
    # 转换为DataFrame以便绘图
    timeline_data = []
    for county_id, timeline in id_timeline.items():
        timeline_data.append({
            'countyid': county_id,
            'start_year': timeline['start_year'],
            'end_year': timeline['end_year'],
            'duration': timeline['end_year'] - timeline['start_year'] + 1
        })
    
    timeline_df = pd.DataFrame(timeline_data)
    
    # 按开始年份排序
    timeline_df = timeline_df.sort_values('start_year')
    
    # 创建图形
    plt.figure(figsize=(15, 8))
    
    # 绘制水平条形图
    y_pos = range(len(timeline_df))
    plt.barh(y_pos, timeline_df['duration'], 
             left=timeline_df['start_year'],
             height=0.8,
             alpha=0.6,
             color='skyblue')
    
    # 添加ID标签
    plt.yticks(y_pos, timeline_df['countyid'], fontsize=8)
    
    # 设置标题和轴标签
    plt.title(f'河北省({province_code})各县区ID持续时间', fontsize=12)
    plt.xlabel('年份', fontsize=10)
    plt.ylabel('区划代码', fontsize=10)
    
    # 添加网格线
    plt.grid(True, axis='x', linestyle='--', alpha=0.7)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    output_path = os.path.join(BASE_DIR, 'data', 'processed', f'hebei_id_timeline.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"图片已保存至: {output_path}")
    
    # 显示图片
    plt.show()
    
    # 返回数据供进一步分析
    return timeline_df

def main():
    # 设置文件路径
    merged_io_tfp_path = os.path.join(BASE_DIR, 'data', 'processed', 'merged_io_tfp.parquet')
    
    # 读取数据
    print("正在读取数据...")
    print(f"数据文件路径: {merged_io_tfp_path}")
    df = pd.read_parquet(merged_io_tfp_path)
    
    # 绘制河北省ID持续时间图
    print("\n正在绘制河北省ID持续时间图...")
    timeline_df = plot_id_timeline(df)
    
    # 打印一些基本统计信息
    print("\n基本统计信息:")
    print(f"总ID数量: {len(timeline_df)}")
    print(f"最早开始年份: {timeline_df['start_year'].min()}")
    print(f"最晚结束年份: {timeline_df['end_year'].max()}")
    print(f"平均持续时间: {timeline_df['duration'].mean():.2f}年")
    print(f"最长持续时间: {timeline_df['duration'].max()}年")
    print(f"最短持续时间: {timeline_df['duration'].min()}年")

if __name__ == "__main__":
    main() 