#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
生成每个县每个时间段累积PSBC开点数的变量

此脚本读取PSBC.xlsx文件，处理数据，并生成一个包含每个县每年累积网点数的CSV文件。
主要步骤包括：
1. 数据预处理和清洗
2. 填补缺失的county_id
3. 计算每个县每年的累积网点数
4. 输出结果到CSV文件
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
from collections import defaultdict
from scipy.spatial.distance import cdist

# 设置文件路径
INPUT_FILE = './data/raw/PSBC.xlsx'
OUTPUT_FILE = './data/processed/psbc_cumulative_branches.csv'

def main():
    print("开始处理PSBC数据...")

    # 读取Excel文件
    df = read_and_preprocess_data(INPUT_FILE)

    # 填补缺失的county_id
    df = fill_missing_county_ids(df)

    # 计算累积网点数
    result_df = calculate_cumulative_branches(df)

    # 保存结果
    save_results(result_df, OUTPUT_FILE)

    print(f"处理完成！结果已保存到 {OUTPUT_FILE}")

def read_and_preprocess_data(file_path):
    """读取并预处理PSBC数据"""
    print(f"读取数据文件: {file_path}")

    # 读取Excel文件
    df = pd.read_excel(file_path)

    # 重命名列以便更容易理解
    column_names = {
        'Unnamed: 0': 'id',
        'Unnamed: 1': 'bank_code',
        'Unnamed: 2': 'bank_name',
        'Unnamed: 3': 'status_code',
        'Unnamed: 4': 'open_date',
        'Unnamed: 5': 'close_date',
        'Unnamed: 6': 'unknown_1',
        'Unnamed: 7': 'province',
        'Unnamed: 8': 'city',
        'Unnamed: 9': 'county',
        'Unnamed: 10': 'longitude',
        'Unnamed: 11': 'latitude',
        'Unnamed: 12': 'parent_bank',
        'Unnamed: 13': 'unknown_2',
        'Unnamed: 14': 'address',
        'Unnamed: 15': 'postal_code',
        'Unnamed: 16': 'unknown_3',
        'Unnamed: 17': 'unknown_4',
        'Unnamed: 18': 'bank_short_name',
        'Unnamed: 19': 'bank_type',
        'Unnamed: 20': 'bank_category',
        'Unnamed: 21': 'branch_type',
        'Unnamed: 22': 'region_code',
        'Unnamed: 23': 'regulatory_agency',
        'Unnamed: 24': 'unknown_5'
    }
    df = df.rename(columns=column_names)

    # 转换日期列
    df['open_date'] = pd.to_datetime(df['open_date'], errors='coerce')
    df['open_year'] = df['open_date'].dt.year

    # 转换关闭日期列
    df['close_date'] = pd.to_datetime(df['close_date'], errors='coerce')
    df['close_year'] = df['close_date'].dt.year

    # 将region_code转换为整数形式的county_id
    df['county_id'] = df['region_code'].fillna(0).astype(int)

    # 检查关闭日期情况
    closed_branches = df['close_date'].notna().sum()
    print(f"数据预处理完成，共 {len(df)} 条记录，其中 {closed_branches} 条有关闭日期")

    return df

def fill_missing_county_ids(df):
    """填补缺失的county_id"""
    print("开始填补缺失的county_id...")

    # 找出缺失county_id的记录
    missing_county_id = df[df['county_id'] == 0].copy()
    print(f"缺失county_id的记录数: {len(missing_county_id)}")

    if len(missing_county_id) == 0:
        return df

    # 创建一个字典来存储填补的county_id
    filled_county_ids = {}

    # 第一层：使用省份+县名组合
    for idx, row in missing_county_id.iterrows():
        county = row['county']
        province = row['province']

        if pd.notna(county) and pd.notna(province):
            # 查找具有相同省份和县名组合且有county_id的记录
            matching_records = df[(df['county'] == county) &
                                 (df['province'] == province) &
                                 (df['county_id'] > 0)]

            if len(matching_records) > 0:
                # 获取最常见的county_id
                most_common_county_id = matching_records['county_id'].mode()[0]
                filled_county_ids[idx] = most_common_county_id

    # 第二层：使用城市+县名组合
    for idx, row in missing_county_id.iterrows():
        if idx in filled_county_ids:
            continue

        county = row['county']
        city = row['city']

        if pd.notna(county) and pd.notna(city):
            # 查找具有相同城市和县名组合且有county_id的记录
            matching_records = df[(df['county'] == county) &
                                 (df['city'] == city) &
                                 (df['county_id'] > 0)]

            if len(matching_records) > 0:
                # 获取最常见的county_id
                most_common_county_id = matching_records['county_id'].mode()[0]
                filled_county_ids[idx] = most_common_county_id

    # 第三层：使用地理坐标接近度
    for idx, row in missing_county_id.iterrows():
        if idx in filled_county_ids:
            continue

        county = row['county']
        longitude = row['longitude']
        latitude = row['latitude']

        if pd.notna(county) and pd.notna(longitude) and pd.notna(latitude):
            # 查找具有相同县名且有county_id和地理坐标的记录
            matching_records = df[(df['county'] == county) &
                                 (df['county_id'] > 0) &
                                 df['longitude'].notna() &
                                 df['latitude'].notna()]

            if len(matching_records) > 0:
                # 计算地理距离
                point1 = np.array([[longitude, latitude]])
                points2 = matching_records[['longitude', 'latitude']].values
                distances = cdist(point1, points2, 'euclidean')[0]

                # 获取距离最近的记录的county_id
                closest_idx = np.argmin(distances)
                closest_county_id = matching_records.iloc[closest_idx]['county_id']
                filled_county_ids[idx] = closest_county_id

    # 第四层：使用出现频率最高的county_id
    for idx, row in missing_county_id.iterrows():
        if idx in filled_county_ids:
            continue

        county = row['county']

        if pd.notna(county):
            # 查找具有相同县名且有county_id的记录
            matching_records = df[(df['county'] == county) & (df['county_id'] > 0)]

            if len(matching_records) > 0:
                # 获取最常见的county_id
                most_common_county_id = matching_records['county_id'].mode()[0]
                filled_county_ids[idx] = most_common_county_id

    # 应用填补的county_id
    for idx, county_id in filled_county_ids.items():
        df.at[idx, 'county_id'] = county_id

    print(f"成功填补 {len(filled_county_ids)} 条记录的county_id")
    print(f"仍有 {len(missing_county_id) - len(filled_county_ids)} 条记录无法填补county_id")

    return df

def calculate_cumulative_branches(df):
    """计算每个县每年的累积网点数，考虑网点关闭情况"""
    print("开始计算累积网点数...")

    # 确定年份范围
    min_year = df['open_year'].min()
    max_year = df['open_year'].max()
    years = range(min_year, max_year + 1)
    print(f"年份范围: {min_year} 至 {max_year}")

    # 获取所有有效的county_id
    counties = df[df['county_id'] > 0]['county_id'].unique()
    print(f"有效的county_id数量: {len(counties)}")

    # 创建一个空的列表来存储结果
    result = []

    # 对每个县的每一年计算累积网点数
    for county in counties:
        county_branches = df[df['county_id'] == county]

        for year in years:
            # 计算到该年为止开设的网点数
            opened = len(county_branches[county_branches['open_year'] <= year])

            # 计算到该年为止关闭的网点数
            closed = len(county_branches[(county_branches['close_year'].notna()) &
                                        (county_branches['close_year'] <= year)])

            # 计算实际运营的累积网点数
            active_branches = opened - closed

            # 将结果添加到列表中
            result.append({
                'county_id': county,
                'year': year,
                'opened_branches': opened,
                'closed_branches': closed,
                'cumulative_branches': active_branches
            })

    # 创建结果DataFrame
    result_df = pd.DataFrame(result)
    print(f"累积网点数计算完成，共 {len(result_df)} 条记录")

    # 显示一些统计信息
    total_opened = result_df[result_df['year'] == max_year]['opened_branches'].sum()
    total_closed = result_df[result_df['year'] == max_year]['closed_branches'].sum()
    total_active = result_df[result_df['year'] == max_year]['cumulative_branches'].sum()
    print(f"截至 {max_year} 年，总开设网点数: {total_opened}, 总关闭网点数: {total_closed}, 实际运营网点数: {total_active}")

    return result_df

def save_results(df, file_path):
    """保存结果到CSV文件"""
    # 确保输出目录存在
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    # 保存结果
    df.to_csv(file_path, index=False)
    print(f"结果已保存到: {file_path}")

    # 显示一些统计信息
    print(f"结果包含 {df['county_id'].nunique()} 个县的数据")
    print(f"结果包含 {df['year'].nunique()} 个年份的数据")
    print(f"累积网点数的范围: {df['cumulative_branches'].min()} 至 {df['cumulative_branches'].max()}")
    print(f"累积网点数的平均值: {df['cumulative_branches'].mean():.2f}")

    # 显示开设和关闭网点的统计信息
    max_year = df['year'].max()
    year_data = df[df['year'] == max_year]
    print(f"\n截至 {max_year} 年的统计信息:")
    print(f"总开设网点数: {year_data['opened_branches'].sum()}")
    print(f"总关闭网点数: {year_data['closed_branches'].sum()}")
    print(f"实际运营网点数: {year_data['cumulative_branches'].sum()}")
    print(f"关闭率: {year_data['closed_branches'].sum() / year_data['opened_branches'].sum() * 100:.2f}%")

if __name__ == "__main__":
    main()
