#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
绘制PSBC累积开点和贷款点的县域地图分布（去除市辖区）

此脚本读取PSBC网点数据和GIS数据，生成县域层面的地图可视化，
排除市辖区，只显示县、县级市、自治县等县域单位。
"""

import pandas as pd
import geopandas as gpd
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.colors import LinearSegmentedColormap
import numpy as np
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置文件路径
TOTAL_BRANCHES_FILE = '/Users/<USER>/Downloads/CountyTFP/data/processed/psbc_cumulative_branches.csv'
LOAN_BRANCHES_FILE = '/Users/<USER>/Downloads/CountyTFP/data/processed/psbc_loan_branches.csv'
GIS_FILE = '/Users/<USER>/Downloads/CountyTFP/data/raw/2015county/2015county.shp'
ID_MAPPING_FILE = '/Users/<USER>/Downloads/CountyTFP/data/processed/psbc_gis_id_mapping_complete.csv'
COUNTRY_FILE = '/Users/<USER>/Downloads/CountyTFP/data/raw/Province/1. Country/country.shp'
PROVINCE_FILE = '/Users/<USER>/Downloads/CountyTFP/data/raw/Province/2. Province/province.shp'
OUTPUT_DIR = '/Users/<USER>/Downloads/CountyTFP/outputs/maps'

def load_and_prepare_data(include_districts=False):
    """加载并准备数据"""
    print("正在加载数据...")

    # 读取网点数据
    total_df = pd.read_csv(TOTAL_BRANCHES_FILE)
    loan_df = pd.read_csv(LOAN_BRANCHES_FILE)

    # 读取GIS数据
    gdf = gpd.read_file(GIS_FILE)

    # 读取ID映射表
    mapping_df = pd.read_csv(ID_MAPPING_FILE)

    if include_districts:
        # 包含所有县级单位（包括市辖区）
        gdf_filtered = gdf.copy()
        print(f"包含所有县级单位: {len(gdf_filtered)} 个单位")
    else:
        # 过滤掉市辖区，只保留县域单位
        county_types = ['县', '县级市', '自治县', '旗', '自治旗', '特区', '林区']
        gdf_filtered = gdf[gdf['县级类'].isin(county_types)].copy()
        print(f"原始GIS数据: {len(gdf)} 个单位")
        print(f"过滤后县域数据: {len(gdf_filtered)} 个单位")
        print(f"去除的市辖区数量: {len(gdf) - len(gdf_filtered)}")

    return total_df, loan_df, gdf_filtered, mapping_df

def merge_data_with_gis(data_df, gdf_counties, mapping_df, year=2021):
    """将网点数据与GIS数据合并"""
    print(f"正在合并 {year} 年的数据...")

    # 筛选指定年份的数据
    year_data = data_df[data_df['year'] == year].copy()

    # 转换county_id为字符串
    year_data['county_id'] = year_data['county_id'].astype(str)

    # 确保mapping_df中的psbc_id也是字符串
    mapping_df = mapping_df.copy()
    mapping_df['psbc_id'] = mapping_df['psbc_id'].astype(str)
    mapping_df['gis_id'] = mapping_df['gis_id'].astype(str)

    # 与ID映射表合并
    merged_data = pd.merge(year_data, mapping_df,
                          left_on='county_id', right_on='psbc_id',
                          how='inner')

    # 确保GIS数据中的区划码也是字符串
    gdf_counties = gdf_counties.copy()
    gdf_counties['区划码'] = gdf_counties['区划码'].astype(str)

    # 与GIS数据合并
    final_data = pd.merge(gdf_counties, merged_data,
                         left_on='区划码', right_on='gis_id',
                         how='left')

    print(f"成功合并的县域数量: {final_data.dropna(subset=['county_id']).shape[0]}")
    print(f"总县域数量: {len(final_data)}")

    return final_data

def create_map(gdf_data, value_col, title, output_file,
               cmap='YlOrRd', figsize=(16, 12), use_classification=True):
    """创建地图"""
    print(f"正在创建地图: {title}")

    # 加载国界和省界数据
    country_gdf = gpd.read_file(COUNTRY_FILE)
    province_gdf = gpd.read_file(PROVINCE_FILE)

    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=figsize)

    # 处理缺失值
    gdf_plot = gdf_data.copy()
    gdf_plot[value_col] = gdf_plot[value_col].fillna(0)

    # 获取有数据的记录
    data_values = gdf_plot[gdf_plot[value_col] > 0][value_col]

    if use_classification and len(data_values) > 0:
        # 使用分级设色，提高颜色区分度
        # 计算分位数
        quantiles = [0, 0.2, 0.4, 0.6, 0.8, 0.9, 0.95, 1.0]
        breaks = data_values.quantile(quantiles).unique()

        # 如果分位数太少，使用等间距分级
        if len(breaks) < 5:
            max_val = data_values.max()
            breaks = np.linspace(0, max_val, 8)

        # 创建分级
        gdf_plot['classified'] = pd.cut(gdf_plot[value_col],
                                       bins=breaks,
                                       include_lowest=True,
                                       duplicates='drop')

        # 绘制地图
        gdf_plot.plot(column='classified',
                      cmap=cmap,
                      linewidth=0.1,
                      edgecolor='white',
                      ax=ax,
                      legend=True)

        # 添加省界
        province_gdf.plot(ax=ax,
                          facecolor='none',
                          edgecolor='black',
                          linewidth=0.5,
                          alpha=0.8)

        # 添加国界
        country_gdf.plot(ax=ax,
                         facecolor='none',
                         edgecolor='black',
                         linewidth=1.5,
                         alpha=1.0)
    else:
        # 使用连续色彩映射
        vmin = 0
        vmax = gdf_plot[value_col].quantile(0.95)

        gdf_plot.plot(column=value_col,
                      cmap=cmap,
                      linewidth=0.1,
                      edgecolor='white',
                      vmin=vmin,
                      vmax=vmax,
                      ax=ax,
                      legend=True)

    # 添加省界
    province_gdf.plot(ax=ax,
                      facecolor='none',
                      edgecolor='black',
                      linewidth=0.5,
                      alpha=0.8)

    # 添加国界
    country_gdf.plot(ax=ax,
                     facecolor='none',
                     edgecolor='black',
                     linewidth=1.5,
                     alpha=1.0)

    # 设置标题和样式
    ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
    ax.set_axis_off()

    # 添加统计信息
    stats_text = f"""统计信息:
有数据的单位: {(gdf_plot[value_col] > 0).sum()}
总单位数: {len(gdf_plot)}
最大值: {gdf_plot[value_col].max():.0f}
平均值: {gdf_plot[value_col].mean():.2f}
中位数: {gdf_plot[value_col].median():.2f}"""

    ax.text(0.02, 0.02, stats_text, transform=ax.transAxes,
            fontsize=10, verticalalignment='bottom',
            bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

    # 保存图片
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"地图已保存: {output_file}")

def create_comparison_map(gdf_total, gdf_loan, output_file, figsize=(20, 10)):
    """创建对比地图"""
    print("正在创建对比地图...")

    # 创建子图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=figsize)

    # 处理数据
    gdf_total_plot = gdf_total.copy()
    gdf_loan_plot = gdf_loan.copy()

    gdf_total_plot['cumulative_branches'] = gdf_total_plot['cumulative_branches'].fillna(0)
    gdf_loan_plot['cumulative_loan_branches'] = gdf_loan_plot['cumulative_loan_branches'].fillna(0)

    # 设置颜色范围
    total_vmax = gdf_total_plot['cumulative_branches'].quantile(0.95)
    loan_vmax = gdf_loan_plot['cumulative_loan_branches'].quantile(0.95)

    # 绘制总网点数地图
    gdf_total_plot.plot(column='cumulative_branches',
                        cmap='Blues',
                        linewidth=0.1,
                        edgecolor='white',
                        vmin=0,
                        vmax=total_vmax,
                        ax=ax1,
                        legend=True)

    ax1.set_title('2021年累积总网点数分布', fontsize=14, fontweight='bold')
    ax1.set_axis_off()

    # 绘制贷款网点数地图
    gdf_loan_plot.plot(column='cumulative_loan_branches',
                       cmap='Reds',
                       linewidth=0.1,
                       edgecolor='white',
                       vmin=0,
                       vmax=loan_vmax,
                       ax=ax2,
                       legend=True)

    ax2.set_title('2021年累积贷款网点数分布', fontsize=14, fontweight='bold')
    ax2.set_axis_off()

    # 添加总标题
    fig.suptitle('PSBC县域网点分布对比（不含市辖区）', fontsize=16, fontweight='bold')

    # 保存图片
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"对比地图已保存: {output_file}")

def create_ratio_map(gdf_merged, output_file, figsize=(16, 12)):
    """创建贷款网点占比地图"""
    print("正在创建贷款网点占比地图...")

    # 计算贷款网点占比
    gdf_plot = gdf_merged.copy()
    gdf_plot['total_branches'] = gdf_plot['cumulative_branches'].fillna(0)
    gdf_plot['loan_branches'] = gdf_plot['cumulative_loan_branches'].fillna(0)

    # 计算占比（避免除零错误）
    gdf_plot['loan_ratio'] = np.where(
        gdf_plot['total_branches'] > 0,
        gdf_plot['loan_branches'] / gdf_plot['total_branches'] * 100,
        0
    )

    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=figsize)

    # 绘制地图
    gdf_plot.plot(column='loan_ratio',
                  cmap='RdYlBu_r',
                  linewidth=0.1,
                  edgecolor='white',
                  vmin=0,
                  vmax=100,
                  ax=ax,
                  legend=True)

    ax.set_title('2021年PSBC贷款网点占总网点比例分布（县域）', fontsize=16, fontweight='bold', pad=20)
    ax.set_axis_off()

    # 添加统计信息
    valid_data = gdf_plot[gdf_plot['total_branches'] > 0]
    stats_text = f"""统计信息:
有网点的县域: {len(valid_data)}
平均占比: {valid_data['loan_ratio'].mean():.1f}%
中位数占比: {valid_data['loan_ratio'].median():.1f}%
最高占比: {valid_data['loan_ratio'].max():.1f}%"""

    ax.text(0.02, 0.02, stats_text, transform=ax.transAxes,
            fontsize=10, verticalalignment='bottom',
            bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

    # 保存图片
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"占比地图已保存: {output_file}")

def create_comparison_map_with_borders(gdf_total, gdf_loan, output_file, figsize=(20, 10)):
    """创建带边界的对比地图"""
    print("正在创建带边界的对比地图...")

    # 加载国界和省界数据
    country_gdf = gpd.read_file(COUNTRY_FILE)
    province_gdf = gpd.read_file(PROVINCE_FILE)

    # 创建子图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=figsize)

    # 处理数据
    gdf_total_plot = gdf_total.copy()
    gdf_loan_plot = gdf_loan.copy()

    gdf_total_plot['cumulative_branches'] = gdf_total_plot['cumulative_branches'].fillna(0)
    gdf_loan_plot['cumulative_loan_branches'] = gdf_loan_plot['cumulative_loan_branches'].fillna(0)

    # 设置颜色范围
    total_vmax = gdf_total_plot['cumulative_branches'].quantile(0.95)
    loan_vmax = gdf_loan_plot['cumulative_loan_branches'].quantile(0.95)

    # 绘制总网点数地图
    gdf_total_plot.plot(column='cumulative_branches',
                        cmap='Blues',
                        linewidth=0.1,
                        edgecolor='white',
                        vmin=0,
                        vmax=total_vmax,
                        ax=ax1,
                        legend=True)

    # 添加边界到第一个子图
    province_gdf.plot(ax=ax1, facecolor='none', edgecolor='black', linewidth=0.5, alpha=0.8)
    country_gdf.plot(ax=ax1, facecolor='none', edgecolor='black', linewidth=1.5, alpha=1.0)

    ax1.set_title('2021年累积总网点数分布', fontsize=14, fontweight='bold')
    ax1.set_axis_off()

    # 绘制贷款网点数地图
    gdf_loan_plot.plot(column='cumulative_loan_branches',
                       cmap='Reds',
                       linewidth=0.1,
                       edgecolor='white',
                       vmin=0,
                       vmax=loan_vmax,
                       ax=ax2,
                       legend=True)

    # 添加边界到第二个子图
    province_gdf.plot(ax=ax2, facecolor='none', edgecolor='black', linewidth=0.5, alpha=0.8)
    country_gdf.plot(ax=ax2, facecolor='none', edgecolor='black', linewidth=1.5, alpha=1.0)

    ax2.set_title('2021年累积贷款网点数分布', fontsize=14, fontweight='bold')
    ax2.set_axis_off()

    # 添加总标题
    fig.suptitle('PSBC县域网点分布对比（不含市辖区）', fontsize=16, fontweight='bold')

    # 保存图片
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"带边界的对比地图已保存: {output_file}")

def create_ratio_map_with_borders(gdf_merged, output_file, figsize=(16, 12)):
    """创建带边界的贷款网点占比地图"""
    print("正在创建带边界的贷款网点占比地图...")

    # 加载国界和省界数据
    country_gdf = gpd.read_file(COUNTRY_FILE)
    province_gdf = gpd.read_file(PROVINCE_FILE)

    # 计算贷款网点占比
    gdf_plot = gdf_merged.copy()
    gdf_plot['total_branches'] = gdf_plot['cumulative_branches'].fillna(0)
    gdf_plot['loan_branches'] = gdf_plot['cumulative_loan_branches'].fillna(0)

    # 计算占比（避免除零错误）
    gdf_plot['loan_ratio'] = np.where(
        gdf_plot['total_branches'] > 0,
        gdf_plot['loan_branches'] / gdf_plot['total_branches'] * 100,
        0
    )

    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=figsize)

    # 绘制地图
    gdf_plot.plot(column='loan_ratio',
                  cmap='RdYlBu_r',
                  linewidth=0.1,
                  edgecolor='white',
                  vmin=0,
                  vmax=100,
                  ax=ax,
                  legend=True)

    # 添加边界
    province_gdf.plot(ax=ax, facecolor='none', edgecolor='black', linewidth=0.5, alpha=0.8)
    country_gdf.plot(ax=ax, facecolor='none', edgecolor='black', linewidth=1.5, alpha=1.0)

    ax.set_title('2021年PSBC贷款网点占总网点比例分布（县域）', fontsize=16, fontweight='bold', pad=20)
    ax.set_axis_off()

    # 添加统计信息
    valid_data = gdf_plot[gdf_plot['total_branches'] > 0]
    stats_text = f"""统计信息:
有网点的县域: {len(valid_data)}
平均占比: {valid_data['loan_ratio'].mean():.1f}%
中位数占比: {valid_data['loan_ratio'].median():.1f}%
最高占比: {valid_data['loan_ratio'].max():.1f}%"""

    ax.text(0.02, 0.02, stats_text, transform=ax.transAxes,
            fontsize=10, verticalalignment='bottom',
            bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

    # 保存图片
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"带边界的占比地图已保存: {output_file}")

def main():
    """主函数"""
    import os

    # 创建输出目录
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # 只创建去掉市辖区的版本（counties版本），并添加国界和省界
    print(f"\n{'='*50}")
    print("创建县域地图（含国界和省界）...")
    print(f"{'='*50}")

    # 加载数据（不包含市辖区）
    total_df, loan_df, gdf_counties, mapping_df = load_and_prepare_data(include_districts=False)

    # 合并2021年数据
    gdf_total_2021 = merge_data_with_gis(total_df, gdf_counties, mapping_df, 2021)
    gdf_loan_2021 = merge_data_with_gis(loan_df, gdf_counties, mapping_df, 2021)

    # 创建单独的地图，使用更好的颜色方案
    create_map(gdf_total_2021, 'cumulative_branches',
               '2021年PSBC累积总网点数分布（县域）',
               f'{OUTPUT_DIR}/psbc_total_branches_2021_counties_with_borders.png',
               cmap='Blues')

    create_map(gdf_loan_2021, 'cumulative_loan_branches',
               '2021年PSBC累积贷款网点数分布（县域）',
               f'{OUTPUT_DIR}/psbc_loan_branches_2021_counties_with_borders.png',
               cmap='Reds')

    # 创建对比地图（也需要修改以添加边界）
    create_comparison_map_with_borders(gdf_total_2021, gdf_loan_2021,
                                      f'{OUTPUT_DIR}/psbc_branches_comparison_2021_counties_with_borders.png')

    # 合并数据创建占比地图
    gdf_merged = pd.merge(gdf_total_2021[['区划码', 'geometry', 'cumulative_branches']],
                         gdf_loan_2021[['区划码', 'cumulative_loan_branches']],
                         on='区划码', how='left')

    create_ratio_map_with_borders(gdf_merged, f'{OUTPUT_DIR}/psbc_loan_ratio_2021_counties_with_borders.png')

    print(f"\n{'='*50}")
    print("县域地图创建完成！")
    print(f"输出目录: {OUTPUT_DIR}")
    print("生成的地图文件（含国界和省界）:")
    print("- psbc_total_branches_2021_counties_with_borders.png")
    print("- psbc_loan_branches_2021_counties_with_borders.png")
    print("- psbc_branches_comparison_2021_counties_with_borders.png")
    print("- psbc_loan_ratio_2021_counties_with_borders.png")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()
