#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建PSBC网点和贷款机构政策扩散柱状图
显示每年有PSBC网点和有贷款网点的县数量变化
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import warnings
import os
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 文件路径
TOTAL_BRANCHES_FILE = '/Users/<USER>/Downloads/CountyTFP/data/processed/psbc_cumulative_branches.csv'
LOAN_BRANCHES_FILE = '/Users/<USER>/Downloads/CountyTFP/data/processed/psbc_loan_branches.csv'
GIS_FILE = '/Users/<USER>/Downloads/CountyTFP/data/raw/2015county/2015county.shp'
ID_MAPPING_FILE = '/Users/<USER>/Downloads/CountyTFP/data/processed/psbc_gis_id_mapping_complete.csv'
OUTPUT_DIR = '/Users/<USER>/Downloads/CountyTFP/outputs/charts'

def load_data():
    """加载数据并过滤掉市辖区"""
    print("正在加载数据...")

    # 读取网点数据
    total_df = pd.read_csv(TOTAL_BRANCHES_FILE)
    loan_df = pd.read_csv(LOAN_BRANCHES_FILE)

    # 读取GIS数据和ID映射表，用于过滤市辖区
    import geopandas as gpd
    gdf = gpd.read_file(GIS_FILE)
    mapping_df = pd.read_csv(ID_MAPPING_FILE)

    # 只保留县域单位
    county_types = ['县', '县级市', '自治县', '旗', '自治旗', '特区', '林区']
    gdf_counties = gdf[gdf['县级类'].isin(county_types)].copy()

    # 获取县域单位的GIS ID
    county_gis_ids = set(gdf_counties['区划码'].astype(str))

    # 从映射表中获取对应的PSBC ID
    mapping_df['gis_id'] = mapping_df['gis_id'].astype(str)
    county_mapping = mapping_df[mapping_df['gis_id'].isin(county_gis_ids)]
    county_psbc_ids = set(county_mapping['psbc_id'].astype(str))

    # 过滤网点数据，只保留县域单位
    total_df['county_id'] = total_df['county_id'].astype(str)
    loan_df['county_id'] = loan_df['county_id'].astype(str)

    total_df_counties = total_df[total_df['county_id'].isin(county_psbc_ids)]
    loan_df_counties = loan_df[loan_df['county_id'].isin(county_psbc_ids)]

    print(f"原始总网点数据: {len(total_df)} 条记录")
    print(f"过滤后总网点数据: {len(total_df_counties)} 条记录")
    print(f"原始贷款网点数据: {len(loan_df)} 条记录")
    print(f"过滤后贷款网点数据: {len(loan_df_counties)} 条记录")
    print(f"县域单位数量: {len(gdf_counties)}")

    return total_df_counties, loan_df_counties

def calculate_county_coverage(df, value_col, years):
    """计算每年有网点的县数量"""
    coverage_data = []

    for year in years:
        year_data = df[df['year'] == year]
        # 计算有网点的县数量（网点数 > 0）
        counties_with_branches = (year_data[value_col] > 0).sum()
        total_counties = len(year_data)
        coverage_rate = counties_with_branches / total_counties * 100

        coverage_data.append({
            'year': year,
            'counties_with_branches': counties_with_branches,
            'total_counties': total_counties,
            'coverage_rate': coverage_rate
        })

    return pd.DataFrame(coverage_data)

def create_diffusion_chart(coverage_df, title, output_file, color='steelblue'):
    """创建政策扩散柱状图"""
    print(f"正在创建图表: {title}")

    # 创建图形
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))

    # 上图：有网点的县数量
    bars1 = ax1.bar(coverage_df['year'], coverage_df['counties_with_branches'],
                     color=color, alpha=0.8, edgecolor='white', linewidth=0.5)

    ax1.set_title(f'{title} - 县数量', fontsize=16, fontweight='bold', pad=15)
    ax1.set_xlabel('年份', fontsize=12)
    ax1.set_ylabel('有网点的县数量', fontsize=12)
    ax1.grid(True, alpha=0.3, axis='y')

    # 添加数值标签
    for bar in bars1:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 5,
                f'{int(height)}', ha='center', va='bottom', fontsize=10)

    # 设置y轴范围
    ax1.set_ylim(0, coverage_df['counties_with_branches'].max() * 1.1)

    # 下图：覆盖率
    bars2 = ax2.bar(coverage_df['year'], coverage_df['coverage_rate'],
                     color=color, alpha=0.6, edgecolor='white', linewidth=0.5)

    ax2.set_title(f'{title} - 覆盖率', fontsize=16, fontweight='bold', pad=15)
    ax2.set_xlabel('年份', fontsize=12)
    ax2.set_ylabel('覆盖率 (%)', fontsize=12)
    ax2.grid(True, alpha=0.3, axis='y')

    # 添加数值标签
    for bar in bars2:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{height:.1f}%', ha='center', va='bottom', fontsize=10)

    # 设置y轴范围
    ax2.set_ylim(0, 100)

    # 调整布局
    plt.tight_layout()

    # 保存图片
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"图表已保存: {output_file}")

def create_comparison_chart(total_coverage_df, loan_coverage_df, output_file):
    """创建对比图表"""
    print("正在创建对比图表...")

    # 创建图形
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 12))

    # 合并数据用于对比（只取重叠的年份）
    common_years = set(total_coverage_df['year']).intersection(set(loan_coverage_df['year']))
    common_years = sorted(list(common_years))

    total_subset = total_coverage_df[total_coverage_df['year'].isin(common_years)].sort_values('year')
    loan_subset = loan_coverage_df[loan_coverage_df['year'].isin(common_years)].sort_values('year')

    # 上图：县数量对比
    x = np.arange(len(common_years))
    width = 0.35

    bars1 = ax1.bar(x - width/2, total_subset['counties_with_branches'], width,
                    label='有PSBC网点的县', color='steelblue', alpha=0.8)
    bars2 = ax1.bar(x + width/2, loan_subset['counties_with_branches'], width,
                    label='有贷款网点的县', color='crimson', alpha=0.8)

    ax1.set_title('PSBC网点与贷款网点政策扩散对比 - 县数量', fontsize=16, fontweight='bold', pad=15)
    ax1.set_xlabel('年份', fontsize=12)
    ax1.set_ylabel('县数量', fontsize=12)
    ax1.set_xticks(x)
    ax1.set_xticklabels(common_years)
    ax1.legend(fontsize=11)
    ax1.grid(True, alpha=0.3, axis='y')

    # 下图：覆盖率对比
    ax2.plot(common_years, total_subset['coverage_rate'], 'o-',
             label='PSBC网点覆盖率', color='steelblue', linewidth=2, markersize=6)
    ax2.plot(common_years, loan_subset['coverage_rate'], 's-',
             label='贷款网点覆盖率', color='crimson', linewidth=2, markersize=6)

    ax2.set_title('PSBC网点与贷款网点政策扩散对比 - 覆盖率', fontsize=16, fontweight='bold', pad=15)
    ax2.set_xlabel('年份', fontsize=12)
    ax2.set_ylabel('覆盖率 (%)', fontsize=12)
    ax2.legend(fontsize=11)
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 100)

    # 添加数值标签
    for i, year in enumerate(common_years):
        total_rate = total_subset[total_subset['year'] == year]['coverage_rate'].iloc[0]
        loan_rate = loan_subset[loan_subset['year'] == year]['coverage_rate'].iloc[0]
        ax2.text(year, total_rate + 1, f'{total_rate:.1f}%', ha='center', va='bottom', fontsize=9)
        ax2.text(year, loan_rate + 1, f'{loan_rate:.1f}%', ha='center', va='bottom', fontsize=9)

    # 调整布局
    plt.tight_layout()

    # 保存图片
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"对比图表已保存: {output_file}")

def print_summary_statistics(total_coverage_df, loan_coverage_df):
    """打印统计摘要"""
    print("\n" + "="*60)
    print("政策扩散统计摘要")
    print("="*60)

    print("\nPSBC总网点扩散情况 (1997-2015年):")
    print(f"起始年份 (1997): {total_coverage_df.iloc[0]['counties_with_branches']}个县 ({total_coverage_df.iloc[0]['coverage_rate']:.1f}%)")
    print(f"结束年份 (2015): {total_coverage_df.iloc[-1]['counties_with_branches']}个县 ({total_coverage_df.iloc[-1]['coverage_rate']:.1f}%)")
    print(f"增长: +{total_coverage_df.iloc[-1]['counties_with_branches'] - total_coverage_df.iloc[0]['counties_with_branches']}个县")

    print("\nPSBC贷款网点扩散情况 (2007-2015年):")
    print(f"起始年份 (2007): {loan_coverage_df.iloc[0]['counties_with_branches']}个县 ({loan_coverage_df.iloc[0]['coverage_rate']:.1f}%)")
    print(f"结束年份 (2015): {loan_coverage_df.iloc[-1]['counties_with_branches']}个县 ({loan_coverage_df.iloc[-1]['coverage_rate']:.1f}%)")
    print(f"增长: +{loan_coverage_df.iloc[-1]['counties_with_branches'] - loan_coverage_df.iloc[0]['counties_with_branches']}个县")

    # 检查是否单调递增
    total_increasing = all(total_coverage_df['counties_with_branches'].diff().dropna() >= 0)
    loan_increasing = all(loan_coverage_df['counties_with_branches'].diff().dropna() >= 0)

    print(f"\nPSBC总网点扩散是否单调递增: {'是' if total_increasing else '否'}")
    print(f"PSBC贷款网点扩散是否单调递增: {'是' if loan_increasing else '否'}")

def main():
    """主函数"""
    # 创建输出目录
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # 加载数据
    total_df, loan_df = load_data()

    # 计算PSBC总网点覆盖情况 (1997-2015年)
    total_years = list(range(1997, 2016))
    total_coverage_df = calculate_county_coverage(total_df, 'cumulative_branches', total_years)

    # 计算PSBC贷款网点覆盖情况 (2007-2015年)
    loan_years = list(range(2007, 2016))
    loan_coverage_df = calculate_county_coverage(loan_df, 'cumulative_loan_branches', loan_years)

    # 创建PSBC总网点扩散图
    create_diffusion_chart(total_coverage_df, 'PSBC网点政策扩散',
                          f'{OUTPUT_DIR}/psbc_total_diffusion.png', 'steelblue')

    # 创建PSBC贷款网点扩散图
    create_diffusion_chart(loan_coverage_df, 'PSBC贷款网点政策扩散',
                          f'{OUTPUT_DIR}/psbc_loan_diffusion.png', 'crimson')

    # 创建对比图
    create_comparison_chart(total_coverage_df, loan_coverage_df,
                           f'{OUTPUT_DIR}/psbc_diffusion_comparison.png')

    # 打印统计摘要
    print_summary_statistics(total_coverage_df, loan_coverage_df)

    print(f"\n所有图表创建完成！输出目录: {OUTPUT_DIR}")

if __name__ == "__main__":
    main()
