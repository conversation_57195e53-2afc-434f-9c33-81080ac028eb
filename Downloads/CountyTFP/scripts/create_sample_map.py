#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建PSBC网点地图示例 - 2015年累积开点图
"""

import pandas as pd
import geopandas as gpd
import matplotlib.pyplot as plt
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 文件路径
TOTAL_BRANCHES_FILE = '/Users/<USER>/Downloads/CountyTFP/data/processed/psbc_cumulative_branches.csv'
GIS_FILE = '/Users/<USER>/Downloads/CountyTFP/data/raw/2015county/2015county.shp'
ID_MAPPING_FILE = '/Users/<USER>/Downloads/CountyTFP/data/processed/psbc_gis_id_mapping_complete.csv'
PROVINCE_FILE = '/Users/<USER>/Downloads/CountyTFP/data/raw/Province/2. Province/province.shp'
OUTPUT_DIR = '/Users/<USER>/Downloads/CountyTFP/outputs/maps'

def load_and_prepare_data():
    """加载并准备数据"""
    print("正在加载数据...")

    # 读取网点数据
    total_df = pd.read_csv(TOTAL_BRANCHES_FILE)

    # 读取GIS数据，只保留县域单位
    gdf = gpd.read_file(GIS_FILE)
    county_types = ['县', '县级市', '自治县', '旗', '自治旗', '特区', '林区']
    gdf_counties = gdf[gdf['县级类'].isin(county_types)].copy()

    # 读取ID映射表
    mapping_df = pd.read_csv(ID_MAPPING_FILE)

    # 读取省界数据
    province_gdf = gpd.read_file(PROVINCE_FILE)

    print(f"县域单位数量: {len(gdf_counties)}")
    print(f"网点数据年份范围: {total_df['year'].min()}-{total_df['year'].max()}")

    return total_df, gdf_counties, mapping_df, province_gdf

def merge_data_with_gis(data_df, gdf_counties, mapping_df, year):
    """将网点数据与GIS数据合并"""
    print(f"正在合并 {year} 年的数据...")

    # 筛选指定年份的数据
    year_data = data_df[data_df['year'] == year].copy()
    year_data['county_id'] = year_data['county_id'].astype(str)

    # 确保mapping_df中的ID都是字符串
    mapping_df = mapping_df.copy()
    mapping_df['psbc_id'] = mapping_df['psbc_id'].astype(str)
    mapping_df['gis_id'] = mapping_df['gis_id'].astype(str)

    # 与ID映射表合并
    merged_data = pd.merge(year_data, mapping_df,
                          left_on='county_id', right_on='psbc_id',
                          how='inner')

    # 确保GIS数据中的区划码也是字符串
    gdf_counties = gdf_counties.copy()
    gdf_counties['区划码'] = gdf_counties['区划码'].astype(str)

    # 与GIS数据合并
    final_data = pd.merge(gdf_counties, merged_data,
                         left_on='区划码', right_on='gis_id',
                         how='left')

    print(f"成功合并的县域数量: {final_data.dropna(subset=['county_id']).shape[0]}")

    return final_data

def get_fixed_color_breaks(data_type='total'):
    """设置固定的颜色分级断点"""
    if data_type == 'total':
        # 总网点数的固定分级
        breaks = [0, 1, 5, 10, 20, 30, 50, 80, 150]
        labels = ['0', '1-4', '5-9', '10-19', '20-29', '30-49', '50-79', '80+']
    else:  # loan
        # 贷款网点数的固定分级
        breaks = [0, 1, 3, 6, 10, 15, 25, 40, 80]
        labels = ['0', '1-2', '3-5', '6-9', '10-14', '15-24', '25-39', '40+']

    print(f"使用固定颜色分级: {labels}")
    return breaks, labels

def create_sample_map():
    """创建示例地图 - 2015年累积开点图"""
    import os
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # 加载数据
    total_df, gdf_counties, mapping_df, province_gdf = load_and_prepare_data()

    # 获取固定的颜色分级
    breaks, labels = get_fixed_color_breaks('total')

    # 合并2015年数据
    gdf_2015 = merge_data_with_gis(total_df, gdf_counties, mapping_df, 2015)

    # 处理缺失值
    gdf_2015['cumulative_branches'] = gdf_2015['cumulative_branches'].fillna(0)

    # 创建分级
    gdf_2015['branch_category'] = pd.cut(gdf_2015['cumulative_branches'],
                                        bins=breaks,
                                        labels=labels,
                                        include_lowest=True,
                                        right=False)

    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))

    # 绘制县域地图
    gdf_2015.plot(column='branch_category',
                  cmap='Blues',
                  linewidth=0.1,
                  edgecolor='white',
                  ax=ax,
                  legend=True,
                  legend_kwds={'title': '网点数量', 'loc': 'lower left'})

    # 添加省界
    province_gdf.plot(ax=ax,
                      facecolor='none',
                      edgecolor='black',
                      linewidth=0.5,
                      alpha=0.8)

    # 设置标题
    ax.set_title('2015年PSBC累积网点数', fontsize=18, fontweight='bold', pad=20)
    ax.set_axis_off()

    # 添加统计信息
    valid_data = gdf_2015[gdf_2015['cumulative_branches'] > 0]
    total_branches = gdf_2015['cumulative_branches'].sum()

    stats_text = f"""数据统计:
有网点县域: {len(valid_data)}个
总县域数: {len(gdf_2015)}个
总网点数: {total_branches:.0f}个
平均网点数: {gdf_2015['cumulative_branches'].mean():.1f}个
最大网点数: {gdf_2015['cumulative_branches'].max():.0f}个"""

    ax.text(0.02, 0.02, stats_text, transform=ax.transAxes,
            fontsize=11, verticalalignment='bottom',
            bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.9))

    # 添加分级说明
    range_text = f"分级标准: 固定颜色梯度\n{', '.join(labels[:4])}\n{', '.join(labels[4:])}"
    ax.text(0.98, 0.02, range_text, transform=ax.transAxes,
            fontsize=10, verticalalignment='bottom', horizontalalignment='right',
            bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.7))

    # 保存图片
    output_file = f'{OUTPUT_DIR}/sample_psbc_total_2015.png'
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"示例地图已保存: {output_file}")

    # 显示一些关键统计信息
    print(f"\n=== 2015年数据统计 ===")
    print(f"有网点的县域: {len(valid_data)}/{len(gdf_2015)} ({len(valid_data)/len(gdf_2015)*100:.1f}%)")
    print(f"总网点数: {total_branches:.0f}")
    print(f"平均每县网点数: {gdf_2015['cumulative_branches'].mean():.1f}")
    print(f"网点数中位数: {gdf_2015['cumulative_branches'].median():.1f}")
    print(f"最大网点数: {gdf_2015['cumulative_branches'].max():.0f}")
    print(f"颜色分级: {labels}")
    print("使用固定颜色梯度，确保所有年份地图比例尺一致")

if __name__ == "__main__":
    create_sample_map()
