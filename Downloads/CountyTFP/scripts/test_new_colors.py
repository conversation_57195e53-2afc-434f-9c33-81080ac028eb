#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试新的颜色方案 - 0和缺失值为白色，最低档位颜色深一点
"""

import pandas as pd
import geopandas as gpd
import matplotlib.pyplot as plt
import numpy as np
import warnings
import os
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 文件路径
TOTAL_BRANCHES_FILE = '/Users/<USER>/Downloads/CountyTFP/data/processed/psbc_cumulative_branches.csv'
GIS_FILE = '/Users/<USER>/Downloads/CountyTFP/data/raw/2015county/2015county.shp'
ID_MAPPING_FILE = '/Users/<USER>/Downloads/CountyTFP/data/processed/psbc_gis_id_mapping_complete.csv'
PROVINCE_FILE = '/Users/<USER>/Downloads/CountyTFP/data/raw/Province/2. Province/province.shp'
OUTPUT_DIR = '/Users/<USER>/Downloads/CountyTFP/outputs/maps'

def load_and_prepare_data():
    """加载并准备数据"""
    print("正在加载数据...")

    # 读取网点数据
    total_df = pd.read_csv(TOTAL_BRANCHES_FILE)

    # 读取GIS数据，只保留县域单位
    gdf = gpd.read_file(GIS_FILE)
    county_types = ['县', '县级市', '自治县', '旗', '自治旗', '特区', '林区']
    gdf_counties = gdf[gdf['县级类'].isin(county_types)].copy()

    # 读取ID映射表
    mapping_df = pd.read_csv(ID_MAPPING_FILE)

    # 读取省界数据
    province_gdf = gpd.read_file(PROVINCE_FILE)

    return total_df, gdf_counties, mapping_df, province_gdf

def merge_data_with_gis(data_df, gdf_counties, mapping_df, year):
    """将网点数据与GIS数据合并"""
    # 筛选指定年份的数据
    year_data = data_df[data_df['year'] == year].copy()
    year_data['county_id'] = year_data['county_id'].astype(str)

    # 确保mapping_df中的ID都是字符串
    mapping_df = mapping_df.copy()
    mapping_df['psbc_id'] = mapping_df['psbc_id'].astype(str)
    mapping_df['gis_id'] = mapping_df['gis_id'].astype(str)

    # 与ID映射表合并
    merged_data = pd.merge(year_data, mapping_df,
                          left_on='county_id', right_on='psbc_id',
                          how='inner')

    # 确保GIS数据中的区划码也是字符串
    gdf_counties = gdf_counties.copy()
    gdf_counties['区划码'] = gdf_counties['区划码'].astype(str)

    # 与GIS数据合并
    final_data = pd.merge(gdf_counties, merged_data,
                         left_on='区划码', right_on='gis_id',
                         how='left')

    return final_data

def create_test_map():
    """创建测试地图"""
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # 加载数据
    total_df, gdf_counties, mapping_df, province_gdf = load_and_prepare_data()

    # 合并2015年数据
    gdf_2015 = merge_data_with_gis(total_df, gdf_counties, mapping_df, 2015)

    # 处理缺失值
    gdf_2015['cumulative_branches'] = gdf_2015['cumulative_branches'].fillna(0)

    # 固定分级 - 4个档位（0单独处理为白色）
    breaks = [1, 5, 15, 40, 150]
    labels = ['1-4', '5-14', '15-39', '40+']

    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))

    # 明确将0值区域设为白色
    gdf_zero = gdf_2015[gdf_2015['cumulative_branches'] == 0].copy()
    gdf_zero.plot(color='white',
                  linewidth=0.1,
                  edgecolor='lightgray',
                  ax=ax)

    # 只对有数据的区域（>0）进行分级和着色
    gdf_with_data = gdf_2015[gdf_2015['cumulative_branches'] > 0].copy()

    print(f"有数据的县域数量: {len(gdf_with_data)}")
    print(f"0值的县域数量: {len(gdf_zero)}")
    print(f"总县域数量: {len(gdf_2015)}")

    if len(gdf_with_data) > 0:
        # 创建分级（只对有数据的区域）
        gdf_with_data['category'] = pd.cut(gdf_with_data['cumulative_branches'],
                                          bins=breaks,  # 现在breaks已经不包含0了
                                          labels=labels,
                                          include_lowest=True,
                                          right=False)

        # 使用颜色范围，最低档位稍浅一点，从0.4开始
        import matplotlib.colors as mcolors
        base_cmap = plt.cm.get_cmap('Blues')
        colors = base_cmap(np.linspace(0.4, 1.0, len(labels)))
        custom_cmap = mcolors.ListedColormap(colors)

        # 绘制有数据的区域
        gdf_with_data.plot(column='category',
                          cmap=custom_cmap,
                          linewidth=0.1,
                          edgecolor='white',
                          ax=ax,
                          legend=True,
                          legend_kwds={'title': '网点数量', 'loc': 'lower left', 'fontsize': 14, 'title_fontsize': 16})

    # 添加省界
    province_gdf.plot(ax=ax,
                      facecolor='none',
                      edgecolor='black',
                      linewidth=0.5,
                      alpha=0.8)

    # 设置标题
    ax.set_title('2015年PSBC累积网点数（新颜色方案测试）', fontsize=18, fontweight='bold', pad=20)
    ax.set_axis_off()

    # 保存图片
    output_file = f'{OUTPUT_DIR}/test_new_colors_2015.png'
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"测试地图已保存: {output_file}")

    # 显示分级统计
    if len(gdf_with_data) > 0:
        print("\n各分级的县域数量:")
        category_counts = gdf_with_data['category'].value_counts().sort_index()
        for category, count in category_counts.items():
            print(f"{category}: {count}个县域")

if __name__ == "__main__":
    create_test_map()
