#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
生成每个县每个时间段累积的允许贷款的PSBC网点数

此脚本读取PSBC.xlsx文件，处理数据，并生成一个包含每个县每年累积的允许贷款的网点数的CSV文件。
贷款网点的判断规则：
1. 2007年及之后开业的网点：
   - 业务描述中包含贷款相关关键词的网点
   - 或者是某个级别以上的网点（总行、一级分行、二级分行、支行等）
2. 2007年之前开业的网点：
   - 如果业务描述中包含贷款关键词或者是某个级别以上的网点，假设是2008年新增的贷款业务
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
from collections import defaultdict

# 设置文件路径
INPUT_FILE = './data/raw/PSBC.xlsx'
OUTPUT_FILE = './data/processed/psbc_loan_branches.csv'

# 定义贷款相关关键词
LOAN_KEYWORDS = ['贷款', '信贷', '借款', '融资', '小额', '信用']

# 定义高级别网点类型（假设这些网点都提供贷款服务）
HIGH_LEVEL_BRANCHES = ['总行', '总行营业部、专营机构', '一级分行', '一级分行营业部', 
                       '一级分行直属支行', '二级分行', '支行']

def main():
    print("开始处理PSBC贷款网点数据...")
    
    # 读取Excel文件
    df = read_and_preprocess_data(INPUT_FILE)
    
    # 标记允许贷款的网点
    df = mark_loan_branches(df)
    
    # 计算累积贷款网点数
    result_df = calculate_cumulative_loan_branches(df)
    
    # 保存结果
    save_results(result_df, OUTPUT_FILE)
    
    print(f"处理完成！结果已保存到 {OUTPUT_FILE}")

def read_and_preprocess_data(file_path):
    """读取并预处理PSBC数据"""
    print(f"读取数据文件: {file_path}")
    
    # 读取Excel文件
    df = pd.read_excel(file_path)
    
    # 重命名列以便更容易理解
    column_names = {
        'Unnamed: 0': 'id',
        'Unnamed: 1': 'bank_code',
        'Unnamed: 2': 'bank_name',
        'Unnamed: 3': 'status_code',
        'Unnamed: 4': 'open_date',
        'Unnamed: 5': 'close_date',
        'Unnamed: 6': 'unknown_1',
        'Unnamed: 7': 'province',
        'Unnamed: 8': 'city',
        'Unnamed: 9': 'county',
        'Unnamed: 10': 'longitude',
        'Unnamed: 11': 'latitude',
        'Unnamed: 12': 'parent_bank',
        'Unnamed: 13': 'unknown_2',
        'Unnamed: 14': 'address',
        'Unnamed: 15': 'postal_code',
        'Unnamed: 16': 'unknown_3',
        'Unnamed: 17': 'unknown_4',
        'Unnamed: 18': 'bank_short_name',
        'Unnamed: 19': 'bank_type',
        'Unnamed: 20': 'bank_category',
        'Unnamed: 21': 'branch_type',
        'Unnamed: 22': 'region_code',
        'Unnamed: 23': 'regulatory_agency',
        'Unnamed: 24': 'description'  # 业务描述
    }
    df = df.rename(columns=column_names)
    
    # 转换日期列
    df['open_date'] = pd.to_datetime(df['open_date'], errors='coerce')
    df['open_year'] = df['open_date'].dt.year
    
    # 转换关闭日期列
    df['close_date'] = pd.to_datetime(df['close_date'], errors='coerce')
    df['close_year'] = df['close_date'].dt.year
    
    # 将region_code转换为整数形式的county_id
    df['county_id'] = df['region_code'].fillna(0).astype(int)
    
    print(f"数据预处理完成，共 {len(df)} 条记录")
    return df

def mark_loan_branches(df):
    """标记允许贷款的网点"""
    print("开始标记允许贷款的网点...")
    
    # 检查业务描述中是否包含贷款关键词
    df['has_loan_keyword'] = df['description'].str.contains('|'.join(LOAN_KEYWORDS), na=False)
    
    # 检查是否是高级别网点
    df['is_high_level'] = df['branch_type'].isin(HIGH_LEVEL_BRANCHES)
    
    # 标记允许贷款的网点及其开始提供贷款服务的年份
    def get_loan_service_year(row):
        # 2007年及之后开业的网点
        if row['open_year'] >= 2007:
            # 业务描述中包含贷款关键词或者是高级别网点
            if row['has_loan_keyword'] or row['is_high_level']:
                return row['open_year']  # 开业即提供贷款服务
            else:
                return None  # 不提供贷款服务
        # 2007年之前开业的网点
        else:
            # 业务描述中包含贷款关键词或者是高级别网点
            if row['has_loan_keyword'] or row['is_high_level']:
                return 2008  # 假设2008年开始提供贷款服务
            else:
                return None  # 不提供贷款服务
    
    df['loan_service_year'] = df.apply(get_loan_service_year, axis=1)
    
    # 统计允许贷款的网点数量
    loan_branches = df[df['loan_service_year'].notna()]
    print(f"允许贷款的网点数量: {len(loan_branches)} ({len(loan_branches)/len(df)*100:.2f}%)")
    
    # 统计2007年及之后开业且允许贷款的网点数量
    after_2007_loan = loan_branches[loan_branches['open_year'] >= 2007]
    print(f"2007年及之后开业且允许贷款的网点数量: {len(after_2007_loan)} ({len(after_2007_loan)/len(loan_branches)*100:.2f}%)")
    
    # 统计2007年之前开业但在2008年新增贷款业务的网点数量
    before_2007_loan = loan_branches[loan_branches['open_year'] < 2007]
    print(f"2007年之前开业但在2008年新增贷款业务的网点数量: {len(before_2007_loan)} ({len(before_2007_loan)/len(loan_branches)*100:.2f}%)")
    
    return df

def calculate_cumulative_loan_branches(df):
    """计算每个县每年的累积贷款网点数，考虑网点关闭情况"""
    print("开始计算累积贷款网点数...")
    
    # 确定年份范围
    min_year = 2007  # 从2007年开始计算
    max_year = df['open_year'].max()
    years = range(min_year, max_year + 1)
    print(f"年份范围: {min_year} 至 {max_year}")
    
    # 获取所有有效的county_id
    counties = df[df['county_id'] > 0]['county_id'].unique()
    print(f"有效的county_id数量: {len(counties)}")
    
    # 创建一个空的列表来存储结果
    result = []
    
    # 对每个县的每一年计算累积贷款网点数
    for county in counties:
        county_branches = df[df['county_id'] == county]
        
        for year in years:
            # 计算到该年为止开设的贷款网点数
            opened = len(county_branches[(county_branches['loan_service_year'].notna()) & 
                                        (county_branches['loan_service_year'] <= year)])
            
            # 计算到该年为止关闭的贷款网点数
            closed = len(county_branches[(county_branches['loan_service_year'].notna()) & 
                                        (county_branches['loan_service_year'] <= year) & 
                                        (county_branches['close_year'].notna()) & 
                                        (county_branches['close_year'] <= year)])
            
            # 计算实际运营的累积贷款网点数
            active_loan_branches = opened - closed
            
            # 将结果添加到列表中
            result.append({
                'county_id': county,
                'year': year,
                'opened_loan_branches': opened,
                'closed_loan_branches': closed,
                'cumulative_loan_branches': active_loan_branches
            })
    
    # 创建结果DataFrame
    result_df = pd.DataFrame(result)
    print(f"累积贷款网点数计算完成，共 {len(result_df)} 条记录")
    
    # 显示一些统计信息
    total_opened = result_df[result_df['year'] == max_year]['opened_loan_branches'].sum()
    total_closed = result_df[result_df['year'] == max_year]['closed_loan_branches'].sum()
    total_active = result_df[result_df['year'] == max_year]['cumulative_loan_branches'].sum()
    print(f"截至 {max_year} 年，总开设贷款网点数: {total_opened}, 总关闭贷款网点数: {total_closed}, 实际运营贷款网点数: {total_active}")
    
    return result_df

def save_results(df, file_path):
    """保存结果到CSV文件"""
    # 确保输出目录存在
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    # 保存结果
    df.to_csv(file_path, index=False)
    print(f"结果已保存到: {file_path}")
    
    # 显示一些统计信息
    print(f"结果包含 {df['county_id'].nunique()} 个县的数据")
    print(f"结果包含 {df['year'].nunique()} 个年份的数据")
    print(f"累积贷款网点数的范围: {df['cumulative_loan_branches'].min()} 至 {df['cumulative_loan_branches'].max()}")
    print(f"累积贷款网点数的平均值: {df['cumulative_loan_branches'].mean():.2f}")
    
    # 显示开设和关闭贷款网点的统计信息
    max_year = df['year'].max()
    year_data = df[df['year'] == max_year]
    print(f"\n截至 {max_year} 年的统计信息:")
    print(f"总开设贷款网点数: {year_data['opened_loan_branches'].sum()}")
    print(f"总关闭贷款网点数: {year_data['closed_loan_branches'].sum()}")
    print(f"实际运营贷款网点数: {year_data['cumulative_loan_branches'].sum()}")
    if year_data['opened_loan_branches'].sum() > 0:
        print(f"关闭率: {year_data['closed_loan_branches'].sum() / year_data['opened_loan_branches'].sum() * 100:.2f}%")

if __name__ == "__main__":
    main()
