#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建完整的PSBC网点时间序列地图
- 累积开点图：1997-2015年（19张独立图 + 1张2×2对比图）
- 累积贷款点图：2007-2015年（9张独立图 + 1张2×2对比图）
"""

import pandas as pd
import geopandas as gpd
import matplotlib.pyplot as plt
import numpy as np
import warnings
import os
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 文件路径
TOTAL_BRANCHES_FILE = '/Users/<USER>/Downloads/CountyTFP/data/processed/psbc_cumulative_branches.csv'
LOAN_BRANCHES_FILE = '/Users/<USER>/Downloads/CountyTFP/data/processed/psbc_loan_branches.csv'
GIS_FILE = '/Users/<USER>/Downloads/CountyTFP/data/raw/2015county/2015county.shp'
ID_MAPPING_FILE = '/Users/<USER>/Downloads/CountyTFP/data/processed/psbc_gis_id_mapping_complete.csv'
PROVINCE_FILE = '/Users/<USER>/Downloads/CountyTFP/data/raw/Province/2. Province/province.shp'
OUTPUT_DIR = '/Users/<USER>/Downloads/CountyTFP/outputs/maps/time_series'

def load_and_prepare_data():
    """加载并准备数据"""
    print("正在加载数据...")

    # 读取网点数据
    total_df = pd.read_csv(TOTAL_BRANCHES_FILE)
    loan_df = pd.read_csv(LOAN_BRANCHES_FILE)

    # 读取GIS数据，只保留县域单位
    gdf = gpd.read_file(GIS_FILE)
    county_types = ['县', '县级市', '自治县', '旗', '自治旗', '特区', '林区']
    gdf_counties = gdf[gdf['县级类'].isin(county_types)].copy()

    # 读取ID映射表
    mapping_df = pd.read_csv(ID_MAPPING_FILE)

    # 读取省界数据
    province_gdf = gpd.read_file(PROVINCE_FILE)

    print(f"县域单位数量: {len(gdf_counties)}")

    return total_df, loan_df, gdf_counties, mapping_df, province_gdf

def merge_data_with_gis(data_df, gdf_counties, mapping_df, year):
    """将网点数据与GIS数据合并"""
    # 筛选指定年份的数据
    year_data = data_df[data_df['year'] == year].copy()
    year_data['county_id'] = year_data['county_id'].astype(str)

    # 确保mapping_df中的ID都是字符串
    mapping_df = mapping_df.copy()
    mapping_df['psbc_id'] = mapping_df['psbc_id'].astype(str)
    mapping_df['gis_id'] = mapping_df['gis_id'].astype(str)

    # 与ID映射表合并
    merged_data = pd.merge(year_data, mapping_df,
                          left_on='county_id', right_on='psbc_id',
                          how='inner')

    # 确保GIS数据中的区划码也是字符串
    gdf_counties = gdf_counties.copy()
    gdf_counties['区划码'] = gdf_counties['区划码'].astype(str)

    # 与GIS数据合并
    final_data = pd.merge(gdf_counties, merged_data,
                         left_on='区划码', right_on='gis_id',
                         how='left')

    return final_data

def get_fixed_color_breaks(data_type='total'):
    """设置固定的颜色分级断点 - 4个档位"""
    if data_type == 'total':
        # 总网点数的4个档位 - 0单独处理为白色
        breaks = [1, 5, 15, 40, 150]  # 4个断点，5个区间（包括0）
        labels = ['1-4', '5-14', '15-39', '40+']  # 4个标签对应4个区间
    else:  # loan
        # 贷款网点数的4个档位 - 0单独处理为白色
        breaks = [1, 3, 8, 20, 80]  # 4个断点，5个区间（包括0）
        labels = ['1-2', '3-7', '8-19', '20+']  # 4个标签对应4个区间

    return breaks, labels

def create_single_map(gdf_data, value_col, year, data_type, province_gdf, output_file):
    """创建单张地图"""
    # 获取固定的颜色分级
    breaks, labels = get_fixed_color_breaks(data_type)

    # 处理缺失值
    gdf_plot = gdf_data.copy()
    gdf_plot[value_col] = gdf_plot[value_col].fillna(0)

    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))

    # 选择颜色方案
    cmap = 'Blues' if data_type == 'total' else 'Reds'

    # 明确将0值区域设为白色
    gdf_zero = gdf_plot[gdf_plot[value_col] == 0].copy()
    gdf_zero.plot(color='white',
                  linewidth=0.1,
                  edgecolor='lightgray',
                  ax=ax)

    # 只对有数据的区域（>0）进行分级和着色
    gdf_with_data = gdf_plot[gdf_plot[value_col] > 0].copy()

    if len(gdf_with_data) > 0:
        # 创建分级（只对有数据的区域）
        gdf_with_data['category'] = pd.cut(gdf_with_data[value_col],
                                          bins=breaks,  # 现在breaks已经不包含0了
                                          labels=labels,
                                          include_lowest=True,
                                          right=False)

        # 使用颜色范围，最低档位稍浅一点，从0.4开始
        import matplotlib.colors as mcolors
        base_cmap = plt.cm.get_cmap(cmap)
        # 创建从0.4到1.0的颜色映射，最低档位稍浅但仍与白色区分
        colors = base_cmap(np.linspace(0.4, 1.0, len(labels)))
        custom_cmap = mcolors.ListedColormap(colors)

        # 绘制有数据的区域
        gdf_with_data.plot(column='category',
                          cmap=custom_cmap,
                          linewidth=0.1,
                          edgecolor='white',
                          ax=ax,
                          legend=True,
                          legend_kwds={'title': '网点数量', 'loc': 'lower left', 'fontsize': 14, 'title_fontsize': 16})

    # 添加省界
    province_gdf.plot(ax=ax,
                      facecolor='none',
                      edgecolor='black',
                      linewidth=0.5,
                      alpha=0.8)

    # 设置标题
    title_prefix = 'PSBC累积网点数' if data_type == 'total' else 'PSBC累积贷款网点数'
    ax.set_title(f'{year}年{title_prefix}', fontsize=18, fontweight='bold', pad=20)
    ax.set_axis_off()

    # 保存图片
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()

def create_comparison_map(gdf_data_list, years, value_col, data_type, province_gdf, output_file):
    """创建2×2对比地图"""
    # 获取固定的颜色分级
    breaks, labels = get_fixed_color_breaks(data_type)

    # 创建2×2子图
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    axes = axes.flatten()

    # 选择颜色方案
    cmap = 'Blues' if data_type == 'total' else 'Reds'

    for i, (gdf_data, year) in enumerate(zip(gdf_data_list, years)):
        ax = axes[i]

        # 处理缺失值
        gdf_plot = gdf_data.copy()
        gdf_plot[value_col] = gdf_plot[value_col].fillna(0)

        # 明确将0值区域设为白色
        gdf_zero = gdf_plot[gdf_plot[value_col] == 0].copy()
        gdf_zero.plot(color='white',
                      linewidth=0.1,
                      edgecolor='lightgray',
                      ax=ax)

        # 只对有数据的区域（>0）进行分级和着色
        gdf_with_data = gdf_plot[gdf_plot[value_col] > 0].copy()

        if len(gdf_with_data) > 0:
            # 创建分级（只对有数据的区域）
            gdf_with_data['category'] = pd.cut(gdf_with_data[value_col],
                                              bins=breaks,  # 现在breaks已经不包含0了
                                              labels=labels,
                                              include_lowest=True,
                                              right=False)

            # 使用颜色范围，最低档位稍浅一点，从0.4开始
            import matplotlib.colors as mcolors
            base_cmap = plt.cm.get_cmap(cmap)
            colors = base_cmap(np.linspace(0.4, 1.0, len(labels)))
            custom_cmap = mcolors.ListedColormap(colors)

            # 绘制有数据的区域
            gdf_with_data.plot(column='category',
                              cmap=custom_cmap,
                              linewidth=0.1,
                              edgecolor='white',
                              ax=ax,
                              legend=False)  # 单独添加图例

        # 添加省界
        province_gdf.plot(ax=ax,
                          facecolor='none',
                          edgecolor='black',
                          linewidth=0.5,
                          alpha=0.8)

        # 设置子图标题
        ax.set_title(f'{year}年', fontsize=16, fontweight='bold', pad=10)
        ax.set_axis_off()

    # 添加总标题
    title_prefix = 'PSBC累积网点数' if data_type == 'total' else 'PSBC累积贷款网点数'
    fig.suptitle(f'{title_prefix}时间对比', fontsize=20, fontweight='bold', y=0.95)

    # 添加统一的图例
    from matplotlib.patches import Patch
    import matplotlib.colors as mcolors
    # 使用与地图相同的颜色范围，从0.4开始到1.0
    base_cmap = plt.cm.get_cmap(cmap)
    colors = base_cmap(np.linspace(0.4, 1.0, len(labels)))
    legend_elements = [Patch(facecolor=colors[i], label=labels[i]) for i in range(len(labels))]
    fig.legend(handles=legend_elements, title='网点数量', loc='center',
               bbox_to_anchor=(0.5, 0.02), ncol=len(labels), fontsize=14, title_fontsize=16)

    # 保存图片
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.1)
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """主函数"""
    # 创建输出目录
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # 加载数据
    total_df, loan_df, gdf_counties, mapping_df, province_gdf = load_and_prepare_data()

    print("\n" + "="*60)
    print("开始创建累积开点图 (1997-2015年)")
    print("="*60)

    # 创建累积开点图 (1997-2015年)
    total_years = list(range(1997, 2016))
    for year in total_years:
        print(f"正在创建 {year} 年累积开点图...")
        gdf_year = merge_data_with_gis(total_df, gdf_counties, mapping_df, year)
        output_file = f'{OUTPUT_DIR}/psbc_total_{year}.png'
        create_single_map(gdf_year, 'cumulative_branches', year, 'total', province_gdf, output_file)

    # 创建累积开点2×2对比图 (1997, 2003, 2009, 2015)
    print("正在创建累积开点2×2对比图...")
    comparison_years = [1997, 2003, 2009, 2015]
    comparison_data = []
    for year in comparison_years:
        gdf_year = merge_data_with_gis(total_df, gdf_counties, mapping_df, year)
        comparison_data.append(gdf_year)

    create_comparison_map(comparison_data, comparison_years, 'cumulative_branches', 'total',
                         province_gdf, f'{OUTPUT_DIR}/psbc_total_comparison.png')

    print("\n" + "="*60)
    print("开始创建累积贷款点图 (2007-2015年)")
    print("="*60)

    # 创建累积贷款点图 (2007-2015年)
    loan_years = list(range(2007, 2016))
    for year in loan_years:
        print(f"正在创建 {year} 年累积贷款点图...")
        gdf_year = merge_data_with_gis(loan_df, gdf_counties, mapping_df, year)
        output_file = f'{OUTPUT_DIR}/psbc_loan_{year}.png'
        create_single_map(gdf_year, 'cumulative_loan_branches', year, 'loan', province_gdf, output_file)

    # 创建累积贷款点2×2对比图 (2007, 2009, 2012, 2015)
    print("正在创建累积贷款点2×2对比图...")
    loan_comparison_years = [2007, 2009, 2012, 2015]
    loan_comparison_data = []
    for year in loan_comparison_years:
        gdf_year = merge_data_with_gis(loan_df, gdf_counties, mapping_df, year)
        loan_comparison_data.append(gdf_year)

    create_comparison_map(loan_comparison_data, loan_comparison_years, 'cumulative_loan_branches', 'loan',
                         province_gdf, f'{OUTPUT_DIR}/psbc_loan_comparison.png')

    print("\n" + "="*60)
    print("所有地图创建完成！")
    print(f"输出目录: {OUTPUT_DIR}")
    print(f"累积开点图: {len(total_years)} 张独立图 + 1张对比图")
    print(f"累积贷款点图: {len(loan_years)} 张独立图 + 1张对比图")
    print(f"总计: {len(total_years) + len(loan_years) + 2} 张地图")
    print("="*60)

if __name__ == "__main__":
    main()
