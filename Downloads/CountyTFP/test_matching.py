import pandas as pd
import geopandas as gpd
import networkx as nx
from pathlib import Path

def find_id_nodes_in_time_range(target_code, year, time_window=3):
    """
    在指定时间窗口内查找与目标代码相关的所有节点
    
    Parameters:
    -----------
    target_code : str
        目标代码
    year : int
        目标年份
    time_window : int, default=3
        时间窗口大小（年）
    
    Returns:
    --------
    list
        相关节点列表
    """
    # 构建有向图
    G = nx.DiGraph()
    
    # 读取变迁记录
    transitions_file = Path("/Users/<USER>/Downloads/CountyTFP/notebooks/exploratory/all_transitions_outputs.txt")
    with open(transitions_file, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():
                parts = line.strip().split(' -> ')
                if len(parts) == 2:
                    old_code, new_code = parts
                    G.add_edge(old_code, new_code)
    
    # 找到所有相关节点
    related_nodes = set()
    
    # 向前查找（新代码）
    for node in G.nodes():
        if node.startswith(target_code[:2]):  # 检查前两位是否相同
            try:
                node_year = int(node[2:4])
                if abs(node_year - year) <= time_window:
                    if nx.has_path(G, target_code, node) or nx.has_path(G, node, target_code):
                        related_nodes.add(node)
            except ValueError:
                continue
    
    return list(related_nodes)

def test_matching():
    # 读取数据
    base_dir = Path("/Users/<USER>/Downloads/CountyTFP")
    
    # 根据您的说明更新文件路径：
    # IO TFP 在 raw 下
    econ_file = base_dir / "data/raw/merged_io_tfp.parquet"
    # GIS 在 processed 下  
    gis_file = base_dir / "data/processed/gis.geojson"
    # 变迁记录在 notebooks 下
    transitions_file = base_dir / "notebooks/exploratory/all_transitions_outputs.txt"
    
    print(f"经济数据文件路径: {econ_file}")
    print(f"GIS数据文件路径: {gis_file}")
    print(f"变迁记录文件路径: {transitions_file}")
    
    # 读取GIS数据
    gis_data = gpd.read_file(gis_file)
    print(f"GIS数据列名: {gis_data.columns.tolist()}")
    
    # 读取经济数据
    econ_data = pd.read_parquet(econ_file)
    print(f"经济数据列名: {econ_data.columns.tolist()}")
    
    # 测试一个年份的匹配
    test_year = 2000
    print(f"\n测试{test_year}年的匹配:")
    
    # 获取该年的经济数据ID (使用正确的列名 countyid)
    year_econ_ids = econ_data[econ_data['year'] == test_year]['countyid'].unique()
    print(f"该年经济数据ID数量: {len(year_econ_ids)}")
    
    # 获取GIS数据ID (使用正确的列名 CountyID)
    gis_ids = gis_data['CountyID'].unique()
    print(f"GIS数据ID数量: {len(gis_ids)}")
    
    # 测试一个ID的匹配
    test_id = str(year_econ_ids[0])
    print(f"\n测试ID {test_id} 的匹配:")
    
    # 使用find_id_nodes_in_time_range查找相关节点
    related_nodes = find_id_nodes_in_time_range(test_id, test_year)
    print(f"找到的相关节点: {related_nodes}")
    
    # 检查这些节点是否在GIS数据中
    matches = [node for node in related_nodes if node in gis_ids]
    print(f"在GIS数据中找到的匹配: {matches}")

if __name__ == "__main__":
    test_matching() 