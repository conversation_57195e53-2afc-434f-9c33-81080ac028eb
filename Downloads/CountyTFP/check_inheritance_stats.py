import pandas as pd
import geopandas as gpd
import networkx as nx
from networkx.readwrite.gpickle import write_gpickle, read_gpickle
import os
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

# 设置基础路径
BASE_DIR = Path('/Users/<USER>/Downloads/CountyTFP')

def standardize_name(name):
    """
    标准化县名，去掉通用后缀
    """
    if not isinstance(name, str):
        return ""
    # 去掉通用后缀
    suffixes = ['县', '市', '区', '旗', '自治县', '自治旗']
    for suffix in suffixes:
        if name.endswith(suffix):
            return name[:-len(suffix)]
    return name

def find_matches_by_name_and_prefix(econ_id, econ_name, gis_df):
    """
    通过前两位和名字进行匹配
    """
    matches = []
    prefix = econ_id[:2]
    std_econ_name = standardize_name(econ_name)
    
    # 筛选同一省份的GIS数据
    gis_same_province = gis_df[gis_df['CountyID'].str.startswith(prefix)]
    
    for _, gis_row in gis_same_province.iterrows():
        std_gis_name = standardize_name(gis_row['CountyName'])
        # 检查名字是否包含关系
        if std_econ_name in std_gis_name or std_gis_name in std_econ_name:
            matches.append(gis_row['CountyID'])
    
    return matches

def find_matches_by_lineage(econ_id, complete_graph, gis_ids):
    """
    通过演化链条查找匹配
    """
    matches = []
    # 获取所有相关节点
    all_nodes = set()
    for node in complete_graph.nodes():
        if node[0] == econ_id:
            all_nodes.add(node)
            # 查找祖先和后代
            ancestors = nx.ancestors(complete_graph, node)
            descendants = nx.descendants(complete_graph, node)
            all_nodes.update(ancestors)
            all_nodes.update(descendants)
    
    # 检查每个节点是否与GIS ID有直接对应关系
    for node in all_nodes:
        node_id = node[0]
        if node_id in gis_ids:
            # 检查是否是一一对应关系
            is_one_to_one = True
            for other_node in all_nodes:
                if other_node[0] != node_id and other_node[0] in gis_ids:
                    is_one_to_one = False
                    break
            if is_one_to_one:
                matches.append(node_id)
    
    return matches

def find_id_nodes_in_time_range(target_code, year=None, time_window=3):
    """
    找到与目标代码相关的所有节点（不考虑时间窗口）
    
    Args:
        target_code: 目标代码
        year: 目标年份（可选）
        time_window: 时间窗口（可选，默认3年）
    
    Returns:
        set: 所有相关节点的集合
    """
    # 确保target_code是字符串
    target_code = str(target_code)
    
    # 找到所有与目标代码相关的节点
    related_nodes = set()
    
    # 检查所有可能的路径关系
    for node in G.nodes():
        # 检查从目标到当前节点的路径
        if nx.has_path(G, target_code, node):
            related_nodes.add(node)
        # 检查从当前节点到目标的路径
        if nx.has_path(G, node, target_code):
            related_nodes.add(node)
    
    return related_nodes

def find_matches_for_year(gis_data, econ_data, year, complete_graph):
    """为特定年份匹配GIS ID和经济ID"""
    # 获取该年份的经济数据
    year_data = econ_data[econ_data['year'] == year].copy()
    
    # 初始化结果
    matches = []
    used_gis_ids = set()  # 记录已使用的GIS ID
    
    # 1. 直接ID匹配
    for _, row in year_data.iterrows():
        econ_id = str(row['countyid'])  # 确保是字符串
        county_name = row['county_name']
        
        # 在GIS数据中查找匹配
        gis_match = gis_data[gis_data['CountyID'] == econ_id]
        
        if not gis_match.empty and gis_match.iloc[0]['CountyID'] not in used_gis_ids:
            matches.append({
                'year': year,
                'gis_id': gis_match.iloc[0]['CountyID'],
                'econ_id': econ_id,
                'match_type': 'direct_id',
                'county_name': county_name
            })
            used_gis_ids.add(gis_match.iloc[0]['CountyID'])
    
    # 2. 名称匹配（对于未匹配的ID）
    unmatched = year_data[~year_data['countyid'].astype(str).isin([m['econ_id'] for m in matches])]
    for _, row in unmatched.iterrows():
        econ_id = str(row['countyid'])
        county_name = row['county_name']
        
        # 在未使用的GIS ID中查找名称匹配
        available_gis = gis_data[~gis_data['CountyID'].isin(used_gis_ids)]
        name_matches = available_gis[available_gis['CountyName'] == county_name]
        
        if not name_matches.empty:
            matches.append({
                'year': year,
                'gis_id': name_matches.iloc[0]['CountyID'],
                'econ_id': econ_id,
                'match_type': 'name',
                'county_name': county_name
            })
            used_gis_ids.add(name_matches.iloc[0]['CountyID'])
    
    # 3. 沿革链匹配（对于仍未匹配的ID）
    still_unmatched = year_data[~year_data['countyid'].astype(str).isin([m['econ_id'] for m in matches])]
    for _, row in still_unmatched.iterrows():
        econ_id = str(row['countyid'])
        county_name = row['county_name']
        
        # 获取该ID的所有相关节点
        related_nodes = find_id_nodes_in_time_range(complete_graph, econ_id)
        
        # 在未使用的GIS ID中查找沿革链匹配
        available_gis = gis_data[~gis_data['CountyID'].isin(used_gis_ids)]
        for _, gis_row in available_gis.iterrows():
            gis_id = gis_row['CountyID']
            if gis_id in related_nodes:
                matches.append({
                    'year': year,
                    'gis_id': gis_id,
                    'econ_id': econ_id,
                    'match_type': 'lineage',
                    'county_name': county_name
                })
                used_gis_ids.add(gis_id)
                break
    
    return matches

def check_complete_graph():
    """
    检查complete_graph是否在内存中
    """
    if 'complete_graph' not in globals():
        print("错误：complete_graph不在内存中")
        return False
    
    if not isinstance(complete_graph, nx.DiGraph):
        print("错误：complete_graph不是DiGraph类型")
        return False
    
    print(f"complete_graph信息:")
    print(f"  节点数: {complete_graph.number_of_nodes()}")
    print(f"  边数: {complete_graph.number_of_edges()}")
    return True

def save_complete_graph(graph, file_path):
    """保存complete_graph到文件"""
    print(f"正在保存complete_graph到: {file_path}")
    write_gpickle(graph, file_path)
    print("保存完成")

def load_complete_graph(file_path):
    """从文件加载complete_graph"""
    print(f"正在从文件加载complete_graph: {file_path}")
    try:
        graph = read_gpickle(file_path)
        print(f"加载成功: {graph.number_of_nodes()} 个节点, {graph.number_of_edges()} 条边")
        return graph
    except Exception as e:
        print(f"加载失败: {e}")
        return None

def build_complete_change_graph(transitions_file):
    """从变迁记录构建完整的变迁图"""
    G = nx.DiGraph()
    
    with open(transitions_file, 'r') as f:
        for line in f:
            if line.startswith('Transition:'):
                parts = line.strip().split(' -> ')
                if len(parts) == 2:
                    old_id = parts[0].split(': ')[1]
                    new_id = parts[1]
                    G.add_edge(old_id, new_id)
    
    return G

def main():
    # 设置文件路径
    base_dir = '/Users/<USER>/Downloads/CountyTFP'
    gis_file = os.path.join(base_dir, 'data/raw/gis/County_2010_2020.geojson')
    econ_file = os.path.join(base_dir, 'data/raw/economic/economic_data.csv')
    transitions_file = os.path.join(base_dir, 'notebooks/exploratory/all_transitions_outputs.txt')
    
    # 读取数据
    print("读取数据...")
    gis_data = gpd.read_file(gis_file)
    econ_data = pd.read_csv(econ_file)
    
    # 构建完整变迁图
    print("构建完整变迁图...")
    complete_graph = build_complete_change_graph(transitions_file)
    
    # 获取所有年份
    years = sorted(econ_data['year'].unique())
    
    # 按年份进行匹配
    print("\n按年份进行匹配...")
    all_matches = []
    for year in years:
        print(f"\n处理{year}年...")
        matches = find_matches_for_year(gis_data, econ_data, year, complete_graph)
        all_matches.extend(matches)
        
        # 输出该年份的匹配统计
        total_ids = len(econ_data[econ_data['year'] == year])
        matched_ids = len(matches)
        match_rate = matched_ids / total_ids * 100
        
        print(f"{year}年匹配统计:")
        print(f"总ID数: {total_ids}")
        print(f"匹配成功数: {matched_ids}")
        print(f"匹配率: {match_rate:.2f}%")
        
        # 输出匹配类型统计
        match_types = pd.DataFrame(matches)['match_type'].value_counts()
        print("\n匹配类型统计:")
        for match_type, count in match_types.items():
            print(f"{match_type}: {count}")
    
    # 输出总体统计
    print("\n总体匹配统计:")
    total_matches = len(all_matches)
    total_ids = len(econ_data)
    overall_match_rate = total_matches / total_ids * 100
    
    print(f"总匹配数: {total_matches}")
    print(f"总ID数: {total_ids}")
    print(f"总体匹配率: {overall_match_rate:.2f}%")
    
    # 输出匹配类型统计
    match_types = pd.DataFrame(all_matches)['match_type'].value_counts()
    print("\n总体匹配类型统计:")
    for match_type, count in match_types.items():
        print(f"{match_type}: {count}")

def plot_yearly_id_counts(df, output_path):
    """
    统计并绘制每年的唯一ID数量
    """
    # 按年份统计唯一ID数量
    yearly_counts = df.groupby('year')['countyid'].nunique().reset_index()
    yearly_counts.columns = ['year', 'unique_ids']
    
    # 创建图表
    plt.figure(figsize=(15, 8))
    
    # 绘制柱状图
    plt.bar(yearly_counts['year'], yearly_counts['unique_ids'], 
            color='skyblue', edgecolor='black')
    
    # 添加数值标签
    for i, v in enumerate(yearly_counts['unique_ids']):
        plt.text(yearly_counts['year'][i], v, str(v), 
                ha='center', va='bottom')
    
    plt.title('每年唯一ID数量统计')
    plt.xlabel('年份')
    plt.ylabel('唯一ID数量')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.xticks(rotation=45)
    
    # 保存图片
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    return yearly_counts

def plot_incomplete_timeline(timeline_df, output_path):
    """
    绘制时间序列不完整的ID的时间线
    """
    plt.figure(figsize=(15, 8))
    
    # 为每个ID绘制一条水平线
    for idx, row in timeline_df.iterrows():
        plt.hlines(y=idx, xmin=row['start_year'], xmax=row['end_year'], 
                  colors='blue', linewidth=2)
        # 在开始和结束点添加标记
        plt.plot(row['start_year'], idx, 'o', color='green', markersize=8)
        plt.plot(row['end_year'], idx, 'o', color='red', markersize=8)
        # 添加ID标签
        plt.text(row['start_year']-1, idx, row['countyid'], 
                verticalalignment='center', fontsize=8)
    
    plt.title('江苏省时间序列不完整的ID时间线')
    plt.xlabel('年份')
    plt.ylabel('ID索引')
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 保存图片
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

def find_inheritance_relationships(df, complete_graph, province_code='32', time_window=3):
    """
    查找继承关系并统计匹配情况
    
    参数:
    - df: 包含countyid和year列的DataFrame
    - complete_graph: 完整的演化图
    - province_code: 省份代码（默认'32'为江苏省）
    - time_window: 时间窗口（默认3年）
    
    返回:
    - 匹配结果统计
    """
    # 确保countyid是字符串格式
    df['countyid'] = df['countyid'].astype(str).str.zfill(6)
    
    # 筛选指定省份的数据
    province_df = df[df['countyid'].str.startswith(province_code)]
    
    # 获取所有年份
    all_years = sorted(province_df['year'].unique())
    year_range = max(all_years) - min(all_years) + 1
    
    # 对每个countyid进行分组，获取最早和最晚年份
    timeline_df = province_df.groupby('countyid').agg({
        'year': ['min', 'max', 'count']
    }).reset_index()
    
    # 重命名列
    timeline_df.columns = ['countyid', 'start_year', 'end_year', 'year_count']
    
    # 计算每个ID的持续时间
    timeline_df['duration'] = timeline_df['end_year'] - timeline_df['start_year'] + 1
    
    # 找出时间序列不完整的ID（持续时间小于总年份范围）
    incomplete_ids = timeline_df[timeline_df['duration'] < year_range].copy()
    complete_ids = timeline_df[timeline_df['duration'] == year_range].copy()
    
    # 按结束年份排序
    incomplete_ids = incomplete_ids.sort_values('end_year')
    
    # 查找潜在的继承关系
    matched_ids = set()  # 记录已匹配的ID
    inheritance_pairs = []  # 记录继承关系对
    potential_pairs = []  # 记录潜在的匹配对（仅考虑时间窗口）
    
    for i, row1 in incomplete_ids.iterrows():
        if row1['countyid'] in matched_ids:
            continue
            
        old_id = row1['countyid']
        old_end_year = row1['end_year']
        
        # 查找时间窗口内的其他ID
        for j, row2 in incomplete_ids.iterrows():
            if i == j or row2['countyid'] in matched_ids:
                continue
                
            new_id = row2['countyid']
            new_start_year = row2['start_year']
            
            # 检查时间窗口
            if abs(new_start_year - old_end_year) <= time_window:
                # 记录潜在匹配对
                potential_pairs.append({
                    'old_id': old_id,
                    'new_id': new_id,
                    'old_end_year': old_end_year,
                    'new_start_year': new_start_year,
                    'time_gap': new_start_year - old_end_year
                })
                
                # 使用find_id_nodes_in_time_range查找节点
                old_nodes = find_id_nodes_in_time_range(old_id, old_end_year - time_window, old_end_year + time_window)
                new_nodes = find_id_nodes_in_time_range(new_id, new_start_year - time_window, new_start_year + time_window)
                
                # 检查是否有共同的时间戳
                for old_node in old_nodes:
                    for new_node in new_nodes:
                        if complete_graph.has_edge(old_node, new_node):
                            edge_data = complete_graph.get_edge_data(old_node, new_node)
                            chain_year = int(edge_data.get('year', 0))
                            
                            # 检查演化时间是否也在时间窗口内
                            if abs(chain_year - old_end_year) <= time_window:
                                inheritance_pairs.append({
                                    'old_id': old_id,
                                    'new_id': new_id,
                                    'old_end_year': old_end_year,
                                    'new_start_year': new_start_year,
                                    'chain_year': chain_year
                                })
                                matched_ids.add(old_id)
                                matched_ids.add(new_id)
                                break
    
    # 统计信息
    total_incomplete_ids = len(incomplete_ids)
    matched_count = len(matched_ids)
    remaining_count = total_incomplete_ids - matched_count
    match_rate = matched_count / total_incomplete_ids if total_incomplete_ids > 0 else 0
    
    stats = {
        'total_complete_ids': len(complete_ids),
        'total_incomplete_ids': total_incomplete_ids,
        'matched_pairs': len(inheritance_pairs),
        'matched_ids': matched_count,
        'remaining_ids': remaining_count,
        'match_rate': match_rate,
        'inheritance_pairs': inheritance_pairs,
        'potential_pairs': potential_pairs,
        'incomplete_timeline_df': incomplete_ids,
        'complete_timeline_df': complete_ids,
        'year_range': year_range
    }
    
    return stats

def count_yearly_unique_ids():
    # 设置文件路径
    merged_io_tfp_path = BASE_DIR / 'data' / 'processed' / 'merged_io_tfp.parquet'
    
    # 读取数据
    print("正在读取数据...")
    df = pd.read_parquet(merged_io_tfp_path)
    
    # 按年份统计唯一ID数量
    yearly_counts = df.groupby('year')['countyid'].nunique().sort_index()
    
    # 打印结果
    print("\n每年的唯一ID数量:")
    for year, count in yearly_counts.items():
        print(f"{year}: {count}")

def check_id_ending_with_01():
    # 设置文件路径
    merged_io_tfp_path = BASE_DIR / 'data' / 'processed' / 'merged_io_tfp.parquet'
    
    # 读取数据
    print("正在读取数据...")
    df = pd.read_parquet(merged_io_tfp_path)
    
    # 确保countyid是字符串格式
    df['countyid'] = df['countyid'].astype(str).str.zfill(6)
    
    # 筛选尾号为01的ID
    id_01 = df[df['countyid'].str.endswith('01')]
    
    # 获取唯一的ID和县名组合
    unique_ids = id_01[['countyid', 'county_name']].drop_duplicates().sort_values('countyid')
    
    # 打印结果
    print("\n尾号为01的ID及其对应的县名:")
    for _, row in unique_ids.iterrows():
        print(f"ID: {row['countyid']}, 县名: {row['county_name']}")

if __name__ == "__main__":
    main()
    count_yearly_unique_ids()
    check_id_ending_with_01() 