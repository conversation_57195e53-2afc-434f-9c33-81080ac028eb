# 县级全要素生产率分析项目

本项目旨在对多个数据集进行数据探索和可视化分析，研究县级全要素生产率的影响因素和空间分布特征。

## 项目概述

本研究整合了银行、农业和地理空间等多维度数据，通过探索性数据分析和统计建模，揭示影响县级全要素生产率的关键因素及其空间分布模式。

## 数据来源

本项目使用以下主要数据集：

1. **IO.dta** - 输入输出数据
2. **PSBC.xlsx** - 邮政储蓄银行数据
3. **result.csv** - 结果数据
4. **bank_variables.dta** - 银行变量数据
5. **地理空间数据** - 县级行政区划和地理特征

## 项目结构

```
project/
├── data/                  # 数据目录
│   ├── raw/               # 原始数据
│   ├── interim/           # 中间处理数据
│   └── processed/         # 分析就绪数据
│
├── code/                  # 代码目录
│   ├── preprocessing/     # 数据预处理脚本
│   ├── features/          # 特征工程脚本
│   ├── analysis/          # 分析脚本
│   └── visualization/     # 可视化脚本
│
├── notebooks/             # Jupyter笔记本
│   ├── exploratory/       # 探索性分析笔记本
│   └── results/           # 结果展示笔记本
│
├── results/               # 结果目录
│   ├── figures/           # 图表和可视化
│   ├── tables/            # 统计表格
│   └── models/            # 保存的模型
│
├── docs/                  # 文档
│   ├── data_dictionary.md # 数据字典
│   ├── methodology.md     # 方法论文档
│   └── analysis_notes.md  # 分析笔记
│
├── references/            # 参考文献和外部资源
├── drafts/                # 草稿代码和实验性脚本
│
├── environment.yml        # 环境配置
├── Makefile               # 自动化工作流
└── README.md              # 项目概述
```

## 安装与设置

### 环境设置

```bash
# 创建conda环境
conda env create -f environment.yml

# 激活环境
conda activate county-tfp-analysis

# 更新环境
conda env update -f environment.yml
```

### 数据处理流程

```bash
# 运行完整分析流程
make all

# 或者分步运行
make data              # 加载数据
make process_raw       # 处理原始数据
make generate_features # 生成特征
make run_analysis      # 运行分析
make visualize         # 创建可视化
```

## 主要分析内容

1. **银行服务可及性分析**
   - 银行网点空间分布
   - 银行服务与经济发展关系

2. **农业生产效率分析**
   - 农业投入产出关系
   - 季节性变化模式

3. **地理因素影响分析**
   - 空间自相关检验
   - 地理加权回归

4. **综合生产率模型**
   - 多因素回归分析
   - 空间计量经济学模型

## 代码组织

### 预处理脚本

`code/preprocessing/` 目录包含数据加载和预处理脚本：

- `load_data.py` - 加载各种格式的数据文件
- `process_data.py` - 数据清洗和基本处理

### 特征工程

`code/features/` 目录包含特征创建脚本：

- `banking_features.py` - 银行相关特征
- `agriculture_features.py` - 农业相关特征
- `spatial_features.py` - 空间特征

### 分析脚本

`code/analysis/` 目录包含数据分析脚本：

- `descriptive_stats.py` - 描述性统计
- `regression_models.py` - 回归分析

### 可视化脚本

`code/visualization/` 目录包含可视化脚本：

- `plots.py` - 统计图表
- `maps.py` - 地理空间可视化

## 草稿代码

`drafts/` 目录用于存放有参考价值但不是最终方案的Python草稿文件。这些文件可能包含实验性代码或替代实现方法。

## 贡献指南

请参阅 `CONTRIBUTING.md` 文件了解如何为本项目做出贡献。

## 许可证

本项目采用 [MIT 许可证](LICENSE)。
