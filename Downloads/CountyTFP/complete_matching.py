import pandas as pd
import geopandas as gpd
import networkx as nx
from pathlib import Path
from collections import defaultdict
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def find_id_nodes_in_time_range(target_code, year, time_window=3):
    """
    在指定时间窗口内查找与目标代码相关的所有节点
    
    Parameters:
    -----------
    target_code : str
        目标代码
    year : int
        目标年份
    time_window : int, default=3
        时间窗口大小（年）
    
    Returns:
    --------
    list
        相关节点列表
    """
    # 构建有向图
    G = nx.DiGraph()
    
    # 读取变迁记录
    transitions_file = Path("/Users/<USER>/Downloads/CountyTFP/notebooks/exploratory/all_transitions_outputs.txt")
    with open(transitions_file, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():
                parts = line.strip().split(' -> ')
                if len(parts) == 2:
                    old_code, new_code = parts
                    G.add_edge(old_code, new_code)
    
    # 找到所有相关节点
    related_nodes = set()
    
    # 向前查找（新代码）
    for node in G.nodes():
        if node.startswith(target_code[:2]):  # 检查前两位是否相同
            try:
                node_year = int(node[2:4])
                if abs(node_year - year) <= time_window:
                    if nx.has_path(G, target_code, node) or nx.has_path(G, node, target_code):
                        related_nodes.add(node)
            except ValueError:
                continue
    
    return list(related_nodes)

def match_for_single_year(gis_data, econ_data, year):
    """
    为单个年份进行两步匹配（去掉沿革链匹配）
    
    Parameters:
    -----------
    gis_data : GeoDataFrame
        GIS数据
    econ_data : DataFrame
        经济数据
    year : int
        目标年份
    
    Returns:
    --------
    dict
        匹配结果统计
    """
    # 获取该年的数据
    year_econ = econ_data[econ_data['year'] == year].copy()
    
    # 初始化结果
    matches = []
    used_gis_ids = set()
    
    # 统计信息
    stats = {
        'year': year,
        'total_econ_ids': len(year_econ),
        'total_gis_ids': len(gis_data),
        'direct_matches': 0,
        'name_matches': 0,
        'total_matches': 0,
        'unmatched_econ_ids': [],
        'match_details': []
    }
    
    # 第一步：直接ID匹配
    print(f"  第一步：直接ID匹配...")
    for _, row in year_econ.iterrows():
        econ_id = str(row['countyid'])
        econ_name = row['county_name']
        
        # 在GIS数据中查找相同ID
        gis_match = gis_data[gis_data['CountyID'] == econ_id]
        if not gis_match.empty and econ_id not in used_gis_ids:
            gis_name = gis_match.iloc[0]['CountyName']
            matches.append({
                'econ_id': econ_id,
                'gis_id': econ_id,
                'econ_name': econ_name,
                'gis_name': gis_name,
                'match_type': 'direct_id'
            })
            used_gis_ids.add(econ_id)
            stats['direct_matches'] += 1
    
    # 第二步：名称匹配（未匹配的ID）
    print(f"  第二步：名称匹配...")
    matched_econ_ids = [m['econ_id'] for m in matches]
    unmatched_econ = year_econ[~year_econ['countyid'].astype(str).isin(matched_econ_ids)]
    
    for _, row in unmatched_econ.iterrows():
        econ_id = str(row['countyid'])
        econ_name = row['county_name']
        
        # 在未使用的GIS ID中查找名称匹配
        available_gis = gis_data[~gis_data['CountyID'].isin(used_gis_ids)]
        name_matches_gis = available_gis[available_gis['CountyName'] == econ_name]
        
        if not name_matches_gis.empty:
            # 如果有多个名称匹配，选择第一个
            gis_match = name_matches_gis.iloc[0]
            gis_id = gis_match['CountyID']
            gis_name = gis_match['CountyName']
            
            matches.append({
                'econ_id': econ_id,
                'gis_id': gis_id,
                'econ_name': econ_name,
                'gis_name': gis_name,
                'match_type': 'name'
            })
            used_gis_ids.add(gis_id)
            stats['name_matches'] += 1
        else:
            # 记录未匹配的ID
            stats['unmatched_econ_ids'].append({
                'econ_id': econ_id,
                'econ_name': econ_name
            })
    
    # 汇总统计
    stats['total_matches'] = len(matches)
    stats['match_details'] = matches
    stats['match_rate'] = stats['total_matches'] / stats['total_econ_ids'] * 100
    
    return stats

def plot_matches_by_period(all_results, gis_data, base_dir):
    """
    按五年期间绘制匹配结果地图
    
    Parameters:
    -----------
    all_results : list
        所有年份的匹配结果
    gis_data : GeoDataFrame
        GIS数据
    base_dir : Path
        基础目录
    """
    # 获取所有年份
    years = [r['year'] for r in all_results]
    min_year, max_year = min(years), max(years)
    
    # 按五年分组
    periods = []
    for start_year in range(min_year, max_year + 1, 5):
        end_year = min(start_year + 4, max_year)
        if start_year <= max_year:
            periods.append((start_year, end_year))
    
    print(f"\n开始绘制地图，共{len(periods)}个时期...")
    
    for start_year, end_year in periods:
        print(f"绘制 {start_year}-{end_year} 年期间的匹配地图...")
        
        # 收集该时期所有匹配的GIS ID
        period_matched_ids = set()
        period_direct_ids = set()
        period_name_ids = set()
        
        for result in all_results:
            if start_year <= result['year'] <= end_year:
                for match in result['match_details']:
                    period_matched_ids.add(match['gis_id'])
                    if match['match_type'] == 'direct_id':
                        period_direct_ids.add(match['gis_id'])
                    elif match['match_type'] == 'name':
                        period_name_ids.add(match['gis_id'])
        
        # 创建地图数据
        map_data = gis_data.copy()
        map_data['match_status'] = 'unmatched'
        map_data.loc[map_data['CountyID'].isin(period_direct_ids), 'match_status'] = 'direct'
        map_data.loc[map_data['CountyID'].isin(period_name_ids), 'match_status'] = 'name'
        
        # 创建图形
        fig, ax = plt.subplots(1, 1, figsize=(15, 12))
        
        # 绘制地图
        # 未匹配的区域 - 浅灰色
        unmatched = map_data[map_data['match_status'] == 'unmatched']
        if not unmatched.empty:
            unmatched.plot(ax=ax, color='lightgray', edgecolor='white', linewidth=0.5, alpha=0.7)
        
        # 直接匹配的区域 - 深蓝色
        direct_matched = map_data[map_data['match_status'] == 'direct']
        if not direct_matched.empty:
            direct_matched.plot(ax=ax, color='darkblue', edgecolor='white', linewidth=0.5)
        
        # 名称匹配的区域 - 橙色
        name_matched = map_data[map_data['match_status'] == 'name']
        if not name_matched.empty:
            name_matched.plot(ax=ax, color='orange', edgecolor='white', linewidth=0.5)
        
        # 设置标题和样式
        ax.set_title(f'{start_year}-{end_year}年 经济数据与GIS数据匹配结果', 
                    fontsize=16, fontweight='bold', pad=20)
        
        # 添加图例
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor='darkblue', label=f'直接ID匹配 ({len(period_direct_ids)}个区县)'),
            Patch(facecolor='orange', label=f'名称匹配 ({len(period_name_ids)}个区县)'),
            Patch(facecolor='lightgray', alpha=0.7, label='未匹配区域')
        ]
        ax.legend(handles=legend_elements, loc='upper right', fontsize=12)
        
        # 移除坐标轴
        ax.set_axis_off()
        
        # 添加统计信息文本
        total_matched = len(period_matched_ids)
        total_counties = len(gis_data)
        match_rate = total_matched / total_counties * 100
        
        stats_text = f"""匹配统计：
总区县数：{total_counties}
匹配区县数：{total_matched}
匹配率：{match_rate:.1f}%
直接匹配：{len(period_direct_ids)}
名称匹配：{len(period_name_ids)}"""
        
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=11,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        # 保存图片
        output_dir = base_dir / "data/processed/maps"
        output_dir.mkdir(exist_ok=True)
        
        filename = f"matching_map_{start_year}_{end_year}.png"
        filepath = output_dir / filename
        
        plt.tight_layout()
        plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        
        print(f"  地图已保存: {filepath}")

def run_complete_matching():
    """
    运行完整的分年匹配（仅前两步）
    """
    # 读取数据
    base_dir = Path("/Users/<USER>/Downloads/CountyTFP")
    
    # 文件路径
    econ_file = base_dir / "data/raw/merged_io_tfp.parquet"
    gis_file = base_dir / "data/processed/gis.geojson"
    
    print("读取数据...")
    print(f"经济数据文件: {econ_file}")
    print(f"GIS数据文件: {gis_file}")
    
    # 读取数据
    gis_data = gpd.read_file(gis_file)
    econ_data = pd.read_parquet(econ_file)
    
    print(f"GIS数据形状: {gis_data.shape}")
    print(f"经济数据形状: {econ_data.shape}")
    print(f"GIS数据列名: {gis_data.columns.tolist()}")
    print(f"经济数据列名: {econ_data.columns.tolist()}")
    
    # 获取所有年份
    years = sorted(econ_data['year'].unique())
    print(f"\n可用年份: {years}")
    
    # 分年匹配
    all_results = []
    
    for year in years:
        print(f"\n{'='*50}")
        print(f"处理 {year} 年")
        print(f"{'='*50}")
        
        # 进行匹配
        year_stats = match_for_single_year(gis_data, econ_data, year)
        all_results.append(year_stats)
        
        # 输出该年统计
        print(f"\n{year}年匹配统计:")
        print(f"  总经济数据ID数: {year_stats['total_econ_ids']}")
        print(f"  直接ID匹配: {year_stats['direct_matches']}")
        print(f"  名称匹配: {year_stats['name_matches']}")
        print(f"  总匹配数: {year_stats['total_matches']}")
        print(f"  未匹配数: {len(year_stats['unmatched_econ_ids'])}")
        print(f"  匹配率: {year_stats['match_rate']:.2f}%")
        
        # 显示一些匹配示例
        if year_stats['match_details']:
            print(f"\n  匹配示例（前3个）:")
            for i, match in enumerate(year_stats['match_details'][:3]):
                print(f"    {i+1}. {match['econ_id']} -> {match['gis_id']} "
                      f"({match['match_type']}) {match['econ_name']}")
    
    # 输出总体统计
    print(f"\n{'='*50}")
    print("总体匹配统计")
    print(f"{'='*50}")
    
    total_econ_ids = sum([r['total_econ_ids'] for r in all_results])
    total_matches = sum([r['total_matches'] for r in all_results])
    total_direct = sum([r['direct_matches'] for r in all_results])
    total_name = sum([r['name_matches'] for r in all_results])
    
    print(f"总经济数据ID数: {total_econ_ids}")
    print(f"总匹配数: {total_matches}")
    print(f"直接ID匹配总数: {total_direct}")
    print(f"名称匹配总数: {total_name}")
    print(f"总体匹配率: {total_matches/total_econ_ids*100:.2f}%")
    
    # 绘制地图
    plot_matches_by_period(all_results, gis_data, base_dir)
    
    # 保存结果
    results_df = pd.DataFrame([
        {
            'year': r['year'],
            'total_econ_ids': r['total_econ_ids'],
            'direct_matches': r['direct_matches'],
            'name_matches': r['name_matches'],
            'total_matches': r['total_matches'],
            'unmatched_count': len(r['unmatched_econ_ids']),
            'match_rate': r['match_rate']
        }
        for r in all_results
    ])
    
    output_file = base_dir / "data/processed/matching_results_summary.csv"
    results_df.to_csv(output_file, index=False)
    print(f"\n匹配结果汇总已保存到: {output_file}")
    
    # 保存详细匹配记录
    all_matches = []
    for r in all_results:
        for match in r['match_details']:
            match['year'] = r['year']
            all_matches.append(match)
    
    matches_df = pd.DataFrame(all_matches)
    detailed_output_file = base_dir / "data/processed/matching_results_detailed.csv"
    matches_df.to_csv(detailed_output_file, index=False)
    print(f"详细匹配记录已保存到: {detailed_output_file}")
    
    return all_results

if __name__ == "__main__":
    results = run_complete_matching() 