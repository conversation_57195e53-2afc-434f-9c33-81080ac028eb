# ==============================================
# 简化版：直接合并恢复候选县和过滤后县级数据并画地图
# ==============================================

import pandas as pd
import geopandas as gpd
import matplotlib.pyplot as plt

print("=== 开始合并数据 ===")

# 1. 检查现有变量
print("检查现有变量:")
print(f"✅ filtered_county_gdf: {len(filtered_county_gdf)} 个")
print(f"✅ recovery_candidates_final: {len(recovery_candidates_final)} 个")
print(f"✅ county_gdf: {len(county_gdf)} 个")

# 2. 从recovery_candidates_final提取恢复候选县的ID
recovery_ids = [candidate['gis_id'] for candidate in recovery_candidates_final]
print(f"恢复候选县ID数量: {len(recovery_ids)}")

# 3. 确定连接字段
print(f"filtered_county_gdf 的列: {list(filtered_county_gdf.columns)}")
print(f"county_gdf 的列: {list(county_gdf.columns)}")

# 自动判断ID字段
id_column = None
possible_id_columns = ['gis_id', 'CountyID', '区划码', 'adcode', 'code']
for col in possible_id_columns:
    if col in filtered_county_gdf.columns and col in county_gdf.columns:
        id_column = col
        break

if id_column is None:
    print("❌ 无法找到匹配的ID列")
    print(f"filtered_county_gdf 可用列: {list(filtered_county_gdf.columns)}")
    print(f"county_gdf 可用列: {list(county_gdf.columns)}")
else:
    print(f"✅ 使用ID字段: {id_column}")

# 4. 从county_gdf中提取恢复候选县的地理数据
recovery_data = county_gdf[county_gdf[id_column].isin(recovery_ids)].copy()
print(f"成功从county_gdf提取恢复候选县: {len(recovery_data)}")

# 5. 添加数据来源标记
filtered_county_gdf_marked = filtered_county_gdf.copy()
filtered_county_gdf_marked['data_source'] = 'original_filtered'

recovery_data['data_source'] = 'recovered_county'

# 6. 检查重复并移除
filtered_ids = set(filtered_county_gdf[id_column].astype(str))
recovery_ids_clean = recovery_data[~recovery_data[id_column].astype(str).isin(filtered_ids)].copy()
print(f"移除重复后的恢复县: {len(recovery_ids_clean)}")

# 7. 合并数据
final_county_df = gpd.GeoDataFrame(
    pd.concat([filtered_county_gdf_marked, recovery_ids_clean], ignore_index=True)
)

print(f"\n✅ 合并完成!")
print(f"原始过滤后: {len(filtered_county_gdf)} 个县")
print(f"恢复候选县: {len(recovery_candidates_final)} 个")
print(f"实际恢复: {len(recovery_ids_clean)} 个")
print(f"最终数据集: {len(final_county_df)} 个县")

coverage_increase = len(recovery_ids_clean)
improvement_pct = (coverage_increase / len(filtered_county_gdf)) * 100
print(f"覆盖范围提升: +{coverage_increase} 个县 (+{improvement_pct:.1f}%)")

# 8. 画地图对比
print(f"\n=== 创建对比地图 ===")

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 创建对比图
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))

# 左图：原始过滤后的数据
filtered_county_gdf.plot(
    ax=ax1,
    color='lightblue',
    edgecolor='white',
    linewidth=0.5,
    alpha=0.7
)
ax1.set_title(f'原始过滤后县级数据\n{len(filtered_county_gdf)} 个县', 
              fontsize=14, fontweight='bold')
ax1.set_axis_off()

# 右图：合并后的最终数据（不同颜色显示数据来源）
final_county_df[final_county_df['data_source'] == 'original_filtered'].plot(
    ax=ax2,
    color='lightblue',
    edgecolor='white',
    linewidth=0.5,
    alpha=0.7,
    label=f'原始过滤数据 ({len(filtered_county_gdf)}个)'
)

if len(recovery_ids_clean) > 0:
    final_county_df[final_county_df['data_source'] == 'recovered_county'].plot(
        ax=ax2,
        color='lightcoral',
        edgecolor='white',
        linewidth=0.5,
        alpha=0.7,
        label=f'恢复的县 ({len(recovery_ids_clean)}个)'
    )

ax2.set_title(f'合并后最终数据集\n{len(final_county_df)} 个县 (+{improvement_pct:.1f}%)', 
              fontsize=14, fontweight='bold')
ax2.legend(loc='upper right', fontsize=12)
ax2.set_axis_off()

plt.tight_layout()
plt.savefig('county_merge_comparison.png', dpi=300, bbox_inches='tight')
plt.show()

# 9. 数据来源统计
print(f"\n=== 最终数据集统计 ===")
source_counts = final_county_df['data_source'].value_counts()
print(f"数据来源分布:")
for source, count in source_counts.items():
    pct = (count / len(final_county_df)) * 100
    print(f"  {source}: {count} 个 ({pct:.1f}%)")

# 10. 保存最终数据集
print(f"\n=== 保存最终数据集 ===")
final_county_df.to_file("notebooks/shared_data/final_merged_counties.geojson", driver='GeoJSON')
print(f"✅ 最终数据集已保存: notebooks/shared_data/final_merged_counties.geojson")

# 创建没有geometry的CSV版本
final_df_no_geom = final_county_df.drop(columns=['geometry'])
final_df_no_geom.to_csv("notebooks/shared_data/final_merged_counties.csv", 
                        index=False, encoding='utf-8-sig')
print(f"✅ CSV版本已保存: notebooks/shared_data/final_merged_counties.csv")

print(f"\n🎉 县级数据合并完成！")
print(f"最终数据集: {len(final_county_df)} 个县级单位")
print(f"地图已保存: county_merge_comparison.png") 