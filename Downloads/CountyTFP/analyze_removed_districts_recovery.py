"""
分析被移除市辖区的历史名称记录
找出过去5年曾经不叫"xx区"且历史记录无歧义的地区，这些可以重新纳入分析
"""

import networkx as nx
from collections import defaultdict
import pandas as pd

def analyze_removed_districts_for_recovery(complete_graph, removed_district_ids, current_year=2024, lookback_years=5):
    """
    分析被移除市辖区的历史名称，找出可以重新纳入的地区
    
    参数:
    - complete_graph: 完整的历史变迁图
    - removed_district_ids: 被移除的市辖区ID列表
    - current_year: 当前年份
    - lookback_years: 回溯年数
    
    返回:
    - recovery_candidates: 可以重新纳入的候选地区
    - analysis_results: 详细分析结果
    """
    
    print(f"🔍 开始分析被移除市辖区的历史名称记录...")
    print(f"   目标时间范围: {current_year - lookback_years + 1}-{current_year} (过去{lookback_years}年)")
    print(f"   被移除市辖区数量: {len(removed_district_ids)}")
    print("="*80)
    
    recovery_candidates = []
    analysis_results = {
        'processed_districts': [],
        'found_in_graph': [],
        'not_found_in_graph': [],
        'had_non_district_names': [],
        'ambiguous_histories': [],
        'clean_recovery_candidates': []
    }
    
    start_year = current_year - lookback_years + 1
    
    for district_id in removed_district_ids:
        print(f"\n📋 分析区划码: {district_id}")
        analysis_results['processed_districts'].append(district_id)
        
        # 1. 找到该代码的所有历史节点
        historical_nodes = [node for node in complete_graph.nodes() if node[0] == district_id]
        
        if not historical_nodes:
            print(f"   ❌ 在历史图中未找到")
            analysis_results['not_found_in_graph'].append(district_id)
            continue
            
        print(f"   ✅ 找到历史节点: {len(historical_nodes)} 个")
        analysis_results['found_in_graph'].append(district_id)
        
        # 2. 获取完整演化谱系
        complete_lineage_nodes = set()
        
        for hist_node in historical_nodes:
            try:
                if complete_graph.has_node(hist_node):
                    ancestors = nx.ancestors(complete_graph, hist_node)
                    descendants = nx.descendants(complete_graph, hist_node)
                    lineage_nodes = {hist_node} | ancestors | descendants
                    complete_lineage_nodes.update(lineage_nodes)
            except nx.NetworkXError as e:
                print(f"   ⚠️  处理节点 {hist_node} 时出错: {e}")
                continue
        
        # 3. 筛选目标时间范围内的节点
        def is_in_target_range(node_tuple):
            try:
                node_year = int(node_tuple[2])
                return start_year <= node_year <= current_year
            except (ValueError, IndexError):
                return False
        
        target_period_nodes = [node for node in complete_lineage_nodes if is_in_target_range(node)]
        
        if not target_period_nodes:
            print(f"   📅 目标时间范围内无记录")
            continue
            
        print(f"   📅 目标时间范围内节点: {len(target_period_nodes)} 个")
        
        # 4. 分析历史名称
        target_period_names = []
        nodes_by_year = defaultdict(list)
        
        for node in target_period_nodes:
            code, name, year_str = node
            try:
                year = int(year_str)
                nodes_by_year[year].append((code, name))
                target_period_names.append((year, name))
            except ValueError:
                continue
        
        # 按年份排序显示
        target_period_names.sort()
        print(f"   📝 目标时间范围内的名称记录:")
        for year, name in target_period_names:
            suffix_info = "以'区'结尾" if name.endswith('区') else "不以'区'结尾"
            print(f"      {year}年: {name} ({suffix_info})")
        
        # 5. 检查是否有非"xx区"名称
        non_district_names = [(year, name) for year, name in target_period_names if not name.endswith('区')]
        
        if not non_district_names:
            print(f"   📊 结论: 目标时间范围内全部以'区'结尾，无法重新纳入")
            continue
            
        print(f"   ✨ 发现非'区'名称: {len(non_district_names)} 个")
        analysis_results['had_non_district_names'].append(district_id)
        
        for year, name in non_district_names:
            print(f"      {year}年: {name}")
        
        # 6. 检查历史记录的歧义性
        # 检查是否存在一年内多个不同名称或代码
        ambiguous = False
        ambiguity_details = []
        
        for year, year_nodes in nodes_by_year.items():
            unique_names = list(set([name for code, name in year_nodes]))
            unique_codes = list(set([code for code, name in year_nodes]))
            
            if len(unique_names) > 1:
                ambiguous = True
                ambiguity_details.append(f"{year}年有多个名称: {', '.join(unique_names)}")
            
            if len(unique_codes) > 1:
                ambiguous = True
                ambiguity_details.append(f"{year}年有多个代码: {', '.join(unique_codes)}")
        
        if ambiguous:
            print(f"   ⚠️  发现歧义记录:")
            analysis_results['ambiguous_histories'].append({
                'district_id': district_id,
                'ambiguity_details': ambiguity_details
            })
            for detail in ambiguity_details:
                print(f"      {detail}")
            print(f"   📊 结论: 存在歧义，不建议重新纳入")
            continue
        
        # 7. 符合条件的候选者
        print(f"   ✅ 符合重新纳入条件!")
        print(f"   📊 结论: 历史记录清晰，曾有非'区'名称，建议重新纳入")
        
        candidate_info = {
            'district_id': district_id,
            'target_period_nodes': target_period_nodes,
            'non_district_names': non_district_names,
            'all_names_in_period': target_period_names,
            'complete_lineage_size': len(complete_lineage_nodes)
        }
        
        recovery_candidates.append(candidate_info)
        analysis_results['clean_recovery_candidates'].append(candidate_info)
    
    # 8. 总结报告
    print(f"\n{'='*80}")
    print(f"🎯 **重新纳入分析总结报告**")
    print(f"{'='*80}")
    print(f"📊 处理的市辖区总数: {len(removed_district_ids)}")
    print(f"🔍 在历史图中找到: {len(analysis_results['found_in_graph'])}")
    print(f"❌ 在历史图中未找到: {len(analysis_results['not_found_in_graph'])}")
    print(f"✨ 有非'区'历史名称: {len(analysis_results['had_non_district_names'])}")
    print(f"⚠️  历史记录有歧义: {len(analysis_results['ambiguous_histories'])}")
    print(f"✅ **最终可重新纳入: {len(recovery_candidates)}**")
    
    if recovery_candidates:
        print(f"\n🎉 **推荐重新纳入的地区详情:**")
        for i, candidate in enumerate(recovery_candidates, 1):
            print(f"\n{i}. 区划码: {candidate['district_id']}")
            print(f"   非'区'名称记录: {len(candidate['non_district_names'])} 个")
            for year, name in candidate['non_district_names']:
                print(f"     - {year}年: {name}")
            print(f"   完整演化谱系规模: {candidate['complete_lineage_size']} 个节点")
    
    if analysis_results['ambiguous_histories']:
        print(f"\n⚠️  **歧义记录详情 (不建议纳入):**")
        for item in analysis_results['ambiguous_histories']:
            print(f"\n区划码: {item['district_id']}")
            for detail in item['ambiguity_details']:
                print(f"   - {detail}")
    
    return recovery_candidates, analysis_results


def generate_recovery_recommendations(recovery_candidates, analysis_results):
    """生成具体的重新纳入建议"""
    
    if not recovery_candidates:
        print("\n📄 无重新纳入建议")
        return []
    
    print(f"\n{'='*80}")
    print(f"📋 **具体重新纳入建议**")
    print(f"{'='*80}")
    
    recommendations = []
    
    for candidate in recovery_candidates:
        district_id = candidate['district_id']
        non_district_names = candidate['non_district_names']
        
        # 找出最近的非'区'名称
        latest_non_district = max(non_district_names, key=lambda x: x[0]) if non_district_names else None
        
        recommendation = {
            'district_id': district_id,
            'action': 'include',
            'reason': f"过去5年曾名为'{latest_non_district[1]}'({latest_non_district[0]}年)，历史记录清晰无歧义",
            'latest_non_district_name': latest_non_district[1] if latest_non_district else None,
            'latest_non_district_year': latest_non_district[0] if latest_non_district else None
        }
        
        recommendations.append(recommendation)
        
        print(f"✅ {district_id}: 建议重新纳入")
        print(f"   理由: {recommendation['reason']}")
    
    print(f"\n📊 **重新纳入统计:**")
    print(f"   可重新纳入的市辖区: {len(recommendations)} 个")
    print(f"   占原移除总数的比例: {len(recommendations)/len(analysis_results['processed_districts'])*100:.1f}%")
    
    return recommendations


# 使用示例函数
def example_usage():
    """使用示例"""
    print("🚀 **市辖区重新纳入分析系统**")
    print("基于历史名称记录的智能筛选")
    print("\n使用方法:")
    print("1. 确保已构建完整的 complete_graph")
    print("2. 提供被移除的市辖区ID列表")
    print("3. 调用分析函数:")
    print("   recovery_candidates, analysis_results = analyze_removed_districts_for_recovery(")
    print("       complete_graph, removed_district_ids)")
    print("4. 生成具体建议:")
    print("   recommendations = generate_recovery_recommendations(recovery_candidates, analysis_results)")

if __name__ == "__main__":
    example_usage() 