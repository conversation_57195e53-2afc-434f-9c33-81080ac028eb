# ==============================================
# 检查变量状态 - 查看filtered_county_gdf是否被修改
# ==============================================

print("=== 检查当前变量状态 ===")

# 1. 检查filtered_county_gdf的基本信息
print(f"1. filtered_county_gdf 当前状态:")
print(f"   类型: {type(filtered_county_gdf)}")
print(f"   长度: {len(filtered_county_gdf)}")
print(f"   列名: {list(filtered_county_gdf.columns)}")

# 2. 检查是否有data_source列（表明是否被修改过）
if 'data_source' in filtered_county_gdf.columns:
    print(f"⚠️  filtered_county_gdf 包含 'data_source' 列，说明已被修改!")
    print(f"   data_source 分布:")
    print(filtered_county_gdf['data_source'].value_counts())
else:
    print(f"✅ filtered_county_gdf 没有 'data_source' 列，应该是原始数据")

# 3. 检查county_gdf的状态
print(f"\n2. county_gdf 当前状态:")
print(f"   类型: {type(county_gdf)}")
print(f"   长度: {len(county_gdf)}")
print(f"   列名: {list(county_gdf.columns)[:10]}...")  # 只显示前10个列名

# 4. 检查是否存在备份变量
backup_vars = [var for var in globals().keys() if 'filtered' in var.lower() or 'backup' in var.lower()]
print(f"\n3. 检查是否有备份变量:")
if backup_vars:
    for var in backup_vars:
        try:
            var_value = globals()[var]
            if hasattr(var_value, '__len__') and hasattr(var_value, 'columns'):
                print(f"   {var}: {len(var_value)} 条记录")
        except:
            pass
else:
    print("   未发现备份变量")

# 5. 检查是否有原始保存的数据
import os
print(f"\n4. 检查保存的数据文件:")
shared_data_files = []
if os.path.exists("notebooks/shared_data"):
    files = os.listdir("notebooks/shared_data")
    for f in files:
        if 'filtered' in f.lower() or 'county' in f.lower():
            file_path = f"notebooks/shared_data/{f}"
            file_size = os.path.getsize(file_path)
            print(f"   {f}: {file_size/1024/1024:.2f} MB")
            shared_data_files.append(f)

# 6. 建议恢复原始数据的方法
print(f"\n5. 数据恢复建议:")
if len(filtered_county_gdf) > 1570:
    print(f"⚠️  当前 filtered_county_gdf 有 {len(filtered_county_gdf)} 条记录，超过预期的1570条")
    print(f"可能的原因:")
    print(f"   1. 变量被重新赋值（比如被final_county_df覆盖）")
    print(f"   2. 执行了某个会修改数据的操作")
    print(f"   3. 加载了不同的数据源")
    
    print(f"\n📋 恢复方案:")
    if 'filtered_county_gdf.geojson' in shared_data_files:
        print(f"   方案1: 从保存的文件重新加载")
        print(f"   ```python")
        print(f"   filtered_county_gdf_original = gpd.read_file('notebooks/shared_data/filtered_county_gdf.geojson')")
        print(f"   print(f'恢复的数据: {{len(filtered_county_gdf_original)}} 条记录')")
        print(f"   ```")
    
    print(f"   方案2: 重新执行过滤操作")
    print(f"   ```python")
    print(f"   # 重新从county_gdf执行remove_districts_within_city_centers()函数")
    print(f"   ```")
    
    if 'data_source' in filtered_county_gdf.columns:
        print(f"   方案3: 从当前数据中提取原始部分")
        print(f"   ```python")
        print(f"   filtered_county_gdf_clean = filtered_county_gdf[")
        print(f"       filtered_county_gdf['data_source'] == 'original_filtered']")
        print(f"   filtered_county_gdf_clean = filtered_county_gdf_clean.drop(columns=['data_source'])")
        print(f"   print(f'清理后的数据: {{len(filtered_county_gdf_clean)}} 条记录')")
        print(f"   ```")

else:
    print(f"✅ filtered_county_gdf 数量正常: {len(filtered_county_gdf)} 条记录")

print(f"\n=== 检查完成 ===") 