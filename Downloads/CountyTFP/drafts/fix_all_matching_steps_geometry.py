"""
修复所有匹配步骤的几何信息

本脚本用于修复所有匹配步骤（直接ID匹配、地名匹配、映射表匹配等）的几何信息问题，
确保所有匹配的记录都有正确的几何信息。
"""

import pandas as pd
import geopandas as gpd
import os
import time
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches

def main():
    print("开始修复所有匹配步骤的几何信息...")
    
    # 文件路径
    all_matched_csv = 'matching_results/all_matched.csv'
    original_shapefile = '2015county/2015county.shp'
    output_geojson = 'matching_results/all_matched_fixed.geojson'
    output_dir = 'geometry_fix_results'
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 检查文件是否存在
    for path in [all_matched_csv, original_shapefile]:
        if not os.path.exists(path):
            print(f"错误: 找不到文件 {path}")
            return
    
    # 读取原始GIS数据
    print("\n读取原始GIS数据...")
    start_time = time.time()
    original_gdf = gpd.read_file(original_shapefile)
    print(f"原始GIS数据行数: {len(original_gdf)}")
    print(f"读取耗时: {time.time() - start_time:.2f}秒")
    
    # 创建区划码到几何信息的映射
    print("\n创建区划码到几何信息的映射...")
    start_time = time.time()
    geometry_dict = {}
    for _, row in original_gdf.iterrows():
        # 确保区划码是字符串类型
        geometry_dict[str(row['区划码'])] = row['geometry']
    
    print(f"区划码到几何信息的映射条目数: {len(geometry_dict)}")
    print(f"创建耗时: {time.time() - start_time:.2f}秒")
    
    # 读取匹配结果CSV
    print("\n读取匹配结果CSV...")
    start_time = time.time()
    all_matched = pd.read_csv(all_matched_csv)
    print(f"匹配结果行数: {len(all_matched)}")
    print(f"读取耗时: {time.time() - start_time:.2f}秒")
    
    # 确保gis_code是字符串类型
    all_matched['gis_code'] = all_matched['gis_code'].astype(str)
    
    # 检查匹配来源分布
    if 'match_source' in all_matched.columns:
        print("\n匹配来源分布:")
        match_source_counts = all_matched['match_source'].value_counts()
        for source, count in match_source_counts.items():
            print(f"{source}: {count}条记录 ({count/len(all_matched):.2%})")
    
    # 为每条记录添加几何信息
    print("\n为每条记录添加几何信息...")
    start_time = time.time()
    
    geometries = []
    valid_count = 0
    invalid_count = 0
    
    # 按匹配来源统计
    source_stats = {}
    
    for _, row in all_matched.iterrows():
        gis_code = row['gis_code']
        match_source = row.get('match_source', '未知来源')
        
        if gis_code in geometry_dict:
            geometries.append(geometry_dict[gis_code])
            valid_count += 1
            
            # 更新来源统计
            if match_source not in source_stats:
                source_stats[match_source] = {'valid': 0, 'invalid': 0, 'total': 0}
            source_stats[match_source]['valid'] += 1
            source_stats[match_source]['total'] += 1
        else:
            geometries.append(None)
            invalid_count += 1
            
            # 更新来源统计
            if match_source not in source_stats:
                source_stats[match_source] = {'valid': 0, 'invalid': 0, 'total': 0}
            source_stats[match_source]['invalid'] += 1
            source_stats[match_source]['total'] += 1
    
    print(f"有效几何信息记录数: {valid_count}/{len(all_matched)} ({valid_count/len(all_matched):.2%})")
    print(f"无效几何信息记录数: {invalid_count}/{len(all_matched)} ({invalid_count/len(all_matched):.2%})")
    print(f"添加耗时: {time.time() - start_time:.2f}秒")
    
    # 按匹配来源统计有效几何信息
    print("\n按匹配来源统计有效几何信息:")
    for source, stats in source_stats.items():
        valid_pct = stats['valid'] / stats['total'] * 100 if stats['total'] > 0 else 0
        print(f"{source}: {stats['valid']}/{stats['total']} ({valid_pct:.2f}%)")
    
    # 创建GeoDataFrame
    print("\n创建GeoDataFrame...")
    start_time = time.time()
    all_matched_gdf = gpd.GeoDataFrame(
        all_matched,
        geometry=geometries,
        crs=original_gdf.crs
    )
    print(f"创建耗时: {time.time() - start_time:.2f}秒")
    
    # 检查几何信息
    valid_geometry_count = all_matched_gdf.geometry.notna().sum()
    print(f"GeoDataFrame中有效几何信息记录数: {valid_geometry_count}/{len(all_matched_gdf)} ({valid_geometry_count/len(all_matched_gdf):.2%})")
    
    # 保存为GeoJSON
    print(f"\n保存修复后的GeoJSON到 {output_geojson}...")
    start_time = time.time()
    all_matched_gdf.to_file(output_geojson, driver='GeoJSON')
    print(f"保存耗时: {time.time() - start_time:.2f}秒")
    
    # 创建可视化
    print("\n创建可视化...")
    
    # 创建地图，显示各匹配来源的几何信息分布
    if 'match_source' in all_matched_gdf.columns:
        # 定义颜色映射
        colors = {
            '直接ID匹配': '#1f77b4',      # 蓝色
            '省份代码+地名匹配': '#ff7f0e',  # 橙色
            '行政区划代码映射匹配': '#2ca02c',  # 绿色
            '自治州合并匹配': '#d62728'     # 红色
        }
        
        # 获取唯一的匹配来源
        match_sources = all_matched_gdf['match_source'].unique()
        
        # 创建地图
        fig, ax = plt.subplots(1, 1, figsize=(15, 10))
        
        # 绘制原始GIS数据（浅灰色）
        original_gdf.plot(
            ax=ax,
            color='#f0f0f0',
            edgecolor='#d0d0d0',
            linewidth=0.1
        )
        
        # 按匹配来源绘制
        for source in match_sources:
            source_gdf = all_matched_gdf[all_matched_gdf['match_source'] == source]
            valid_source_gdf = source_gdf[source_gdf.geometry.notna()]
            
            if len(valid_source_gdf) > 0:
                valid_source_gdf.plot(
                    ax=ax,
                    color=colors.get(source, '#999999'),
                    edgecolor='black',
                    linewidth=0.1,
                    alpha=0.7
                )
        
        # 添加图例
        patches = []
        for source in match_sources:
            if source in colors:
                stats = source_stats.get(source, {'valid': 0, 'total': 0})
                label = f"{source} ({stats['valid']}/{stats['total']})"
                patches.append(mpatches.Patch(color=colors[source], label=label))
        
        ax.legend(handles=patches, loc='lower right', fontsize=12)
        
        # 设置标题
        ax.set_title('修复后各匹配来源的几何信息分布', fontsize=16)
        
        # 关闭坐标轴
        ax.set_axis_off()
        
        # 保存地图
        map_file = os.path.join(output_dir, 'fixed_matching_geometry_map.png')
        plt.savefig(map_file, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"地图已保存到: {map_file}")
    
    # 创建汇总表格
    print("\n创建汇总表格...")
    summary_data = []
    
    for source, stats in source_stats.items():
        summary_data.append({
            '匹配来源': source,
            '总记录数': stats['total'],
            '有效几何信息记录数': stats['valid'],
            '有效几何信息比例': f"{stats['valid']/stats['total']*100:.2f}%" if stats['total'] > 0 else "N/A",
            '无效几何信息记录数': stats['invalid'],
            '无效几何信息比例': f"{stats['invalid']/stats['total']*100:.2f}%" if stats['total'] > 0 else "N/A"
        })
    
    summary_df = pd.DataFrame(summary_data)
    summary_file = os.path.join(output_dir, 'geometry_fix_summary.csv')
    summary_df.to_csv(summary_file, index=False)
    print(f"汇总表格已保存到: {summary_file}")
    
    print("\n修复完成!")

if __name__ == "__main__":
    main()