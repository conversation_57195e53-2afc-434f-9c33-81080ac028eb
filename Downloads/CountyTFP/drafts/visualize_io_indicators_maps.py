"""
投入产出指标地理分布时间序列地图生成脚本

本脚本用于生成七个关键投入产出指标的地理分布时间序列地图：
1. TFP (全要素生产率)
2. Y/L (劳动生产率 - 产出/劳动)
3. Y/K (资本产出比 - 产出/资本)
4. Y/Land (土地产出比 - 产出/土地)
5. K/L (资本劳动比 - 资本/劳动)
6. Land/L (土地劳动比 - 土地/劳动)
7. unitcost (单位成本 - 总成本/产出)

每个指标将生成1997-2015年每年的地图和一个动态GIF，展示指标随时间的变化。

所需变量:
- tfp: 全要素生产率
- agr_outputq: 农业产出
- qlabor: 劳动投入
- qcapital: 资本投入
- qland: 土地投入
- total_cost: 总成本
- year: 数据年份 (1997-2015)
- countyid: 县级行政区ID
- geometry: 地理边界数据

依赖文件:
- 通过load_geo_data()函数加载的地理数据
- 省级边界shapefile文件
"""

import pandas as pd
import geopandas as gpd
import matplotlib.pyplot as plt
import numpy as np
import os
from matplotlib.colors import LinearSegmentedColormap, BoundaryNorm
import imageio.v2 as imageio
from mpl_toolkits.axes_grid1 import make_axes_locatable
from load_geo_data import load_geo_data

print("开始创建投入产出指标地理分布时间序列地图...")

# 创建输出目录
output_dir = "io_indicators_maps"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    print(f"创建输出目录: {output_dir}")

# 使用缓存机制加载数据
print("加载缓存数据...")
gdf = load_geo_data(force_reload=False)
print(f"数据形状: {gdf.shape}")

# 确保countyid是字符串类型
gdf['countyid'] = gdf['countyid'].astype(str)

# 筛选样本：年份在1997到2015之间，排除市辖区和地级市
filtered_data = gdf[
    (gdf['year'] >= 1997) & 
    (gdf['year'] <= 2015) & 
    (gdf['gis_county_type'] != "市辖区") &
    (~gdf['countyid'].str.endswith('00'))  # 排除地级市
].copy()
print(f"筛选后的样本数量: {len(filtered_data)}")
print(f"筛选后的唯一countyid数量: {filtered_data['countyid'].nunique()}")
print(f"筛选后的年份范围: {filtered_data['year'].min()} 到 {filtered_data['year'].max()}")

# 计算投入产出指标
print("\n计算投入产出指标...")

# 1. TFP (全要素生产率) - 直接使用数据中的tfp列
print("1. 使用数据中的TFP值")
# TFP已经在数据中，不需要额外计算

# 2. Y/L (劳动生产率) - agr_outputq / qlabor
print("2. 计算劳动生产率 (Y/L)")
filtered_data['Y_L'] = filtered_data['agr_outputq'] / filtered_data['qlabor']

# 3. Y/K (资本产出比) - agr_outputq / qcapital
print("3. 计算资本产出比 (Y/K)")
filtered_data['Y_K'] = filtered_data['agr_outputq'] / filtered_data['qcapital']

# 4. Y/Land (土地产出比) - agr_outputq / qland
print("4. 计算土地产出比 (Y/Land)")
filtered_data['Y_Land'] = filtered_data['agr_outputq'] / filtered_data['qland']

# 5. K/L (资本劳动比) - qcapital / qlabor
print("5. 计算资本劳动比 (K/L)")
filtered_data['K_L'] = filtered_data['qcapital'] / filtered_data['qlabor']

# 6. Land/L (土地劳动比) - qland / qlabor
print("6. 计算土地劳动比 (Land/L)")
filtered_data['Land_L'] = filtered_data['qland'] / filtered_data['qlabor']

# 7. unitcost (单位成本) - 重新计算，使用价格和数量
print("7. 计算单位成本 (unitcost)")
# 计算各种投入成本
if all(col in filtered_data.columns for col in ['plabor', 'qlabor']):
    filtered_data['labor_cost'] = filtered_data['plabor'] * filtered_data['qlabor']
    print("   计算劳动成本: plabor * qlabor")

if all(col in filtered_data.columns for col in ['pcapital', 'qcapital']):
    filtered_data['capital_cost'] = filtered_data['pcapital'] * filtered_data['qcapital']
    print("   计算资本成本: pcapital * qcapital")

if all(col in filtered_data.columns for col in ['pland', 'qland']):
    filtered_data['land_cost'] = filtered_data['pland'] * filtered_data['qland']
    print("   计算土地成本: pland * qland")

# 检查是否有中间投入材料相关列
material_cols = [col for col in filtered_data.columns if 'mater' in col.lower() or 'inter' in col.lower()]
print(f"   发现可能的中间投入列: {material_cols}")

# 如果找到了qmater和pmater列
if all(col in filtered_data.columns for col in ['pmater', 'qmater']):
    filtered_data['material_cost'] = filtered_data['pmater'] * filtered_data['qmater']
    print("   计算中间投入成本: pmater * qmater")
elif 'intermediate_cost' in filtered_data.columns:
    print("   使用现有的intermediate_cost列")
elif 'material_cost' in filtered_data.columns:
    print("   使用现有的material_cost列")

# 计算总成本
cost_columns = [col for col in ['labor_cost', 'capital_cost', 'land_cost', 'material_cost', 'intermediate_cost'] 
                if col in filtered_data.columns]

if cost_columns:
    filtered_data['total_cost'] = filtered_data[cost_columns].sum(axis=1)
    filtered_data['unitcost'] = filtered_data['total_cost'] / filtered_data['agr_outputq']
    print(f"   使用以下列计算总成本: {cost_columns}")
else:
    print("   警告: 无法计算单位成本，缺少成本数据列")
    # 创建一个空的unitcost列
    filtered_data['unitcost'] = np.nan

# 处理无穷大和NaN值
indicators = ['tfp', 'Y_L', 'Y_K', 'Y_Land', 'K_L', 'Land_L', 'unitcost']
for indicator in indicators:
    filtered_data[indicator] = filtered_data[indicator].replace([np.inf, -np.inf], np.nan)
    # 计算有效值的数量和百分比
    valid_count = filtered_data[indicator].notna().sum()
    valid_percent = valid_count / len(filtered_data) * 100
    print(f"{indicator}有效值数量: {valid_count} ({valid_percent:.2f}%)")
    
    # 添加更详细的统计信息，帮助诊断问题
    if valid_count > 0:
        valid_values = filtered_data[indicator].dropna()
        print(f"  - 最小值: {valid_values.min()}")
        print(f"  - 最大值: {valid_values.max()}")
        print(f"  - 零值数量: {(valid_values == 0).sum()}")
        print(f"  - 负值数量: {(valid_values < 0).sum()}")

# 加载省级边界数据
province_path = "RAW/Province/province.shp"
provinces = gpd.read_file(province_path)
print(f"加载省级边界数据: {len(provinces)}个省份")

# 为每个指标创建地图
# 选择要展示的年份 - 从1997到2015年的每一年
years = range(1997, 2016)  # 1997到2015年的每一年

# 指标名称映射和描述
indicator_info = {
    'tfp': {
        'name': 'TFP (Total Factor Productivity)',
        'description': 'Measures overall production efficiency',
        'higher_better': True
    },
    'Y_L': {
        'name': 'Y/L (Output per Labor)',
        'description': 'Labor productivity',
        'higher_better': True
    },
    'Y_K': {
        'name': 'Y/K (Output per Capital)',
        'description': 'Capital productivity',
        'higher_better': True
    },
    'Y_Land': {
        'name': 'Y/Land (Output per Land)',
        'description': 'Land productivity',
        'higher_better': True
    },
    'K_L': {
        'name': 'K/L (Capital-Labor Ratio)',
        'description': 'Capital intensity',
        'higher_better': None  # Neutral
    },
    'Land_L': {
        'name': 'Land/L (Land-Labor Ratio)',
        'description': 'Land intensity',
        'higher_better': None  # Neutral
    },
    'unitcost': {
        'name': 'Unit Cost (Cost per Output)',
        'description': 'Production efficiency',
        'higher_better': False  # Lower is better
    }
}

# 为每个指标创建一个子目录
for indicator in indicators:
    indicator_dir = os.path.join(output_dir, indicator)
    if not os.path.exists(indicator_dir):
        os.makedirs(indicator_dir)
        print(f"创建{indicator}指标目录: {indicator_dir}")
    
    # 分析指标数据分布，确定合适的颜色区间
    valid_values = filtered_data[indicator].dropna()
    if indicator != 'tfp':  # 除TFP外的所有指标都应为正值
        # 移除零值和负值
        valid_values = valid_values[valid_values > 0]
    
    # 如果没有有效值，跳过此指标
    if len(valid_values) == 0:
        print(f"警告: {indicator}没有有效值，跳过此指标")
        continue
    
    print(f"\n{indicator}指标分布：")
    print(f"最小值: {valid_values.min()}")
    print(f"25%分位数: {valid_values.quantile(0.25)}")
    print(f"中位数: {valid_values.median()}")
    print(f"75%分位数: {valid_values.quantile(0.75)}")
    print(f"90%分位数: {valid_values.quantile(0.90)}")
    print(f"95%分位数: {valid_values.quantile(0.95)}")
    print(f"99%分位数: {valid_values.quantile(0.99)}")
    print(f"最大值: {valid_values.max()}")
    
    # 设置颜色边界
    q25 = valid_values.quantile(0.25)
    q50 = valid_values.quantile(0.50)
    q75 = valid_values.quantile(0.75)
    q90 = valid_values.quantile(0.90)
    q95 = valid_values.quantile(0.95)
    q99 = valid_values.quantile(0.99)
    max_val = valid_values.max()
    min_val = valid_values.min()
    
    # 对于Land_L这样的小数值，使用不同的边界设置方法
    if indicator == 'Land_L' or (max_val < 0.01 and min_val > 0):
        print(f"   注意: {indicator}的值非常小，使用特殊边界设置")
        # 使用线性间隔创建6个边界点（5个区间）
        bounds = np.linspace(min_val, max_val, 6)
        
        # 如果最小值接近0，从0开始
        if min_val < max_val * 0.01:
            bounds = np.linspace(0, max_val, 6)
            
        # 确保有足够的不同边界值
        if len(set([round(b, 10) for b in bounds])) < 6:
            # 如果边界值太接近，创建指数间隔
            if min_val <= 0:
                min_val = max_val * 0.00001  # 避免对0取对数
            
            # 使用对数空间创建边界
            log_min = np.log10(min_val)
            log_max = np.log10(max_val)
            log_bounds = np.linspace(log_min, log_max, 6)
            bounds = [10**x for x in log_bounds]
    elif indicator == 'tfp':
        # TFP可能有负值，使用不同的边界设置
        if min_val < 0 and max_val > 0:
            # 如果有正负值，确保0是一个边界
            neg_bounds = np.linspace(min_val, 0, 3)[:-1]  # 不包括0
            pos_bounds = np.linspace(0, max_val, 5)  # 包括0
            bounds = list(neg_bounds) + list(pos_bounds)
        else:
            # 全正或全负
            bounds = np.linspace(min_val, max_val, 6)
    else:
        # 其他指标使用分位数
        quantiles = [0, 0.2, 0.4, 0.6, 0.8, 1.0]
        bounds = [min_val] + [valid_values.quantile(q) for q in quantiles[1:]]
    
    # 根据指标类型格式化边界值
    if indicator == 'tfp':
        # TFP使用3位小数
        bounds = [float(np.floor(b * 1000) / 1000) for b in bounds]
    elif indicator == 'Land_L' or max_val < 0.01:
        # 对于非常小的值，保持更高精度
        if max_val < 0.0001:
            # 使用科学计数法格式
            bounds = [float(b) for b in bounds]
        else:
            # 使用4位小数
            bounds = [float(np.floor(b * 10000) / 10000) for b in bounds]
    else:
        # 其他指标使用2位小数
        bounds = [float(np.floor(b * 100) / 100) for b in bounds]
    
    # 去除重复值并排序
    bounds = sorted(list(set(bounds)))
    
    # 确保至少有5个区间（6个边界点）
    while len(bounds) < 6:
        # 找到最大间隔并在中间添加一个值
        max_gap = 0
        max_gap_idx = 0
        for i in range(len(bounds) - 1):
            gap = bounds[i+1] - bounds[i]
            if gap > max_gap:
                max_gap = gap
                max_gap_idx = i
        
        # 在最大间隔中间添加一个新边界
        new_bound = bounds[max_gap_idx] + max_gap / 2
        bounds.insert(max_gap_idx + 1, new_bound)
    
    # 如果边界太多，保留最重要的几个
    if len(bounds) > 6:
        # 保留最小值、最大值和中间的几个值
        if len(bounds) <= 8:
            # 如果不是太多，全部保留
            pass
        else:
            # 选择均匀分布的6个边界
            indices = np.linspace(0, len(bounds) - 1, 6).astype(int)
            bounds = [bounds[i] for i in indices]
    
    print(f"{indicator}颜色边界: {bounds}")
    
    # 创建自定义颜色映射（从浅到深）
    colors = ['#F9F8CA', '#96D2B0', '#35B9C5', '#2681B6', '#1E469B']
    
    # 对于"更低更好"的指标（如unitcost），反转颜色映射
    if indicator in ['unitcost'] and indicator_info[indicator]['higher_better'] is False:
        colors = colors[::-1]  # 反转颜色列表
    
    # 确保颜色数量与边界数量匹配
    if len(colors) != len(bounds) - 1:
        if len(bounds) - 1 < len(colors):
            colors = colors[:len(bounds)-1]
        else:
            # 创建更多的颜色
            cmap_temp = LinearSegmentedColormap.from_list('cmap', colors, N=len(bounds)-1)
            colors = [cmap_temp(i/(len(bounds)-2)) for i in range(len(bounds)-1)]
    
    # 创建离散颜色映射
    cmap = LinearSegmentedColormap.from_list('cmap', colors, N=len(bounds)-1)
    norm = BoundaryNorm(bounds, cmap.N)
    
    # 存储地图文件路径，用于创建GIF
    map_files = []
    
    for year in years:
        print(f"创建{year}年的{indicator}地图...")
        
        # 创建当年的数据副本
        year_data = filtered_data[filtered_data['year'] == year].copy()
        
        # 如果当年没有数据，跳过
        if len(year_data) == 0:
            print(f"警告: {year}年没有数据，跳过")
            continue
        
        # 计算当年指标的统计信息
        valid_year_data = year_data[year_data[indicator].notna()]
        if len(valid_year_data) == 0:
            print(f"警告: {year}年没有有效的{indicator}数据，跳过")
            continue
        
        counties_with_data = valid_year_data['countyid'].nunique()
        total_counties = year_data['countyid'].nunique()
        coverage_percentage = (counties_with_data / total_counties * 100) if total_counties > 0 else 0
        mean_value = valid_year_data[indicator].mean()
        median_value = valid_year_data[indicator].median()
        
        # 创建地图
        fig, ax = plt.subplots(1, 1, figsize=(15, 10))
        
        # 绘制county填充地图，使用自定义颜色范围
        year_data.plot(
            column=indicator,
            ax=ax,
            cmap=cmap,
            norm=norm,
            edgecolor='none',
            legend=False
        )
        
        # 绘制省界
        provinces.boundary.plot(
            ax=ax,
            edgecolor='black',
            linewidth=0.5,
            zorder=2
        )
        
        # 设置地图标题和布局
        indicator_name = indicator_info[indicator]['name']
        indicator_desc = indicator_info[indicator]['description']
        
        ax.set_title(
            f'{indicator_name} by County ({year})\n'
            f'{indicator_desc}\n'
            f'Counties with Data: {counties_with_data} out of {total_counties} ({coverage_percentage:.1f}%)\n'
            f'Mean: {mean_value:.4f}, Median: {median_value:.4f}',
            fontsize=16
        )
        
        # 移除坐标轴
        ax.set_axis_off()
        
        # 添加颜色条图例
        divider = make_axes_locatable(ax)
        cax = divider.append_axes("right", size="2%", pad=0.1)
        
        # 创建颜色条
        sm = plt.cm.ScalarMappable(cmap=cmap, norm=norm)
        sm._A = []  # 空数组，用于matplotlib内部计算
        cbar = fig.colorbar(sm, cax=cax)
        cbar.set_label(indicator_name, fontsize=12)
        
        # 设置颜色条刻度
        cbar.set_ticks(bounds)
        
        # 根据指标类型格式化刻度标签
        if indicator == 'tfp':
            cbar.set_ticklabels([f"{b:.3f}" for b in bounds])
        else:
            cbar.set_ticklabels([f"{b:.2f}" for b in bounds])
        
        # 添加数据来源和注释
        higher_better = indicator_info[indicator]['higher_better']
        if higher_better is True:
            note_text = 'Note: Higher values indicate better performance. '
        elif higher_better is False:
            note_text = 'Note: Lower values indicate better performance. '
        else:
            note_text = 'Note: '
            
        note_text += 'Excluding urban districts and prefecture-level cities.\n'
        note_text += 'Source: Agricultural production data (1997-2015)'
        
        plt.annotate(
            note_text,
            xy=(0.02, 0.02),
            xycoords='figure fraction',
            fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8)
        )
        
        # 保存地图
        output_file = os.path.join(indicator_dir, f"{indicator}_map_{year}.png")
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close(fig)
        print(f"保存地图到: {output_file}")
        
        # 添加到文件列表，用于创建GIF
        map_files.append(output_file)
    
    # 创建GIF动画
    if map_files:
        print(f"创建{indicator}指标的GIF动画...")
        gif_path = os.path.join(indicator_dir, f"{indicator}_map_animation.gif")
        
        # 读取所有图片
        images = []
        for file_path in map_files:
            images.append(imageio.imread(file_path))
        
        # 创建GIF，设置每帧停留时间为2秒
        imageio.mimsave(gif_path, images, duration=2.0)
        print(f"GIF动画已保存到: {gif_path}")

print("\n所有投入产出指标地图创建完成!")



