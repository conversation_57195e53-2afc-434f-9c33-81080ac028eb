import pandas as pd
import numpy as np
import pyreadstat
import matplotlib.pyplot as plt
from collections import Counter

print("正在读取文件...")

# 读取Stata文件
bank_df, meta = pyreadstat.read_dta("bank_variables.dta")
print("成功读取bank_variables.dta")
print(f"bank_variables.dta形状: {bank_df.shape}")

# 读取CSV文件
matched_df = pd.read_csv("matching_results/all_matched.csv")
print("成功读取all_matched.csv")
print(f"all_matched.csv形状: {matched_df.shape}")

# 数据预处理
print("\n数据预处理...")

# 确保countyid和year列是正确的数据类型
bank_df['countyid'] = bank_df['countyid'].astype(str)
bank_df['year'] = bank_df['year'].astype(int)
matched_df['countyid'] = matched_df['countyid'].astype(str)
matched_df['year'] = matched_df['year'].astype(int)

# 创建唯一键
bank_df['key'] = bank_df['countyid'] + '_' + bank_df['year'].astype(str)
matched_df['key'] = matched_df['countyid'].astype(str) + '_' + matched_df['year'].astype(str)

# 分析键的重叠情况
bank_keys = set(bank_df['key'])
matched_keys = set(matched_df['key'])

only_in_bank = bank_keys - matched_keys
only_in_matched = matched_keys - bank_keys
in_both = bank_keys.intersection(matched_keys)

print(f"\n只存在于bank_variables.dta中的countyid+year组合数量: {len(only_in_bank)}")
print(f"只存在于all_matched.csv中的countyid+year组合数量: {len(only_in_matched)}")
print(f"同时存在于两个文件中的countyid+year组合数量: {len(in_both)}")

# 分析只存在于一个文件中的键的年份分布
if only_in_bank:
    only_in_bank_years = [int(key.split('_')[1]) for key in only_in_bank]
    only_in_bank_year_counts = Counter(only_in_bank_years)
    print("\n只存在于bank_variables.dta中的键的年份分布:")
    for year, count in sorted(only_in_bank_year_counts.items()):
        print(f"  {year}: {count}个")

if only_in_matched:
    only_in_matched_years = [int(key.split('_')[1]) for key in only_in_matched]
    only_in_matched_year_counts = Counter(only_in_matched_years)
    print("\n只存在于all_matched.csv中的键的年份分布 (前10个):")
    for year, count in sorted(only_in_matched_year_counts.items())[:10]:
        print(f"  {year}: {count}个")

# 分析只存在于一个文件中的键的countyid分布
if only_in_bank:
    only_in_bank_counties = [key.split('_')[0] for key in only_in_bank]
    only_in_bank_county_counts = Counter(only_in_bank_counties)
    print("\n只存在于bank_variables.dta中的键的countyid分布:")
    for county, count in sorted(only_in_bank_county_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"  {county}: {count}个")

# 进行left merge
print("\n进行left merge...")
required_cols = ['year_PSBC', 'bank_type', 'InstituionCode', 'number_all_PSBC', 'number_PSBC', 'countyid', 'year']
merge_cols = [col for col in required_cols if col in bank_df.columns]
bank_subset = bank_df[merge_cols]

# 确保bank_df中的year列是整数类型
bank_subset['year'] = bank_subset['year'].astype(int)

# 进行left merge
merged_df = pd.merge(
    matched_df, 
    bank_subset,
    left_on=['countyid', 'year'],
    right_on=['countyid', 'year'],
    how='left'
)

print(f"合并后的数据形状: {merged_df.shape}")

# 检查合并后的空值情况
null_counts = merged_df[merge_cols].isnull().sum()
print("\n合并后的空值数量:")
for col, count in null_counts.items():
    print(f"  {col}: {count} ({count/len(merged_df)*100:.2f}%)")

# 分析合并后的数据
print("\n合并后的数据分析...")

# 检查哪些年份的数据合并得最好和最差
merge_success_by_year = merged_df.groupby('year').apply(
    lambda x: (x[merge_cols].notnull().all(axis=1).sum() / len(x)) * 100
).sort_values()

print("\n按年份的合并成功率 (前5个最低和最高):")
print("最低的5个年份:")
print(merge_success_by_year.head(5))
print("\n最高的5个年份:")
print(merge_success_by_year.tail(5))

# 检查哪些countyid的数据合并得最好和最差
merge_success_by_county = merged_df.groupby('countyid').apply(
    lambda x: (x[merge_cols].notnull().all(axis=1).sum() / len(x)) * 100
).sort_values()

print("\n按countyid的合并成功率 (前5个最低和最高):")
print("最低的5个countyid:")
print(merge_success_by_county.head(5))
print("\n最高的5个countyid:")
print(merge_success_by_county.tail(5))

# 分析bank_variables.dta中的关键列
print("\nbank_variables.dta中的关键列分析:")
for col in ['year_PSBC', 'bank_type', 'InstituionCode', 'number_all_PSBC', 'number_PSBC']:
    if col in bank_df.columns:
        non_null_count = bank_df[col].notnull().sum()
        print(f"  {col}: {non_null_count}个非空值 ({non_null_count/len(bank_df)*100:.2f}%)")
        if bank_df[col].dtype in [np.int64, np.float64]:
            print(f"    最小值: {bank_df[col].min()}, 最大值: {bank_df[col].max()}, 平均值: {bank_df[col].mean():.2f}")
        elif bank_df[col].dtype == 'object':
            value_counts = bank_df[col].value_counts()
            if len(value_counts) <= 10:
                print(f"    值分布: {dict(value_counts)}")
            else:
                print(f"    唯一值数量: {len(value_counts)}")

print("\n分析完成!")
