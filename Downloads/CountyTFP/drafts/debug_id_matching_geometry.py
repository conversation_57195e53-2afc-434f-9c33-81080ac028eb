"""
调试直接ID匹配几何信息问题

本脚本用于调试直接ID匹配的几何信息问题，检查为什么直接ID匹配的记录没有正确显示在地图上。
"""

import pandas as pd
import geopandas as gpd
import matplotlib.pyplot as plt
import os
from shapely.geometry import Point

def main():
    print("开始调试直接ID匹配几何信息问题...")
    
    # 文件路径
    step1_matched_path = 'matching_results/step1_matched.csv'
    all_matched_geojson = 'matching_results/all_matched.geojson'
    original_shapefile = '2015county/2015county.shp'
    output_dir = 'debug_visualization'
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 检查文件是否存在
    for path in [step1_matched_path, original_shapefile]:
        if not os.path.exists(path):
            print(f"错误: 找不到文件 {path}")
            return
    
    # 读取原始GIS数据
    print("\n读取原始GIS数据...")
    original_gdf = gpd.read_file(original_shapefile)
    print(f"原始GIS数据行数: {len(original_gdf)}")
    
    # 读取直接ID匹配结果
    print("\n读取直接ID匹配结果...")
    step1_matched = pd.read_csv(step1_matched_path)
    print(f"直接ID匹配结果行数: {len(step1_matched)}")
    
    # 读取所有匹配结果GeoJSON（如果存在）
    all_matched_gdf = None
    if os.path.exists(all_matched_geojson):
        print("\n读取所有匹配结果GeoJSON...")
        all_matched_gdf = gpd.read_file(all_matched_geojson)
        print(f"所有匹配结果GeoJSON行数: {len(all_matched_gdf)}")
        
        # 筛选出直接ID匹配的记录
        direct_match_gdf = all_matched_gdf[all_matched_gdf['match_source'] == '直接ID匹配']
        print(f"直接ID匹配的GeoJSON记录数: {len(direct_match_gdf)}")
        
        # 检查几何信息
        valid_geometry_count = direct_match_gdf.geometry.notna().sum()
        print(f"有效几何信息记录数: {valid_geometry_count}/{len(direct_match_gdf)} ({valid_geometry_count/len(direct_match_gdf):.2%})")
        
        # 检查是否有空几何信息
        if valid_geometry_count < len(direct_match_gdf):
            print("警告: 存在空几何信息的记录!")
    
    # 创建区划码到几何信息的映射
    print("\n创建区划码到几何信息的映射...")
    geometry_dict = {}
    for _, row in original_gdf.iterrows():
        geometry_dict[str(row['区划码'])] = row['geometry']
    
    print(f"区划码到几何信息的映射条目数: {len(geometry_dict)}")
    
    # 检查直接ID匹配结果中的gis_code是否在geometry_dict中
    print("\n检查直接ID匹配结果中的gis_code是否在geometry_dict中...")
    
    # 将gis_code转为字符串
    step1_matched['gis_code'] = step1_matched['gis_code'].astype(str)
    
    # 检查每个gis_code是否在geometry_dict中
    found_count = 0
    not_found_count = 0
    
    for _, row in step1_matched.iterrows():
        gis_code = row['gis_code']
        if gis_code in geometry_dict:
            found_count += 1
        else:
            not_found_count += 1
    
    print(f"在geometry_dict中找到的gis_code: {found_count}/{len(step1_matched)} ({found_count/len(step1_matched):.2%})")
    print(f"在geometry_dict中未找到的gis_code: {not_found_count}/{len(step1_matched)} ({not_found_count/len(step1_matched):.2%})")
    
    # 如果有未找到的gis_code，保存前10个未找到的gis_code
    if not_found_count > 0:
        not_found_codes = []
        for _, row in step1_matched.iterrows():
            gis_code = row['gis_code']
            if gis_code not in geometry_dict:
                not_found_codes.append(gis_code)
                if len(not_found_codes) >= 10:
                    break
        
        print(f"前10个未找到的gis_code: {not_found_codes}")
    
    # 手动创建GeoDataFrame
    print("\n手动创建直接ID匹配的GeoDataFrame...")
    
    # 为每条记录添加几何信息
    geometries = []
    found_indices = []
    
    for idx, row in step1_matched.iterrows():
        gis_code = row['gis_code']
        if gis_code in geometry_dict:
            geometries.append(geometry_dict[gis_code])
            found_indices.append(idx)
        else:
            # 对于未找到的记录，添加一个默认的点几何
            geometries.append(Point(0, 0))
    
    # 创建GeoDataFrame
    step1_matched_gdf = gpd.GeoDataFrame(
        step1_matched,
        geometry=geometries,
        crs=original_gdf.crs
    )
    
    # 筛选出有有效几何信息的记录
    valid_step1_matched_gdf = step1_matched_gdf.iloc[found_indices]
    
    print(f"手动创建的GeoDataFrame行数: {len(step1_matched_gdf)}")
    print(f"有有效几何信息的记录数: {len(valid_step1_matched_gdf)}")
    
    # 保存手动创建的GeoDataFrame
    output_file = os.path.join(output_dir, 'step1_matched_with_geometry.geojson')
    valid_step1_matched_gdf.to_file(output_file, driver='GeoJSON')
    print(f"保存手动创建的GeoDataFrame到: {output_file}")
    
    # 创建地图
    print("\n创建地图...")
    
    # 创建地图
    fig, ax = plt.subplots(1, 1, figsize=(15, 10))
    
    # 绘制所有区域（浅色）
    original_gdf.plot(
        ax=ax,
        color='#F9F8CA',
        edgecolor='black',
        linewidth=0.1
    )
    
    # 绘制匹配的区域（深色）
    if len(valid_step1_matched_gdf) > 0:
        valid_step1_matched_gdf.plot(
            ax=ax,
            color='#2681B6',
            edgecolor='black',
            linewidth=0.1
        )
    
    # 设置标题
    ax.set_title(
        f'直接ID匹配结果\n'
        f'总记录数: {len(step1_matched)} | 有效几何信息记录数: {len(valid_step1_matched_gdf)} ({len(valid_step1_matched_gdf)/len(step1_matched):.2%})',
        fontsize=16
    )
    
    # 关闭坐标轴
    ax.set_axis_off()
    
    # 保存地图
    output_file = os.path.join(output_dir, 'step1_matched_map.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"保存地图到: {output_file}")
    
    # 检查原始GIS数据中的区划码格式
    print("\n检查原始GIS数据中的区划码格式...")
    
    # 获取原始GIS数据中的区划码
    original_codes = original_gdf['区划码'].astype(str).tolist()
    
    # 获取直接ID匹配结果中的gis_code
    matched_codes = step1_matched['gis_code'].astype(str).tolist()
    
    # 检查前10个原始区划码
    print(f"前10个原始区划码: {original_codes[:10]}")
    
    # 检查前10个匹配的gis_code
    print(f"前10个匹配的gis_code: {matched_codes[:10]}")
    
    # 检查区划码的长度分布
    original_code_lengths = [len(code) for code in original_codes]
    matched_code_lengths = [len(code) for code in matched_codes]
    
    original_length_counts = {}
    for length in original_code_lengths:
        original_length_counts[length] = original_length_counts.get(length, 0) + 1
    
    matched_length_counts = {}
    for length in matched_code_lengths:
        matched_length_counts[length] = matched_length_counts.get(length, 0) + 1
    
    print(f"原始区划码长度分布: {original_length_counts}")
    print(f"匹配的gis_code长度分布: {matched_length_counts}")
    
    # 检查区划码的格式是否一致
    print("\n检查区划码的格式是否一致...")
    
    # 将原始区划码转为整数
    try:
        original_codes_int = [int(code) for code in original_codes]
        print("原始区划码可以转为整数")
    except ValueError:
        print("警告: 原始区划码不能全部转为整数!")
    
    # 将匹配的gis_code转为整数
    try:
        matched_codes_int = [int(code) for code in matched_codes]
        print("匹配的gis_code可以转为整数")
    except ValueError:
        print("警告: 匹配的gis_code不能全部转为整数!")
    
    # 检查是否有共同的区划码
    common_codes = set(original_codes) & set(matched_codes)
    print(f"原始区划码和匹配的gis_code共有的区划码数量: {len(common_codes)}")
    
    # 如果有共同的区划码，检查前10个
    if common_codes:
        print(f"前10个共同的区划码: {list(common_codes)[:10]}")
    
    print("\n调试完成!")

if __name__ == "__main__":
    main()