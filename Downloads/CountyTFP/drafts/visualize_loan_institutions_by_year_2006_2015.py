"""
贷款机构分布地图生成脚本 (2006-2015年)

本脚本用于生成2006-2015年每年的贷款机构分布地图。
贷款机构的定义：InstituionCode不为空且不以'B0018A'开头的机构。

输出：
1. 每年的贷款机构分布地图
2. 所有年份的动态GIF
"""

import pandas as pd
import geopandas as gpd
import matplotlib.pyplot as plt
import numpy as np
import os
from matplotlib.colors import LinearSegmentedColormap, BoundaryNorm
from load_geo_data import load_geo_data
import imageio.v2 as imageio
from mpl_toolkits.axes_grid1 import make_axes_locatable

print("开始创建2006-2015年贷款机构分布地图...")

# 使用缓存机制加载数据
gdf = load_geo_data(force_reload=False)

# 确保countyid是字符串类型
gdf['countyid'] = gdf['countyid'].astype(str)

# 筛选样本：年份在2006到2015之间，排除市辖区和地级市
filtered_data = gdf[
    (gdf['year'] >= 2006) & 
    (gdf['year'] <= 2015) & 
    (gdf['gis_county_type'] != "市辖区") &
    (~gdf['countyid'].str.endswith('00'))  # 排除地级市
].copy()

print(f"筛选后的样本数量: {len(filtered_data)}")
print(f"筛选后的唯一countyid数量: {filtered_data['countyid'].nunique()}")
print(f"筛选后的年份范围: {filtered_data['year'].min()} 到 {filtered_data['year'].max()}")

# 加载省级边界数据
province_path = "RAW/Province/province.shp"
provinces = gpd.read_file(province_path)
print(f"加载省级边界数据: {len(provinces)}个省份")

# 创建贷款机构标识列
print("创建贷款机构标识列...")
filtered_data['has_loan_inst'] = 0  # 默认为0

# 设置贷款机构标识：InstituionCode不为空且不以B0018A开头
mask = (
    filtered_data['InstituionCode'].notna() & 
    ~filtered_data['InstituionCode'].astype(str).str.startswith('B0018A')
)
filtered_data.loc[mask, 'has_loan_inst'] = 1

# 计算每年有贷款机构的县数量
yearly_stats = {}
for year in range(2006, 2016):
    year_data = filtered_data[filtered_data['year'] == year]
    counties_with_inst = year_data[year_data['has_loan_inst'] == 1]['countyid'].nunique()
    total_counties = year_data['countyid'].nunique()
    coverage_percentage = (counties_with_inst / total_counties * 100) if total_counties > 0 else 0
    yearly_stats[year] = {
        'counties_with_inst': counties_with_inst,
        'total_counties': total_counties,
        'coverage_percentage': coverage_percentage
    }

print("\n每年有贷款机构的县数量统计:")
for year, stats in yearly_stats.items():
    print(f"  {year}年: {stats['counties_with_inst']}/{stats['total_counties']} ({stats['coverage_percentage']:.2f}%)")

# 创建输出目录
output_dir = "loan_institutions_maps_2006_2015"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    print(f"创建输出目录: {output_dir}")

# 创建颜色映射
colors = ['#F9F8CA', '#2681B6']  # 浅黄色表示无贷款机构，深蓝色表示有贷款机构
cmap = LinearSegmentedColormap.from_list('loan_inst_cmap', colors, N=2)

# 为每年创建地图
years = range(2006, 2016)
map_files = []  # 用于存储地图文件路径，后续创建GIF

for year in years:
    print(f"创建{year}年的地图...")
    
    # 创建当年的数据副本
    year_data = filtered_data[filtered_data['year'] == year].copy()
    
    # 获取当年的统计信息
    stats = yearly_stats[year]
    
    # 创建地图
    fig, ax = plt.subplots(1, 1, figsize=(15, 10))
    
    # 绘制county填充地图
    year_data.plot(
        column='has_loan_inst',
        ax=ax,
        cmap=cmap,
        categorical=True,
        legend=False,
        edgecolor='none'
    )
    
    # 绘制省界
    provinces.boundary.plot(
        ax=ax,
        edgecolor='black',
        linewidth=0.5,
        zorder=2
    )
    
    # 设置地图标题和布局
    ax.set_title(
        f'贷款机构分布 ({year}年)\n'
        f'有贷款机构的县: {stats["counties_with_inst"]}/{stats["total_counties"]} ({stats["coverage_percentage"]:.2f}%)',
        fontsize=16
    )
    
    # 移除坐标轴
    ax.set_axis_off()
    
    # 添加图例
    import matplotlib.patches as mpatches
    no_inst_patch = mpatches.Patch(color=colors[0], label='无贷款机构')
    has_inst_patch = mpatches.Patch(color=colors[1], label='有贷款机构')
    ax.legend(handles=[no_inst_patch, has_inst_patch], loc='lower right', fontsize=12)
    
    # 添加数据来源和注释
    plt.annotate(
        '注: 排除市辖区和地级市\n'
        '来源: 机构数据 (2006-2015)',
        xy=(0.02, 0.02),
        xycoords='figure fraction',
        fontsize=10,
        bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8)
    )
    
    # 保存地图
    output_file = os.path.join(output_dir, f"loan_institutions_map_{year}.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close(fig)
    print(f"地图保存至: {output_file}")
    
    # 添加到文件列表，用于创建GIF
    map_files.append(output_file)

# 创建GIF动画
print("创建GIF动画...")
gif_path = os.path.join(output_dir, "loan_institutions_map_animation.gif")

# 读取所有图片
images = []
for file_path in map_files:
    images.append(imageio.imread(file_path))

# 创建GIF，设置帧持续时间为1秒
imageio.mimsave(gif_path, images, duration=1.0)
print(f"GIF动画保存至: {gif_path}")

# 创建汇总统计图表
print("创建汇总统计图表...")

# 提取统计数据
years_list = list(yearly_stats.keys())
counties_with_inst_list = [stats['counties_with_inst'] for stats in yearly_stats.values()]
coverage_percentage_list = [stats['coverage_percentage'] for stats in yearly_stats.values()]

# 创建双Y轴图表
fig, ax1 = plt.subplots(figsize=(12, 8))

# 第一个Y轴：有贷款机构的县数量
ax1.bar(years_list, counties_with_inst_list, color='#2681B6', alpha=0.7)
ax1.set_xlabel('年份', fontsize=14)
ax1.set_ylabel('有贷款机构的县数量', color='#2681B6', fontsize=14)
ax1.tick_params(axis='y', labelcolor='#2681B6')

# 添加数据标签
for i, v in enumerate(counties_with_inst_list):
    ax1.text(years_list[i], v + 5, str(v), ha='center', va='bottom', color='#2681B6', fontweight='bold')

# 第二个Y轴：覆盖率百分比
ax2 = ax1.twinx()
ax2.plot(years_list, coverage_percentage_list, color='#FF5733', marker='o', linewidth=2, markersize=8)
ax2.set_ylabel('覆盖率 (%)', color='#FF5733', fontsize=14)
ax2.tick_params(axis='y', labelcolor='#FF5733')

# 添加数据标签
for i, v in enumerate(coverage_percentage_list):
    ax2.text(years_list[i], v + 0.5, f"{v:.2f}%", ha='center', va='bottom', color='#FF5733', fontweight='bold')

# 设置标题
plt.title('2006-2015年贷款机构覆盖情况', fontsize=16)

# 设置网格线
ax1.grid(True, linestyle='--', alpha=0.3)

# 保存图表
stats_file = os.path.join(output_dir, "loan_institutions_stats.png")
plt.savefig(stats_file, dpi=300, bbox_inches='tight')
plt.close(fig)
print(f"统计图表保存至: {stats_file}")

print("所有地图和图表已成功创建！")