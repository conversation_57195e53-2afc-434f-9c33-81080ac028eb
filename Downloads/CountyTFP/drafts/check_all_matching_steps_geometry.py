"""
检查所有匹配步骤的几何信息

本脚本用于检查所有匹配步骤（直接ID匹配、地名匹配、映射表匹配）的几何信息情况，
确定哪些步骤的匹配结果缺少几何信息。
"""

import pandas as pd
import geopandas as gpd
import os
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.patches as mpatches

def main():
    print("开始检查所有匹配步骤的几何信息...")
    
    # 文件路径
    step1_matched_path = 'matching_results/step1_matched.csv'  # 直接ID匹配
    step2_matched_path = 'matching_results/step2_matched.csv'  # 地名匹配
    step3_matched_path = 'matching_results/step3_matched.csv'  # 映射表匹配
    all_matched_csv = 'matching_results/all_matched.csv'       # 所有匹配
    all_matched_geojson = 'matching_results/all_matched.geojson'  # 所有匹配GeoJSON
    original_shapefile = '2015county/2015county.shp'           # 原始GIS数据
    output_dir = 'geometry_check_results'                      # 输出目录
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 检查文件是否存在
    required_files = [original_shapefile, all_matched_csv, all_matched_geojson]
    optional_files = [step1_matched_path, step2_matched_path, step3_matched_path]
    
    for path in required_files:
        if not os.path.exists(path):
            print(f"错误: 找不到必需文件 {path}")
            return
    
    available_steps = []
    for i, path in enumerate([step1_matched_path, step2_matched_path, step3_matched_path], 1):
        if os.path.exists(path):
            available_steps.append(i)
            print(f"找到步骤{i}匹配结果: {path}")
        else:
            print(f"警告: 找不到步骤{i}匹配结果: {path}")
    
    # 读取原始GIS数据
    print("\n读取原始GIS数据...")
    original_gdf = gpd.read_file(original_shapefile)
    print(f"原始GIS数据行数: {len(original_gdf)}")
    
    # 创建区划码到几何信息的映射
    print("\n创建区划码到几何信息的映射...")
    geometry_dict = {}
    for _, row in original_gdf.iterrows():
        geometry_dict[str(row['区划码'])] = row['geometry']
    
    print(f"区划码到几何信息的映射条目数: {len(geometry_dict)}")
    
    # 读取所有匹配结果GeoJSON
    print("\n读取所有匹配结果GeoJSON...")
    all_matched_gdf = gpd.read_file(all_matched_geojson)
    print(f"所有匹配结果GeoJSON行数: {len(all_matched_gdf)}")
    
    # 检查所有匹配结果的几何信息
    valid_geometry_count = all_matched_gdf.geometry.notna().sum()
    print(f"有效几何信息记录数: {valid_geometry_count}/{len(all_matched_gdf)} ({valid_geometry_count/len(all_matched_gdf):.2%})")
    
    # 按匹配来源统计有效几何信息
    if 'match_source' in all_matched_gdf.columns:
        print("\n按匹配来源统计有效几何信息:")
        match_sources = all_matched_gdf['match_source'].unique()
        
        for source in match_sources:
            source_gdf = all_matched_gdf[all_matched_gdf['match_source'] == source]
            valid_count = source_gdf.geometry.notna().sum()
            print(f"{source}: {valid_count}/{len(source_gdf)} ({valid_count/len(source_gdf):.2%})")
    
    # 检查各步骤匹配结果的几何信息
    step_results = {}
    
    # 步骤1: 直接ID匹配
    if 1 in available_steps:
        print("\n检查步骤1(直接ID匹配)的几何信息...")
        step1_df = pd.read_csv(step1_matched_path)
        step1_df['gis_code'] = step1_df['gis_code'].astype(str)
        
        # 检查gis_code是否在geometry_dict中
        found_count = sum(step1_df['gis_code'].isin(geometry_dict.keys()))
        print(f"在geometry_dict中找到的gis_code: {found_count}/{len(step1_df)} ({found_count/len(step1_df):.2%})")
        
        # 在all_matched_gdf中查找对应的记录
        if 'match_source' in all_matched_gdf.columns:
            step1_in_geojson = all_matched_gdf[all_matched_gdf['match_source'] == '直接ID匹配']
            valid_count = step1_in_geojson.geometry.notna().sum()
            print(f"GeoJSON中有效几何信息记录数: {valid_count}/{len(step1_in_geojson)} ({valid_count/len(step1_in_geojson):.2%})")
        
        step_results[1] = {
            'name': '直接ID匹配',
            'total': len(step1_df),
            'found_in_dict': found_count,
            'valid_in_geojson': valid_count if 'match_source' in all_matched_gdf.columns else 0
        }
    
    # 步骤2: 地名匹配
    if 2 in available_steps:
        print("\n检查步骤2(地名匹配)的几何信息...")
        step2_df = pd.read_csv(step2_matched_path)
        step2_df['gis_code'] = step2_df['gis_code'].astype(str)
        
        # 检查gis_code是否在geometry_dict中
        found_count = sum(step2_df['gis_code'].isin(geometry_dict.keys()))
        print(f"在geometry_dict中找到的gis_code: {found_count}/{len(step2_df)} ({found_count/len(step2_df):.2%})")
        
        # 在all_matched_gdf中查找对应的记录
        if 'match_source' in all_matched_gdf.columns:
            step2_in_geojson = all_matched_gdf[all_matched_gdf['match_source'] == '地名匹配']
            valid_count = step2_in_geojson.geometry.notna().sum()
            print(f"GeoJSON中有效几何信息记录数: {valid_count}/{len(step2_in_geojson)} ({valid_count/len(step2_in_geojson):.2%})")
        
        step_results[2] = {
            'name': '地名匹配',
            'total': len(step2_df),
            'found_in_dict': found_count,
            'valid_in_geojson': valid_count if 'match_source' in all_matched_gdf.columns else 0
        }
    
    # 步骤3: 映射表匹配
    if 3 in available_steps:
        print("\n检查步骤3(映射表匹配)的几何信息...")
        step3_df = pd.read_csv(step3_matched_path)
        step3_df['gis_code'] = step3_df['gis_code'].astype(str)
        
        # 检查gis_code是否在geometry_dict中
        found_count = sum(step3_df['gis_code'].isin(geometry_dict.keys()))
        print(f"在geometry_dict中找到的gis_code: {found_count}/{len(step3_df)} ({found_count/len(step3_df):.2%})")
        
        # 在all_matched_gdf中查找对应的记录
        if 'match_source' in all_matched_gdf.columns:
            step3_in_geojson = all_matched_gdf[all_matched_gdf['match_source'] == '映射表匹配']
            valid_count = step3_in_geojson.geometry.notna().sum()
            print(f"GeoJSON中有效几何信息记录数: {valid_count}/{len(step3_in_geojson)} ({valid_count/len(step3_in_geojson):.2%})")
        
        step_results[3] = {
            'name': '映射表匹配',
            'total': len(step3_df),
            'found_in_dict': found_count,
            'valid_in_geojson': valid_count if 'match_source' in all_matched_gdf.columns else 0
        }
    
    # 创建汇总表格
    print("\n创建汇总表格...")
    summary_data = []
    
    for step, result in step_results.items():
        summary_data.append({
            '步骤': f"步骤{step}: {result['name']}",
            '总记录数': result['total'],
            '在geometry_dict中找到的记录数': result['found_in_dict'],
            '在geometry_dict中找到的比例': f"{result['found_in_dict']/result['total']:.2%}",
            'GeoJSON中有效几何信息记录数': result['valid_in_geojson'],
            'GeoJSON中有效几何信息比例': f"{result['valid_in_geojson']/result['total']:.2%}" if result['total'] > 0 else "N/A"
        })
    
    summary_df = pd.DataFrame(summary_data)
    summary_file = os.path.join(output_dir, 'geometry_check_summary.csv')
    summary_df.to_csv(summary_file, index=False)
    print(f"汇总表格已保存到: {summary_file}")
    
    # 创建可视化
    print("\n创建可视化...")
    
    # 创建地图，显示各步骤匹配结果的几何信息
    if 'match_source' in all_matched_gdf.columns:
        # 定义颜色映射
        colors = {
            '直接ID匹配': '#1f77b4',  # 蓝色
            '地名匹配': '#ff7f0e',    # 橙色
            '映射表匹配': '#2ca02c'   # 绿色
        }
        
        # 创建地图
        fig, ax = plt.subplots(1, 1, figsize=(15, 10))
        
        # 绘制原始GIS数据（浅灰色）
        original_gdf.plot(
            ax=ax,
            color='#f0f0f0',
            edgecolor='#d0d0d0',
            linewidth=0.1
        )
        
        # 按匹配来源绘制
        for source in match_sources:
            source_gdf = all_matched_gdf[all_matched_gdf['match_source'] == source]
            valid_source_gdf = source_gdf[source_gdf.geometry.notna()]
            
            if len(valid_source_gdf) > 0:
                valid_source_gdf.plot(
                    ax=ax,
                    color=colors.get(source, '#999999'),
                    edgecolor='black',
                    linewidth=0.1,
                    alpha=0.7
                )
        
        # 添加图例
        patches = []
        for source in match_sources:
            if source in colors:
                patches.append(mpatches.Patch(color=colors[source], label=source))
        
        ax.legend(handles=patches, loc='lower right', fontsize=12)
        
        # 设置标题
        ax.set_title('各匹配步骤的几何信息分布', fontsize=16)
        
        # 关闭坐标轴
        ax.set_axis_off()
        
        # 保存地图
        map_file = os.path.join(output_dir, 'matching_steps_geometry_map.png')
        plt.savefig(map_file, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"地图已保存到: {map_file}")
    
    print("\n检查完成!")

if __name__ == "__main__":
    main()