import pandas as pd
import numpy as np
import os
import pyreadstat

# 检查是否安装了pyreadstat
try:
    import pyreadstat
except ImportError:
    print("正在安装pyreadstat...")
    import subprocess
    subprocess.check_call(["pip", "install", "pyreadstat"])
    import pyreadstat

print("正在读取文件...")

# 读取Stata文件
try:
    bank_df, meta = pyreadstat.read_dta("bank_variables.dta")
    print("成功读取bank_variables.dta")
    print(f"bank_variables.dta形状: {bank_df.shape}")
    print("前5行数据:")
    print(bank_df.head())
    print("\n列名:")
    print(bank_df.columns.tolist())
except Exception as e:
    print(f"读取bank_variables.dta时出错: {e}")

# 读取CSV文件
try:
    # 先尝试读取前几行来检查文件结构
    sample_df = pd.read_csv("matching_results/all_matched.csv", header=None, nrows=5)
    # 检查第一行是否是列名
    if sample_df.iloc[0, 0] == 'year' and sample_df.iloc[0, 1] == 'countyid':
        # 如果第一行是列名，则使用它作为header
        matched_df = pd.read_csv("matching_results/all_matched.csv", header=0)
    else:
        # 否则不使用header
        matched_df = pd.read_csv("matching_results/all_matched.csv", header=None)
    print("\n成功读取all_matched.csv")
    print(f"all_matched.csv形状: {matched_df.shape}")
    print("前5行数据:")
    print(matched_df.head())
except Exception as e:
    print(f"读取all_matched.csv时出错: {e}")

# 如果成功读取了两个文件，继续分析
if 'bank_df' in locals() and 'matched_df' in locals() and 'sample_df' in locals():
    # 检查是否需要为all_matched.csv添加列名
    if sample_df.iloc[0, 0] != 'year' or sample_df.iloc[0, 1] != 'countyid':
        # 如果第一行不是列名，则添加列名
        matched_df.columns = ['year', 'countyid'] + [f'col_{i}' for i in range(2, matched_df.shape[1])]

    print("\nall_matched.csv的列名:")
    print(matched_df.columns.tolist()[:10] + ['...'])  # 只显示前10列

    # 检查bank_df中是否有您提到的列
    required_cols = ['year_PSBC', 'bank_type', 'InstituionCode', 'number_all_PSBC', 'number_PSBC', 'countyid']
    missing_cols = [col for col in required_cols if col not in bank_df.columns]

    if missing_cols:
        print(f"\n警告: bank_variables.dta中缺少以下列: {missing_cols}")
        # 尝试查找类似的列名
        for missing_col in missing_cols:
            similar_cols = [col for col in bank_df.columns if missing_col.lower() in col.lower()]
            if similar_cols:
                print(f"  '{missing_col}'可能对应以下列: {similar_cols}")

    # 创建唯一键
    print("\n正在创建唯一键...")

    # 确保countyid和year列是正确的数据类型
    if 'countyid' in bank_df.columns:
        bank_df['countyid'] = bank_df['countyid'].astype(str)
    if 'year' in bank_df.columns:
        bank_df['year'] = bank_df['year'].astype(int)
    elif 'year_PSBC' in bank_df.columns:
        bank_df['year'] = bank_df['year_PSBC'].astype(int)

    matched_df['countyid'] = matched_df['countyid'].astype(str)
    matched_df['year'] = matched_df['year'].astype(int)

    # 创建唯一键
    if 'countyid' in bank_df.columns and 'year' in bank_df.columns:
        bank_df['key'] = bank_df['countyid'] + '_' + bank_df['year'].astype(str)
        print(f"bank_df中的唯一键数量: {bank_df['key'].nunique()}")
    else:
        print("无法在bank_df中创建唯一键，因为缺少必要的列")

    matched_df['key'] = matched_df['countyid'].astype(str) + '_' + matched_df['year'].astype(str)
    print(f"matched_df中的唯一键数量: {matched_df['key'].nunique()}")

    # 分析键的重叠情况
    if 'key' in bank_df.columns and 'key' in matched_df.columns:
        bank_keys = set(bank_df['key'])
        matched_keys = set(matched_df['key'])

        only_in_bank = bank_keys - matched_keys
        only_in_matched = matched_keys - bank_keys
        in_both = bank_keys.intersection(matched_keys)

        print(f"\n只存在于bank_variables.dta中的countyid+year组合数量: {len(only_in_bank)}")
        print(f"只存在于all_matched.csv中的countyid+year组合数量: {len(only_in_matched)}")
        print(f"同时存在于两个文件中的countyid+year组合数量: {len(in_both)}")

        # 显示一些只存在于一个文件中的键的例子
        if only_in_bank:
            print("\n只存在于bank_variables.dta中的键示例:")
            for key in list(only_in_bank)[:5]:
                print(f"  {key}")
            if len(only_in_bank) > 5:
                print(f"  ... 以及其他 {len(only_in_bank) - 5} 个")

        if only_in_matched:
            print("\n只存在于all_matched.csv中的键示例:")
            for key in list(only_in_matched)[:5]:
                print(f"  {key}")
            if len(only_in_matched) > 5:
                print(f"  ... 以及其他 {len(only_in_matched) - 5} 个")

        # 尝试合并数据
        print("\n尝试进行left merge...")
        if 'countyid' in bank_df.columns and 'year' in bank_df.columns:
            # 选择要合并的列
            merge_cols = [col for col in required_cols if col in bank_df.columns]
            bank_subset = bank_df[merge_cols]

            # 确保bank_df中的year列是整数类型
            if 'year' in bank_subset.columns:
                bank_subset['year'] = bank_subset['year'].astype(int)

            # 打印合并前的数据类型
            print("\n合并前的数据类型:")
            print(f"matched_df['year']的类型: {matched_df['year'].dtype}")
            print(f"matched_df['countyid']的类型: {matched_df['countyid'].dtype}")
            if 'year' in bank_subset.columns:
                print(f"bank_subset['year']的类型: {bank_subset['year'].dtype}")
            print(f"bank_subset['countyid']的类型: {bank_subset['countyid'].dtype}")

            # 进行left merge
            if 'year' in bank_subset.columns:
                merged_df = pd.merge(
                    matched_df,
                    bank_subset,
                    left_on=['countyid', 'year'],
                    right_on=['countyid', 'year'],
                    how='left'
                )
            else:
                # 如果bank_subset中没有year列，则使用year_PSBC列
                merged_df = pd.merge(
                    matched_df,
                    bank_subset,
                    left_on=['countyid', 'year'],
                    right_on=['countyid', 'year_PSBC'],
                    how='left'
                )

            print(f"合并后的数据形状: {merged_df.shape}")
            print("合并后的前5行数据:")
            print(merged_df.head())

            # 检查合并后的空值情况
            null_counts = merged_df[merge_cols].isnull().sum()
            print("\n合并后的空值数量:")
            for col, count in null_counts.items():
                print(f"  {col}: {count} ({count/len(merged_df)*100:.2f}%)")
        else:
            print("无法进行合并，因为bank_df中缺少必要的列")
else:
    print("由于无法读取文件，无法进行进一步分析")
