#%%
import geopandas as gpd
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import os

print("开始可视化银行数据...")

# 读取合并后的GeoJSON文件
geo_file = "matching_results/all_matched_with_bank.geojson"
print(f"读取文件: {geo_file}")
gdf = gpd.read_file(geo_file)

# 创建输出目录
output_dir = "bank_maps"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    print(f"创建输出目录: {output_dir}")

# 筛选样本：year在97到15之间，且gis_county_type不是"市辖区"
sample_data = gdf[(gdf['year'] >= 1997) & (gdf['year'] <= 2015) & (gdf['gis_county_type'] != "市辖区")].copy()
print(f"筛选后的样本数量: {len(sample_data)}")

# 创建PSBC开点数目柱状图（基于number_all_PSBC字段）
print("\n创建PSBC开点数目柱状图（基于number_all_PSBC字段）...")

# 准备数据：计算每年number_all_PSBC不为0的county数量
years = range(1997, 2016)
counties_with_psbc = []

for year in years:
    # 筛选当年数据
    year_data = sample_data[sample_data['year'] == year]
    
    # 计算当年number_all_PSBC不为0的county数量
    # 注意：这里假设number_all_PSBC为0表示没有PSBC网点，大于0表示有PSBC网点
    counties_with_bank = year_data[
        (year_data['number_all_PSBC'].notna()) & 
        (year_data['number_all_PSBC'] > 0)
    ]['countyid'].nunique()
    
    counties_with_psbc.append(counties_with_bank)
    print(f"{year}年number_all_PSBC>0的county数量: {counties_with_bank}")

# 创建柱状图
fig, ax = plt.subplots(figsize=(12, 6))
bars = ax.bar(years, counties_with_psbc, color='#3182bd', width=0.7)

# 在柱子上方添加数值标签
for bar, count in zip(bars, counties_with_psbc):
    ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 5, 
            str(count), ha='center', va='bottom', fontsize=9)

# 设置图表标题和轴标签
ax.set_title('Number of Counties with PSBC Branches (Based on number_all_PSBC > 0)', fontsize=14)
ax.set_xlabel('Year', fontsize=12)
ax.set_ylabel('Number of Counties', fontsize=12)

# 设置x轴刻度
ax.set_xticks(years)
ax.set_xticklabels([str(year) for year in years], rotation=45)

# 添加网格线
ax.grid(axis='y', linestyle='--', alpha=0.7)

# 调整布局
plt.tight_layout()

# 保存图表
output_file = os.path.join(output_dir, "psbc_counties_by_number_all_PSBC.png")
plt.savefig(output_file, dpi=300, bbox_inches='tight')
plt.close()
print(f"保存柱状图到: {output_file}")
#%%
