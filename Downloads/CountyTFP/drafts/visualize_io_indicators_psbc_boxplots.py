"""
Script for generating boxplots of input-output indicators grouped by PSBC presence
"""

import pandas as pd
import numpy as np
import os
import traceback
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from load_geo_data import load_geo_data

# Set debug mode
DEBUG = True

print("Starting to generate PSBC presence group boxplots...")

# Load data using cache mechanism
try:
    print("Loading cached data...")
    gdf = load_geo_data(force_reload=False)
    print(f"Data shape: {gdf.shape}")
    
    # Check if data is empty
    if gdf.empty:
        raise ValueError("Loaded data is empty")
except Exception as e:
    print(f"Error loading data: {e}")
    if DEBUG:
        traceback.print_exc()
    exit(1)

# Ensure countyid is string type
gdf['countyid'] = gdf['countyid'].astype(str)

# Filter samples: years between 1997 and 2015, exclude urban districts and prefecture-level cities
filtered_data = gdf[
    (gdf['year'] >= 1997) & 
    (gdf['year'] <= 2015) & 
    (gdf['gis_county_type'] != "市辖区") &
    (~gdf['countyid'].str.endswith('00'))  # Exclude prefecture-level cities
].copy()

print(f"Number of samples after filtering: {len(filtered_data)}")
print(f"Number of unique countyids after filtering: {filtered_data['countyid'].nunique()}")
print(f"Year range after filtering: {filtered_data['year'].min()} to {filtered_data['year'].max()}")

# Create PSBC presence 0-1 variable
print("\nCreating PSBC presence 0-1 variable...")

# Ensure number_all_PSBC column exists
if 'number_all_PSBC' not in filtered_data.columns:
    print("Warning: number_all_PSBC column does not exist in the data, please check column names")
    print("Available column names:")
    for col in filtered_data.columns:
        print(f"  - {col}")
    
    # Check for similar column names
    similar_cols = [col for col in filtered_data.columns if 'psbc' in col.lower() or 'branch' in col.lower()]
    if similar_cols:
        print("\nFound potentially related columns:")
        for col in similar_cols:
            print(f"  - {col}")
    
    # Create an empty number_all_PSBC column
    filtered_data['number_all_PSBC'] = np.nan

# Convert number_all_PSBC to numeric and fill NaN with 0
filtered_data['number_all_PSBC'] = pd.to_numeric(filtered_data['number_all_PSBC'], errors='coerce').fillna(0)

# Create PSBC presence 0-1 variable
filtered_data['has_psbc'] = (filtered_data['number_all_PSBC'] > 0).astype(int)

# Count the number of counties with PSBC
counties_with_psbc = filtered_data[filtered_data['has_psbc'] == 1]['countyid'].nunique()
total_counties = filtered_data['countyid'].nunique()
print(f"\nNumber of counties with PSBC: {counties_with_psbc} (Percentage: {counties_with_psbc/total_counties*100:.2f}%)")

# Count the number of counties with PSBC by year
print("\nNumber of counties with PSBC by year:")
for year in sorted(filtered_data['year'].unique()):
    year_data = filtered_data[filtered_data['year'] == year]
    year_counties_with_psbc = year_data[year_data['has_psbc'] == 1]['countyid'].nunique()
    year_total_counties = year_data['countyid'].nunique()
    
    print(f"  Year {year}: {year_counties_with_psbc}/{year_total_counties} ({year_counties_with_psbc/year_total_counties*100:.2f}%)")

# Check the distribution of the 0-1 variable
print(f"\nNumber of records with has_psbc=1: {filtered_data['has_psbc'].sum()}")
print(f"Number of records with has_psbc=0: {(filtered_data['has_psbc'] == 0).sum()}")

# Calculate input-output indicators
print("\nCalculating input-output indicators...")

# 1. TFP (Total Factor Productivity) - Use tfp column directly from the data
print("1. Using TFP values from the data")
# TFP is already in the data, no need to calculate

# 2. Y/L (Labor Productivity) - agr_outputq / qlabor
print("2. Calculating Labor Productivity (Y/L)")
filtered_data['Y_L'] = filtered_data['agr_outputq'] / filtered_data['qlabor']

# 3. Y/K (Capital Productivity) - agr_outputq / qcapital
print("3. Calculating Capital Productivity (Y/K)")
filtered_data['Y_K'] = filtered_data['agr_outputq'] / filtered_data['qcapital']

# 4. Y/Land (Land Productivity) - agr_outputq / qland
print("4. Calculating Land Productivity (Y/Land)")
filtered_data['Y_Land'] = filtered_data['agr_outputq'] / filtered_data['qland']

# 5. K/L (Capital-Labor Ratio) - qcapital / qlabor
print("5. Calculating Capital-Labor Ratio (K/L)")
filtered_data['K_L'] = filtered_data['qcapital'] / filtered_data['qlabor']

# 6. Land/L (Land-Labor Ratio) - qland / qlabor
print("6. Calculating Land-Labor Ratio (Land/L)")
filtered_data['Land_L'] = filtered_data['qland'] / filtered_data['qlabor']

# 7. Unit Cost (if cost data is available)
print("7. Calculating Unit Cost")
# Check if cost data columns exist
cost_columns = [col for col in ['labor_cost', 'capital_cost', 'land_cost', 'material_cost', 'intermediate_cost'] 
                if col in filtered_data.columns]

if cost_columns:
    # Calculate total cost = sum of all input costs
    filtered_data['total_cost'] = filtered_data[cost_columns].sum(axis=1)
    
    # Calculate unit cost = total cost / output
    filtered_data['unitcost'] = filtered_data['total_cost'] / filtered_data['agr_outputq']
    
    print(f"   Calculating total cost: {' + '.join(cost_columns)}")
    print(f"   Calculating unit cost: total_cost / agr_outputq")
else:
    print("   Warning: Cannot calculate unit cost, missing cost data columns")
    # Create an empty unitcost column
    filtered_data['unitcost'] = np.nan

# Define indicators to plot and their information
indicator_info = {
    'tfp': {
        'name': 'TFP',
        'title': 'Total Factor Productivity (TFP)',
        'y_label': 'TFP Value',
        'percentile_range': (1, 99)  # For removing outliers
    },
    'Y_L': {
        'name': 'Y/L',
        'title': 'Labor Productivity (Y/L)',
        'y_label': 'Output/Labor Input',
        'percentile_range': (1, 99)
    },
    'Y_K': {
        'name': 'Y/K',
        'title': 'Capital Productivity (Y/K)',
        'y_label': 'Output/Capital Input',
        'percentile_range': (1, 99)
    },
    'Y_Land': {
        'name': 'Y/Land',
        'title': 'Land Productivity (Y/Land)',
        'y_label': 'Output/Land Input',
        'percentile_range': (1, 99)
    },
    'K_L': {
        'name': 'K/L',
        'title': 'Capital-Labor Ratio (K/L)',
        'y_label': 'Capital Input/Labor Input',
        'percentile_range': (1, 99)
    },
    'Land_L': {
        'name': 'Land/L',
        'title': 'Land-Labor Ratio (Land/L)',
        'y_label': 'Land Input/Labor Input',
        'percentile_range': (1, 99)
    },
    'unitcost': {
        'name': 'Unit Cost',
        'title': 'Unit Cost',
        'y_label': 'Total Cost/Output',
        'percentile_range': (1, 99)
    }
}

# Create output directory
output_dir = "psbc_boxplots"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    print(f"Created output directory: {output_dir}")

# Create boxplots for each indicator
for indicator, info in indicator_info.items():
    print(f"\nCreating boxplot for {indicator} indicator...")
    
    # Check data validity
    valid_data = filtered_data[filtered_data[indicator].notna()].copy()
    if len(valid_data) == 0:
        print(f"Warning: No valid data for {indicator} indicator, skipping")
        continue
    
    # Remove outliers
    if 'percentile_range' in info:
        lower_bound = valid_data[indicator].quantile(info['percentile_range'][0] / 100)
        upper_bound = valid_data[indicator].quantile(info['percentile_range'][1] / 100)
        valid_data = valid_data[(valid_data[indicator] >= lower_bound) & (valid_data[indicator] <= upper_bound)]
        print(f"  Number of samples after removing outliers: {len(valid_data)}")
    
    # Create group labels
    valid_data['psbc_status'] = valid_data['has_psbc'].apply(
        lambda x: 'With PSBC' if x == 1 else 'Without PSBC'
    )
    
    # Get list of years
    years = sorted(valid_data['year'].unique())
    
    # Create chart - increase chart size to accommodate larger fonts
    fig, ax = plt.subplots(figsize=(16, 9))
    
    # Set boxplot positions
    positions = []
    labels = []
    
    # Use colorblind-friendly and black-and-white print-friendly color scheme
    without_psbc_color = '#4575b4'  # Deep blue
    with_psbc_color = '#d73027'     # Deep red
    
    # Create boxplot data
    boxplot_data = []
    boxplot_colors = []
    
    for i, year in enumerate(years):
        # Each year has two boxes, positioned at 2i and 2i+1
        positions.extend([2*i, 2*i+1])
        labels.extend([str(year), ''])
        
        # Get data for the current year
        year_data = valid_data[valid_data['year'] == year]
        
        # Data without PSBC
        no_psbc_data = year_data[year_data['has_psbc'] == 0][indicator].dropna()
        boxplot_data.append(no_psbc_data)
        boxplot_colors.append(without_psbc_color)
        
        # Data with PSBC
        with_psbc_data = year_data[year_data['has_psbc'] == 1][indicator].dropna()
        boxplot_data.append(with_psbc_data)
        boxplot_colors.append(with_psbc_color)
        
        # Print the number of samples for each group by year
        print(f"  Year {year}: Without PSBC {len(no_psbc_data)} samples, With PSBC {len(with_psbc_data)} samples")
    
    # Draw boxplot
    boxprops = dict(linewidth=1.5)
    whiskerprops = dict(linewidth=1.5)
    capprops = dict(linewidth=1.5)
    medianprops = dict(linewidth=2, color='black')
    
    bplot = ax.boxplot(
        boxplot_data,
        positions=positions,
        widths=0.7,
        patch_artist=True,
        showfliers=False,  # Don't show outlier points
        boxprops=boxprops,
        whiskerprops=whiskerprops,
        capprops=capprops,
        medianprops=medianprops
    )
    
    # Set box colors
    for patch, color in zip(bplot['boxes'], boxplot_colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)
    
    # Set x-axis labels
    ax.set_xticks(positions[::2])  # Only show labels at the first position of each year
    ax.set_xticklabels(labels[::2], fontsize=12)
    
    # Set chart title and axis labels
    ax.set_title(info['title'], fontsize=16, pad=20)
    ax.set_xlabel('Year', fontsize=14, labelpad=10)
    ax.set_ylabel(info['y_label'], fontsize=14, labelpad=10)
    
    # Add legend
    import matplotlib.patches as mpatches
    without_psbc_patch = mpatches.Patch(color=without_psbc_color, alpha=0.7, label='Without PSBC')
    with_psbc_patch = mpatches.Patch(color=with_psbc_color, alpha=0.7, label='With PSBC')
    ax.legend(handles=[without_psbc_patch, with_psbc_patch], loc='upper right', fontsize=12)
    
    # Save chart
    output_path = os.path.join(output_dir, f"{indicator}_boxplot.png")
    plt.savefig(output_path, bbox_inches='tight', dpi=300)
    print(f"Chart saved to: {output_path}")
    
    # Close the figure to free memory
    plt.close(fig)

print("\nAll boxplots have been generated successfully.")