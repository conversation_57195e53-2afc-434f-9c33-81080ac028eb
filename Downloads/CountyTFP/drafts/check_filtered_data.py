import pandas as pd
import geopandas as gpd
import matplotlib.pyplot as plt
import numpy as np
from load_geo_data import load_geo_data

print("开始检查数据过滤情况...")

# 使用缓存机制加载数据
gdf = load_geo_data()

# 确保countyid是字符串类型
gdf['countyid'] = gdf['countyid'].astype(str)

# 检查原始数据中的省份分布
if 'gis_province' in gdf.columns:
    print("\n原始数据中的省份分布:")
    province_counts = gdf['gis_province'].value_counts()
    for province, count in province_counts.items():
        print(f"  {province}: {count}条记录")

# 检查原始数据中的年份分布
print("\n原始数据中的年份分布:")
year_counts = gdf['year'].value_counts().sort_index()
for year, count in year_counts.items():
    print(f"  {year}: {count}条记录")

# 检查原始数据中的gis_county_type分布
if 'gis_county_type' in gdf.columns:
    print("\ngis_county_type分布:")
    county_type_counts = gdf['gis_county_type'].value_counts()
    for county_type, count in county_type_counts.items():
        print(f"  {county_type}: {count}条记录")

# 检查countyid以00结尾的情况
ending_with_00 = gdf[gdf['countyid'].str.endswith('00')]
print(f"\ncountyid以00结尾的记录数: {len(ending_with_00)}")
if 'gis_county_type' in ending_with_00.columns:
    print("countyid以00结尾的记录的gis_county_type分布:")
    ending_00_types = ending_with_00['gis_county_type'].value_counts()
    for county_type, count in ending_00_types.items():
        print(f"  {county_type}: {count}条记录")

# 检查number_all_PSBC的分布
print("\nnumber_all_PSBC的分布:")
gdf['number_all_PSBC'] = pd.to_numeric(gdf['number_all_PSBC'], errors='coerce')
print(f"  非空值数量: {gdf['number_all_PSBC'].count()}")
print(f"  为0的记录数: {(gdf['number_all_PSBC'] == 0).sum()}")
print(f"  大于0的记录数: {(gdf['number_all_PSBC'] > 0).sum()}")
print(f"  最小值: {gdf['number_all_PSBC'].min()}")
print(f"  最大值: {gdf['number_all_PSBC'].max()}")

# 应用与原脚本相同的过滤条件
filtered_data = gdf[
    (gdf['year'] >= 2001) & 
    (gdf['year'] <= 2015) & 
    (gdf['gis_county_type'] != "市辖区") &
    (~gdf['countyid'].str.endswith('00'))  # 排除地级市
].copy()

print(f"\n筛选后的样本数量: {len(filtered_data)}")
print(f"筛选后的唯一countyid数量: {filtered_data['countyid'].nunique()}")

# 检查筛选后的省份分布
if 'gis_province' in filtered_data.columns:
    print("\n筛选后的省份分布:")
    filtered_province_counts = filtered_data['gis_province'].value_counts()
    for province, count in filtered_province_counts.items():
        print(f"  {province}: {count}条记录")

# 检查筛选后的年份分布
print("\n筛选后的年份分布:")
filtered_year_counts = filtered_data['year'].value_counts().sort_index()
for year, count in filtered_year_counts.items():
    print(f"  {year}: {count}条记录")

# 检查筛选后的number_all_PSBC分布
print("\n筛选后的number_all_PSBC分布:")
print(f"  非空值数量: {filtered_data['number_all_PSBC'].count()}")
print(f"  为0的记录数: {(filtered_data['number_all_PSBC'] == 0).sum()}")
print(f"  大于0的记录数: {(filtered_data['number_all_PSBC'] > 0).sum()}")

# 检查辽宁和湖南的数据情况
for province in ['辽宁省', '湖南省']:
    if 'gis_province' in gdf.columns:
        province_data = gdf[gdf['gis_province'] == province]
        filtered_province_data = filtered_data[filtered_data['gis_province'] == province]
        
        print(f"\n{province}的数据情况:")
        print(f"  原始数据中的记录数: {len(province_data)}")
        print(f"  筛选后的记录数: {len(filtered_province_data)}")
        
        if len(province_data) > 0 and len(filtered_province_data) == 0:
            # 分析为什么该省的数据被全部过滤掉了
            print(f"  {province}数据被过滤的原因分析:")
            
            # 检查年份条件
            year_filter = province_data[
                (province_data['year'] >= 2001) & 
                (province_data['year'] <= 2015)
            ]
            print(f"    符合年份条件(2001-2015)的记录数: {len(year_filter)}")
            
            # 检查county_type条件
            if 'gis_county_type' in province_data.columns:
                county_type_filter = province_data[province_data['gis_county_type'] != "市辖区"]
                print(f"    不是'市辖区'的记录数: {len(county_type_filter)}")
                
                # 检查该省的county_type分布
                province_county_types = province_data['gis_county_type'].value_counts()
                print(f"    {province}的gis_county_type分布:")
                for county_type, count in province_county_types.items():
                    print(f"      {county_type}: {count}条记录")
            
            # 检查countyid条件
            countyid_filter = province_data[~province_data['countyid'].str.endswith('00')]
            print(f"    countyid不以00结尾的记录数: {len(countyid_filter)}")
            
            # 检查综合条件
            combined_filter = province_data[
                (province_data['year'] >= 2001) & 
                (province_data['year'] <= 2015)
            ]
            if 'gis_county_type' in province_data.columns:
                combined_filter = combined_filter[combined_filter['gis_county_type'] != "市辖区"]
            combined_filter = combined_filter[~combined_filter['countyid'].str.endswith('00')]
            print(f"    符合所有条件的记录数: {len(combined_filter)}")

# 创建一个简单的地图，显示原始数据和筛选后数据的地理分布
if isinstance(gdf, gpd.GeoDataFrame) and 'geometry' in gdf.columns:
    print("\n创建地图比较原始数据和筛选后数据的地理分布...")
    
    # 选择一个年份进行比较
    year_to_check = 2010
    
    # 原始数据中该年的数据
    original_year_data = gdf[gdf['year'] == year_to_check]
    # 筛选后该年的数据
    filtered_year_data = filtered_data[filtered_data['year'] == year_to_check]
    
    # 创建地图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))
    
    # 绘制原始数据地图
    original_year_data.plot(
        column='number_all_PSBC',
        ax=ax1,
        legend=True,
        cmap='Blues',
        edgecolor='none'
    )
    ax1.set_title(f'原始数据 - {year_to_check}年 ({len(original_year_data)}个县)', fontsize=14)
    ax1.set_axis_off()
    
    # 绘制筛选后数据地图
    filtered_year_data.plot(
        column='number_all_PSBC',
        ax=ax2,
        legend=True,
        cmap='Blues',
        edgecolor='none'
    )
    ax2.set_title(f'筛选后数据 - {year_to_check}年 ({len(filtered_year_data)}个县)', fontsize=14)
    ax2.set_axis_off()
    
    # 保存地图
    plt.tight_layout()
    plt.savefig('data_comparison_map.png', dpi=300)
    plt.close()
    print("地图已保存为 data_comparison_map.png")

print("\n检查完成!")