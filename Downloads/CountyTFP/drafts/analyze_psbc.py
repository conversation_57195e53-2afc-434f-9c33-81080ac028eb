import pandas as pd
import numpy as np
import os

# 打印当前工作目录
print(f"当前工作目录: {os.getcwd()}")

# 读取Excel文件
file_path = "PSBC.xlsx"
print(f"正在读取文件: {file_path}")

# 读取Excel文件，不使用标题行
df = pd.read_excel(file_path, header=None)

# 显示数据基本信息
print(f"\n数据形状 (行数, 列数): {df.shape}")
print(f"\n数据类型:\n{df.dtypes}")

# 显示前10行数据
print("\n前10行数据:")
print(df.head(10))

# 检查是否有中文内容需要解码
has_chinese = False
for col in df.columns:
    if df[col].dtype == 'object':  # 只检查字符串类型的列
        sample = df[col].dropna().head(1).values
        if len(sample) > 0:
            sample_str = str(sample[0])
            if any('\u4e00' <= char <= '\u9fff' for char in sample_str):
                has_chinese = True
                print(f"\n列 {col} 包含中文内容，示例: {sample_str}")

if not has_chinese:
    print("\n未检测到中文内容")

# 根据数据内容推测每列的含义
print("\n根据数据内容推测每列的含义:")

# 分析每列的数据特征
for col in df.columns:
    col_data = df[col]
    non_null_count = col_data.count()
    unique_count = col_data.nunique()
    
    # 计算唯一值比例
    unique_ratio = unique_count / non_null_count if non_null_count > 0 else 0
    
    # 获取数据类型和示例值
    dtype = col_data.dtype
    examples = col_data.dropna().head(3).tolist()
    examples_str = str(examples)[:100] + "..." if len(str(examples)) > 100 else str(examples)
    
    print(f"列 {col}:")
    print(f"  - 数据类型: {dtype}")
    print(f"  - 非空值数量: {non_null_count}")
    print(f"  - 唯一值数量: {unique_count}")
    print(f"  - 唯一值比例: {unique_ratio:.2f}")
    print(f"  - 示例值: {examples_str}")
    
    # 根据特征推测列的含义
    if unique_ratio == 1.0 and dtype in ['int64', 'float64']:
        print("  - 可能是ID或序号列")
    elif 'date' in str(dtype).lower():
        print("  - 可能是日期列")
    elif dtype == 'object' and any('\u4e00' <= char <= '\u9fff' for char in str(examples)):
        if '银行' in str(examples) or '营业所' in str(examples):
            print("  - 可能是银行或机构名称列")
        elif '省' in str(examples) or '市' in str(examples) or '区' in str(examples):
            print("  - 可能是地址或区域列")
    elif dtype in ['int64', 'float64'] and unique_count < 10:
        print("  - 可能是分类或状态码列")
    
    print("")
