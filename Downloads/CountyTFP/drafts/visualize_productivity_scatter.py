"""
Script for generating scatter plots of productivity indicators grouped by PSBC presence and loan institution presence
"""

import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
import seaborn as sns
from load_geo_data import load_geo_data
from scipy import stats
import matplotlib.patches as mpatches
from statsmodels.nonparametric.smoothers_lowess import lowess

# 设置绘图风格
sns.set_style("whitegrid")
plt.rcParams.update({'font.size': 12})

print("开始生成生产力指标散点图...")

# 创建输出目录
output_dir = "productivity_scatter_plots"
os.makedirs(output_dir, exist_ok=True)

# 加载数据
gdf = load_geo_data(force_reload=False)
print(f"数据形状: {gdf.shape}")

# 确保countyid是字符串类型
gdf['countyid'] = gdf['countyid'].astype(str)

# 筛选样本：排除市辖区和地级市
filtered_data = gdf[
    (gdf['year'] >= 1997) & 
    (gdf['year'] <= 2015) & 
    (gdf['gis_county_type'] != "市辖区") &
    (~gdf['countyid'].str.endswith('00'))  # 排除地级市
].copy()

print(f"筛选后样本数量: {len(filtered_data)}")

# 对2005年和2009年的农业投入产出数据进行插值处理
print("对2005年和2009年的农业投入产出数据进行插值处理...")

# 确保数据按countyid和year排序
filtered_data = filtered_data.sort_values(['countyid', 'year'])

# 获取需要插值的列（只包括农业投入产出相关变量）
input_output_cols = ['agr_outputq', 'qlabor', 'qcapital', 'qland']
for col in input_output_cols:
    if col in filtered_data.columns:
        # 确保列为数值类型
        filtered_data[col] = pd.to_numeric(filtered_data[col], errors='coerce')

# 创建一个完整的年份范围
all_years = list(range(1997, 2016))
missing_years = [2005, 2009]

# 获取所有唯一的countyid
counties = filtered_data['countyid'].unique()

# 创建一个空的DataFrame来存储插值后的数据
interpolated_data = []

# 对每个县进行插值
for county in counties:
    # 获取该县的数据
    county_data = filtered_data[filtered_data['countyid'] == county].copy()
    
    # 如果该县数据少于3个点，跳过插值（需要足够的点来进行插值）
    if len(county_data) < 3:
        interpolated_data.append(county_data)
        continue
    
    # 创建一个包含所有年份的索引
    full_index = pd.DataFrame({'year': all_years})
    
    # 将县数据与完整年份索引合并，创建缺失年份的行
    county_full = pd.merge(full_index, county_data, on='year', how='left')
    
    # 填充非数值列（使用前向填充）
    non_numeric_cols = [col for col in county_full.columns if col not in input_output_cols and col != 'year']
    county_full[non_numeric_cols] = county_full[non_numeric_cols].fillna(method='ffill')
    
    # 检测并处理2005年和2009年的异常值
    if 'agr_outputq' in county_full.columns:
        # 处理2009年的异常值
        year_2009 = county_full[county_full['year'] == 2009]
        if not year_2009.empty and not pd.isna(year_2009['agr_outputq'].values[0]):
            # 获取2008年和2010年的数据
            year_2008 = county_full[county_full['year'] == 2008]
            year_2010 = county_full[county_full['year'] == 2010]
            
            # 如果2008年和2010年都有数据，检查2009年是否异常
            if (not year_2008.empty and not pd.isna(year_2008['agr_outputq'].values[0]) and
                not year_2010.empty and not pd.isna(year_2010['agr_outputq'].values[0])):
                
                # 计算2009年与相邻年份的比率
                ratio_2008 = year_2009['agr_outputq'].values[0] / year_2008['agr_outputq'].values[0] if year_2008['agr_outputq'].values[0] != 0 else float('inf')
                ratio_2010 = year_2009['agr_outputq'].values[0] / year_2010['agr_outputq'].values[0] if year_2010['agr_outputq'].values[0] != 0 else float('inf')
                
                # 如果2009年的值异常低（比率小于0.1），将其标记为缺失
                if ratio_2008 < 0.1 or ratio_2010 < 0.1:
                    print(f"检测到异常值: 县={county}, 2009年农业产出={year_2009['agr_outputq'].values[0]}, "
                          f"比率(2008)={ratio_2008:.4f}, 比率(2010)={ratio_2010:.4f}")
                    county_full.loc[county_full['year'] == 2009, 'agr_outputq'] = np.nan
        
        # 处理2005年的异常值
        year_2005 = county_full[county_full['year'] == 2005]
        if not year_2005.empty and not pd.isna(year_2005['agr_outputq'].values[0]):
            # 获取2004年和2006年的数据
            year_2004 = county_full[county_full['year'] == 2004]
            year_2006 = county_full[county_full['year'] == 2006]
            
            # 如果2004年和2006年都有数据，检查2005年是否异常
            if (not year_2004.empty and not pd.isna(year_2004['agr_outputq'].values[0]) and
                not year_2006.empty and not pd.isna(year_2006['agr_outputq'].values[0])):
                
                # 计算2005年与相邻年份的比率
                ratio_2004 = year_2005['agr_outputq'].values[0] / year_2004['agr_outputq'].values[0] if year_2004['agr_outputq'].values[0] != 0 else float('inf')
                ratio_2006 = year_2005['agr_outputq'].values[0] / year_2006['agr_outputq'].values[0] if year_2006['agr_outputq'].values[0] != 0 else float('inf')
                
                # 如果2005年的值异常低（比率小于0.1），将其标记为缺失
                if ratio_2004 < 0.1 or ratio_2006 < 0.1:
                    print(f"检测到异常值: 县={county}, 2005年农业产出={year_2005['agr_outputq'].values[0]}, "
                          f"比率(2004)={ratio_2004:.4f}, 比率(2006)={ratio_2006:.4f}")
                    county_full.loc[county_full['year'] == 2005, 'agr_outputq'] = np.nan
    
    # 对农业投入产出列进行插值
    for col in input_output_cols:
        if col in county_full.columns:
            # 使用线性插值法
            try:
                county_full[col] = county_full[col].interpolate(method='linear', limit_direction='both')
            except Exception as e:
                # 如果线性插值失败，尝试最近邻插值
                print(f"线性插值失败，使用最近邻插值: {e}")
                county_full[col] = county_full[col].interpolate(method='nearest', limit_direction='both')
    
    # 添加到结果中
    interpolated_data.append(county_full)

# 合并所有插值后的数据
filtered_data = pd.concat(interpolated_data, ignore_index=True)

# 检查插值结果
for year in missing_years:
    year_data = filtered_data[filtered_data['year'] == year]
    print(f"{year}年插值后的样本数量: {len(year_data)}")
    for col in input_output_cols:
        if col in filtered_data.columns:
            valid_count = year_data[col].notna().sum()
            print(f"  {col}有效值数量: {valid_count} ({valid_count/len(year_data)*100:.2f}%)")

# 确保number_all_PSBC为数值类型并填充缺失值
filtered_data['number_all_PSBC'] = pd.to_numeric(filtered_data['number_all_PSBC'], errors='coerce').fillna(0)

# 创建PSBC存在变量（第一组散点图使用）
filtered_data['has_psbc'] = (filtered_data['number_all_PSBC'] > 0).astype(int)

# 创建贷款机构存在变量（第二组散点图使用）
print("创建贷款机构存在变量...")

# 初始化has_loan_inst列为0
filtered_data['has_loan_inst'] = 0

# 按县和年份排序数据
filtered_data = filtered_data.sort_values(['countyid', 'year'])

# 用于存储每个县第一次出现贷款机构的年份
county_first_loan_year = {}

# 遍历数据计算贷款机构存在变量
for idx, row in filtered_data.iterrows():
    county = row['countyid']
    year = row['year']
    
    # 只考虑2007年及以后的数据
    if year >= 2007:
        # 如果该县该年有贷款机构（InstituionCode不为空且不以B0018A开头）
        if pd.notna(row['InstituionCode']) and not str(row['InstituionCode']).startswith('B0018A'):
            # 如果这是该县第一次出现贷款机构，记录年份
            if county not in county_first_loan_year:
                county_first_loan_year[county] = year
    
    # 如果该县已经有了第一次出现贷款机构的记录，且当前年份大于等于那个年份
    if county in county_first_loan_year and year >= county_first_loan_year[county]:
        filtered_data.at[idx, 'has_loan_inst'] = 1

# 计算有贷款机构的县数量
counties_with_loan = len(county_first_loan_year)
total_counties = filtered_data['countyid'].nunique()
print(f"有贷款机构的县数量: {counties_with_loan}/{total_counties} ({counties_with_loan/total_counties*100:.2f}%)")
print(f"有贷款机构的样本数量: {filtered_data['has_loan_inst'].sum()}")

# 计算生产力指标
print("计算生产力指标...")
filtered_data['Y_L'] = filtered_data['agr_outputq'] / filtered_data['qlabor']
filtered_data['K_L'] = filtered_data['qcapital'] / filtered_data['qlabor']
filtered_data['Land_L'] = filtered_data['qland'] / filtered_data['qlabor']

# 定义要绘制的散点图 - 只保留对数变换+去均值版本
scatter_plots = [
    # 对数变换+去均值版本
    {
        'x': 'K_L', 'y': 'Y_L', 
        'xlabel': 'Demeaned Log Capital-Labor Ratio', 
        'ylabel': 'Demeaned Log Labor Productivity',
        'title': 'Demeaned Log Labor Productivity vs Demeaned Log Capital-Labor Ratio',
        'log_transform': True,
        'demean': True
    },
    {
        'x': 'Land_L', 'y': 'Y_L', 
        'xlabel': 'Demeaned Log Land-Labor Ratio', 
        'ylabel': 'Demeaned Log Labor Productivity',
        'title': 'Demeaned Log Labor Productivity vs Demeaned Log Land-Labor Ratio',
        'log_transform': True,
        'demean': True
    }
]

# 定义颜色方案 - 使用专业期刊常用的配色
# PSBC分组颜色 - 使用蓝色系列
no_psbc_color = '#a6cee3'  # 浅蓝色
has_psbc_color = '#1f78b4'  # 深蓝色

# 贷款机构分组颜色 - 使用红色系列
no_loan_color = '#fb9a99'  # 浅红色
has_loan_color = '#e31a1c'  # 深红色

# 创建去均值函数
def demean_by_county(df, columns):
    """对指定列按县进行去均值处理"""
    result = df.copy()
    for col in columns:
        # 计算每个县的均值
        county_means = df.groupby('countyid')[col].transform('mean')
        # 减去县均值
        result[col] = df[col] - county_means
    return result

# 绘制按PSBC存在分组的散点图
for plot_info in scatter_plots:
    plt.figure(figsize=(10, 8))
    
    # 获取数据
    x = plot_info['x']
    y = plot_info['y']
    
    # 按PSBC存在分组
    group0 = filtered_data[filtered_data['has_psbc'] == 0].copy()
    group1 = filtered_data[filtered_data['has_psbc'] == 1].copy()
    
    # 检查是否需要对数变换
    log_transform = plot_info.get('log_transform', False)
    
    if log_transform:
        # 确保数据为正值（对数变换要求）
        group0 = group0[(group0[x] > 0) & (group0[y] > 0)]
        group1 = group1[(group1[x] > 0) & (group1[y] > 0)]
        
        # 应用对数变换
        group0[x] = np.log(group0[x])
        group0[y] = np.log(group0[y])
        group1[x] = np.log(group1[x])
        group1[y] = np.log(group1[y])
    
    # 检查是否需要去均值
    demean = plot_info.get('demean', False)
    
    if demean:
        # 对两组数据分别进行去均值处理
        group0 = demean_by_county(group0, [x, y])
        group1 = demean_by_county(group1, [x, y])
    
    # 绘制散点图 - 使用黑色和灰色，但增加不同的形状和边缘颜色
    plt.scatter(group0[x], group0[y], color='#cccccc', s=25, alpha=0.6, marker='o', 
                edgecolor='#555555', linewidth=0.5, label='Counties without PSBC')
    plt.scatter(group1[x], group1[y], color='#555555', s=25, alpha=0.6, marker='^', 
                edgecolor='black', linewidth=0.5, label='Counties with PSBC')
    
    # 使用LOWESS回归替代线性回归
    # 无PSBC组
    lowess_result0 = lowess(group0[y], group0[x], frac=0.3, it=3, return_sorted=True)
    x_lowess0, y_lowess0 = lowess_result0[:, 0], lowess_result0[:, 1]
    
    # 有PSBC组
    lowess_result1 = lowess(group1[y], group1[x], frac=0.3, it=3, return_sorted=True)
    x_lowess1, y_lowess1 = lowess_result1[:, 0], lowess_result1[:, 1]
    
    # 绘制LOWESS回归曲线 - 使用红蓝对比
    plt.plot(x_lowess0, y_lowess0, color='#1f77b4', linewidth=2.5, linestyle='-', 
             label='LOWESS trend (No PSBC)')
    plt.plot(x_lowess1, y_lowess1, color='#d62728', linewidth=2.5, linestyle='--', 
             label='LOWESS trend (Has PSBC)')
    
    # 添加标题和标签
    plt.title(plot_info['title'], fontsize=16)
    plt.xlabel(plot_info['xlabel'], fontsize=14)
    plt.ylabel(plot_info['ylabel'], fontsize=14)
    
    # 添加图例
    plt.legend(loc='best', fontsize=12)
    
    # 添加网格线
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    suffix = "_log_demean"
    output_file = os.path.join(output_dir, f"{y}_vs_{x}_by_psbc_lowess{suffix}.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"保存LOWESS回归图表: {output_file}")

# 绘制按贷款机构存在分组的散点图
for plot_info in scatter_plots:
    plt.figure(figsize=(10, 8))
    
    # 获取数据
    x = plot_info['x']
    y = plot_info['y']
    
    # 按贷款机构存在分组
    group0 = filtered_data[filtered_data['has_loan_inst'] == 0].copy()
    group1 = filtered_data[filtered_data['has_loan_inst'] == 1].copy()
    
    # 检查是否需要对数变换
    log_transform = plot_info.get('log_transform', False)
    
    if log_transform:
        # 确保数据为正值（对数变换要求）
        group0 = group0[(group0[x] > 0) & (group0[y] > 0)]
        group1 = group1[(group1[x] > 0) & (group1[y] > 0)]
        
        # 应用对数变换
        group0[x] = np.log(group0[x])
        group0[y] = np.log(group0[y])
        group1[x] = np.log(group1[x])
        group1[y] = np.log(group1[y])
    
    # 检查是否需要去均值
    demean = plot_info.get('demean', False)
    
    if demean:
        # 对两组数据分别进行去均值处理
        group0 = demean_by_county(group0, [x, y])
        group1 = demean_by_county(group1, [x, y])
    
    # 绘制散点图 - 使用黑色和灰色，但增加不同的形状和边缘颜色
    plt.scatter(group0[x], group0[y], color='#cccccc', s=25, alpha=0.6, marker='o', 
                edgecolor='#555555', linewidth=0.5, label='Counties without Loan Institution')
    plt.scatter(group1[x], group1[y], color='#555555', s=25, alpha=0.6, marker='^', 
                edgecolor='black', linewidth=0.5, label='Counties with Loan Institution')
    
    # 使用LOWESS回归替代线性回归
    # 无贷款机构组
    lowess_result0 = lowess(group0[y], group0[x], frac=0.3, it=3, return_sorted=True)
    x_lowess0, y_lowess0 = lowess_result0[:, 0], lowess_result0[:, 1]
    
    # 有贷款机构组
    lowess_result1 = lowess(group1[y], group1[x], frac=0.3, it=3, return_sorted=True)
    x_lowess1, y_lowess1 = lowess_result1[:, 0], lowess_result1[:, 1]
    
    # 绘制LOWESS回归曲线 - 使用红蓝对比
    plt.plot(x_lowess0, y_lowess0, color='#1f77b4', linewidth=2.5, linestyle='-', 
             label='LOWESS trend (No Loan Institution)')
    plt.plot(x_lowess1, y_lowess1, color='#d62728', linewidth=2.5, linestyle='--', 
             label='LOWESS trend (Has Loan Institution)')
    
    # 添加标题和标签
    plt.title(plot_info['title'], fontsize=16)
    plt.xlabel(plot_info['xlabel'], fontsize=14)
    plt.ylabel(plot_info['ylabel'], fontsize=14)
    
    # 添加图例
    plt.legend(loc='best', fontsize=12)
    
    # 添加网格线
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    suffix = "_log_demean"
    output_file = os.path.join(output_dir, f"{y}_vs_{x}_by_loan_inst_lowess{suffix}.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"保存LOWESS回归图表: {output_file}")

print("生产力指标散点图生成完成！")

# 在生成散点图之后添加分析代码
print("\n开始分析Land/L比率较高的县的特征...")

# 确保数据为正值并进行对数变换
analysis_data = filtered_data.copy()
analysis_data = analysis_data[(analysis_data['Land_L'] > 0) & (analysis_data['Y_L'] > 0) & (analysis_data['K_L'] > 0)]
analysis_data['log_Land_L'] = np.log(analysis_data['Land_L'])
analysis_data['log_Y_L'] = np.log(analysis_data['Y_L'])
analysis_data['log_K_L'] = np.log(analysis_data['K_L'])

# 对数据进行去均值处理
analysis_data = demean_by_county(analysis_data, ['log_Land_L', 'log_Y_L', 'log_K_L'])

# 定义高Land/L比率的阈值（右侧样本）
high_land_threshold = analysis_data['log_Land_L'].quantile(0.75)
print(f"高Land/L比率阈值（去均值后的对数值）: {high_land_threshold:.4f}")

# 筛选高Land/L比率的样本
high_land_samples = analysis_data[analysis_data['log_Land_L'] > high_land_threshold]
print(f"高Land/L比率样本数量: {len(high_land_samples)}")

# 按PSBC存在分组
high_land_with_psbc = high_land_samples[high_land_samples['has_psbc'] == 1]
high_land_without_psbc = high_land_samples[high_land_samples['has_psbc'] == 0]
print(f"高Land/L比率样本中有PSBC的数量: {len(high_land_with_psbc)}")
print(f"高Land/L比率样本中无PSBC的数量: {len(high_land_without_psbc)}")

# 比较两组的劳动生产率
print("\n比较高Land/L比率样本中有无PSBC的劳动生产率差异:")
print(f"有PSBC组的平均log_Y_L: {high_land_with_psbc['log_Y_L'].mean():.4f}")
print(f"无PSBC组的平均log_Y_L: {high_land_without_psbc['log_Y_L'].mean():.4f}")
print(f"差异: {high_land_with_psbc['log_Y_L'].mean() - high_land_without_psbc['log_Y_L'].mean():.4f}")

# 比较两组的资本-劳动比率
print("\n比较高Land/L比率样本中有无PSBC的资本-劳动比率差异:")
print(f"有PSBC组的平均log_K_L: {high_land_with_psbc['log_K_L'].mean():.4f}")
print(f"无PSBC组的平均log_K_L: {high_land_without_psbc['log_K_L'].mean():.4f}")
print(f"差异: {high_land_with_psbc['log_K_L'].mean() - high_land_without_psbc['log_K_L'].mean():.4f}")

# 检查地理分布
if 'gis_province' in high_land_samples.columns:
    print("\n高Land/L比率样本的省份分布:")
    province_counts = high_land_samples['gis_province'].value_counts().head(10)
    print(province_counts)
    
    print("\n有PSBC的高Land/L比率样本的省份分布:")
    province_counts_with_psbc = high_land_with_psbc['gis_province'].value_counts().head(10)
    print(province_counts_with_psbc)

# 创建一个额外的散点图，显示K/L与Y/L的关系，按PSBC分组
plt.figure(figsize=(10, 8))

# 筛选高Land/L比率的样本
high_land_group0 = high_land_without_psbc.copy()
high_land_group1 = high_land_with_psbc.copy()

# 绘制散点图
plt.scatter(high_land_group0['log_K_L'], high_land_group0['log_Y_L'], color='#cccccc', s=25, alpha=0.6, marker='o', 
            edgecolor='#555555', linewidth=0.5, label='High Land/L Counties without PSBC')
plt.scatter(high_land_group1['log_K_L'], high_land_group1['log_Y_L'], color='#555555', s=25, alpha=0.6, marker='^', 
            edgecolor='black', linewidth=0.5, label='High Land/L Counties with PSBC')

# 使用LOWESS回归
if len(high_land_group0) > 10:  # 确保有足够的样本进行LOWESS回归
    lowess_result0 = lowess(high_land_group0['log_Y_L'], high_land_group0['log_K_L'], frac=0.5, it=3, return_sorted=True)
    x_lowess0, y_lowess0 = lowess_result0[:, 0], lowess_result0[:, 1]
    plt.plot(x_lowess0, y_lowess0, color='#1f77b4', linewidth=2.5, linestyle='-', 
             label='LOWESS trend (No PSBC)')

if len(high_land_group1) > 10:
    lowess_result1 = lowess(high_land_group1['log_Y_L'], high_land_group1['log_K_L'], frac=0.5, it=3, return_sorted=True)
    x_lowess1, y_lowess1 = lowess_result1[:, 0], lowess_result1[:, 1]
    plt.plot(x_lowess1, y_lowess1, color='#d62728', linewidth=2.5, linestyle='--', 
             label='LOWESS trend (Has PSBC)')

# 添加标题和标签
plt.title('Demeaned Log Labor Productivity vs Demeaned Log Capital-Labor Ratio\n(High Land-Labor Ratio Counties)', fontsize=16)
plt.xlabel('Demeaned Log Capital-Labor Ratio', fontsize=14)
plt.ylabel('Demeaned Log Labor Productivity', fontsize=14)

# 添加图例
plt.legend(loc='best', fontsize=12)

# 添加网格线
plt.grid(True, linestyle='--', alpha=0.7)

# 调整布局
plt.tight_layout()

# 保存图表
output_file = os.path.join(output_dir, "Y_L_vs_K_L_high_land_counties_by_psbc.png")
plt.savefig(output_file, dpi=300, bbox_inches='tight')
plt.close()
print(f"保存高Land/L比率县的K/L与Y/L关系图: {output_file}")

# 创建箱线图比较高Land/L比率县中有无PSBC的K/L差异
plt.figure(figsize=(8, 6))
data_to_plot = [high_land_without_psbc['log_K_L'], high_land_with_psbc['log_K_L']]
labels = ['Without PSBC', 'With PSBC']
plt.boxplot(data_to_plot, labels=labels, patch_artist=True,
            boxprops=dict(facecolor='lightblue'),
            medianprops=dict(color='red', linewidth=1.5))

plt.title('Demeaned Log Capital-Labor Ratio in High Land-Labor Ratio Counties', fontsize=16)
plt.ylabel('Demeaned Log Capital-Labor Ratio', fontsize=14)
plt.grid(True, linestyle='--', alpha=0.7)
plt.tight_layout()

output_file = os.path.join(output_dir, "K_L_boxplot_high_land_counties_by_psbc.png")
plt.savefig(output_file, dpi=300, bbox_inches='tight')
plt.close()
print(f"保存高Land/L比率县的K/L箱线图: {output_file}")

# 在之前的分析后添加对低Land/L比率县的分析
print("\n开始分析Land/L比率较低的县的特征...")

# 定义低Land/L比率的阈值（左侧样本）
low_land_threshold = analysis_data['log_Land_L'].quantile(0.25)
print(f"低Land/L比率阈值（去均值后的对数值）: {low_land_threshold:.4f}")

# 筛选低Land/L比率的样本
low_land_samples = analysis_data[analysis_data['log_Land_L'] < low_land_threshold]
print(f"低Land/L比率样本数量: {len(low_land_samples)}")

# 按PSBC存在分组
low_land_with_psbc = low_land_samples[low_land_samples['has_psbc'] == 1]
low_land_without_psbc = low_land_samples[low_land_samples['has_psbc'] == 0]
print(f"低Land/L比率样本中有PSBC的数量: {len(low_land_with_psbc)}")
print(f"低Land/L比率样本中无PSBC的数量: {len(low_land_without_psbc)}")

# 比较两组的劳动生产率
print("\n比较低Land/L比率样本中有无PSBC的劳动生产率差异:")
print(f"有PSBC组的平均log_Y_L: {low_land_with_psbc['log_Y_L'].mean():.4f}")
print(f"无PSBC组的平均log_Y_L: {low_land_without_psbc['log_Y_L'].mean():.4f}")
print(f"差异: {low_land_with_psbc['log_Y_L'].mean() - low_land_without_psbc['log_Y_L'].mean():.4f}")

# 比较两组的资本-劳动比率
print("\n比较低Land/L比率样本中有无PSBC的资本-劳动比率差异:")
print(f"有PSBC组的平均log_K_L: {low_land_with_psbc['log_K_L'].mean():.4f}")
print(f"无PSBC组的平均log_K_L: {low_land_without_psbc['log_K_L'].mean():.4f}")
print(f"差异: {low_land_with_psbc['log_K_L'].mean() - low_land_without_psbc['log_K_L'].mean():.4f}")

# 检查地理分布
if 'gis_province' in low_land_samples.columns:
    print("\n低Land/L比率样本的省份分布:")
    province_counts = low_land_samples['gis_province'].value_counts().head(10)
    print(province_counts)
    
    print("\n有PSBC的低Land/L比率样本的省份分布:")
    province_counts_with_psbc = low_land_with_psbc['gis_province'].value_counts().head(10)
    print(province_counts_with_psbc)

# 创建一个额外的散点图，显示低Land/L比率县中K/L与Y/L的关系，按PSBC分组
plt.figure(figsize=(10, 8))

# 筛选低Land/L比率的样本
low_land_group0 = low_land_without_psbc.copy()
low_land_group1 = low_land_with_psbc.copy()

# 绘制散点图
plt.scatter(low_land_group0['log_K_L'], low_land_group0['log_Y_L'], color='#cccccc', s=25, alpha=0.6, marker='o', 
            edgecolor='#555555', linewidth=0.5, label='Low Land/L Counties without PSBC')
plt.scatter(low_land_group1['log_K_L'], low_land_group1['log_Y_L'], color='#555555', s=25, alpha=0.6, marker='^', 
            edgecolor='black', linewidth=0.5, label='Low Land/L Counties with PSBC')

# 使用LOWESS回归
if len(low_land_group0) > 10:
    lowess_result0 = lowess(low_land_group0['log_Y_L'], low_land_group0['log_K_L'], frac=0.5, it=3, return_sorted=True)
    x_lowess0, y_lowess0 = lowess_result0[:, 0], lowess_result0[:, 1]
    plt.plot(x_lowess0, y_lowess0, color='#1f77b4', linewidth=2.5, linestyle='-', 
             label='LOWESS trend (No PSBC)')

if len(low_land_group1) > 10:
    lowess_result1 = lowess(low_land_group1['log_Y_L'], low_land_group1['log_K_L'], frac=0.5, it=3, return_sorted=True)
    x_lowess1, y_lowess1 = lowess_result1[:, 0], lowess_result1[:, 1]
    plt.plot(x_lowess1, y_lowess1, color='#d62728', linewidth=2.5, linestyle='--', 
             label='LOWESS trend (Has PSBC)')

# 添加标题和标签
plt.title('Demeaned Log Labor Productivity vs Demeaned Log Capital-Labor Ratio\n(Low Land-Labor Ratio Counties)', fontsize=16)
plt.xlabel('Demeaned Log Capital-Labor Ratio', fontsize=14)
plt.ylabel('Demeaned Log Labor Productivity', fontsize=14)

# 添加图例
plt.legend(loc='best', fontsize=12)

# 添加网格线
plt.grid(True, linestyle='--', alpha=0.7)

# 调整布局
plt.tight_layout()

# 保存图表
output_file = os.path.join(output_dir, "Y_L_vs_K_L_low_land_counties_by_psbc.png")
plt.savefig(output_file, dpi=300, bbox_inches='tight')
plt.close()
print(f"保存低Land/L比率县的K/L与Y/L关系图: {output_file}")

# 创建箱线图比较低Land/L比率县中有无PSBC的K/L差异
plt.figure(figsize=(8, 6))
data_to_plot = [low_land_without_psbc['log_K_L'], low_land_with_psbc['log_K_L']]
labels = ['Without PSBC', 'With PSBC']
plt.boxplot(data_to_plot, labels=labels, patch_artist=True,
            boxprops=dict(facecolor='lightblue'),
            medianprops=dict(color='red', linewidth=1.5))

plt.title('Demeaned Log Capital-Labor Ratio in Low Land-Labor Ratio Counties', fontsize=16)
plt.ylabel('Demeaned Log Capital-Labor Ratio', fontsize=14)
plt.grid(True, linestyle='--', alpha=0.7)
plt.tight_layout()

output_file = os.path.join(output_dir, "K_L_boxplot_low_land_counties_by_psbc.png")
plt.savefig(output_file, dpi=300, bbox_inches='tight')
plt.close()
print(f"保存低Land/L比率县的K/L箱线图: {output_file}")

# 添加对作物类型或产业结构的分析（如果数据中有相关变量）
if 'crop_type' in analysis_data.columns:
    print("\n分析不同Land/L比率县的主要作物类型:")
    
    # 高Land/L比率县的作物分布
    high_land_crops = high_land_samples['crop_type'].value_counts()
    print("高Land/L比率县的主要作物类型:")
    print(high_land_crops.head(5))
    
    # 低Land/L比率县的作物分布
    low_land_crops = low_land_samples['crop_type'].value_counts()
    print("低Land/L比率县的主要作物类型:")
    print(low_land_crops.head(5))


