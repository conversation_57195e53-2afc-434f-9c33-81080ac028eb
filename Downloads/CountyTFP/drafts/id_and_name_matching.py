"""
完整的GIS数据匹配流程
包括四个步骤：
1. 直接ID匹配
2. 前两位+地名匹配
3. 映射表匹配
4. 自治州合并匹配
"""

import pandas as pd
import numpy as np
import os
import re
import geopandas as gpd
from shapely.ops import unary_union
from dbfread import DBF

def clean_name(name):
    """清理地名，去除常见后缀如"市"、"县"、"区"等"""
    if pd.isna(name):
        return name

    # 转为字符串
    name = str(name)

    # 去除常见后缀
    suffixes = ['市', '县', '区', '旗', '盟', '自治州', '自治县', '自治区', '特别行政区']
    for suffix in suffixes:
        if name.endswith(suffix):
            name = name[:-len(suffix)]

    # 去除括号及其内容，如"北京(首都)"
    name = re.sub(r'\([^)]*\)', '', name)

    # 去除空格
    name = name.strip()

    return name

def main():
    print("开始GIS数据匹配流程...")

    # 文件路径
    shapefile_path = '2015county/2015county.shp'  # 使用shp文件以获取几何信息
    dbf_path = '2015county/2015county.dbf'  # 仍然需要dbf文件获取属性信息
    io_path = 'IO.dta'  # IO数据文件
    tfp_path = 'CountyTFP.dta'  # TFP数据文件
    mapping_table_path = 'OLD/result.csv'  # 映射表文件
    output_dir = 'matching_results'  # 输出目录

    # 创建输出目录（如果不存在）
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 输出文件路径
    step1_matched_path = os.path.join(output_dir, 'step1_matched.csv')
    step1_unmatched_path = os.path.join(output_dir, 'step1_unmatched.csv')
    step2_matched_path = os.path.join(output_dir, 'step2_matched.csv')
    step2_unmatched_path = os.path.join(output_dir, 'step2_unmatched.csv')
    step3_matched_path = os.path.join(output_dir, 'step3_matched.csv')
    step3_unmatched_path = os.path.join(output_dir, 'step3_unmatched.csv')
    step4_matched_path = os.path.join(output_dir, 'step4_matched.csv')
    step4_unmatched_path = os.path.join(output_dir, 'step4_unmatched.csv')
    all_matched_path = os.path.join(output_dir, 'all_matched.csv')
    # 添加GeoJSON输出路径
    all_matched_geojson_path = os.path.join(output_dir, 'all_matched.geojson')
    autonomous_merged_geojson_path = os.path.join(output_dir, 'autonomous_merged.geojson')

    # 检查文件是否存在
    for path in [shapefile_path, io_path, tfp_path, mapping_table_path]:
        if not os.path.exists(path):
            print(f"错误: 找不到文件 {path}")
            exit(1)

    print(f"找到所有需要的文件")

    try:
        # 读取GIS数据（包含几何信息）
        print("\n读取GIS数据...")
        gis_gdf = gpd.read_file(shapefile_path)
        print(f"GIS数据行数: {len(gis_gdf)}")
        print(f"GIS数据列名: {gis_gdf.columns.tolist()}")

        # 检查是否包含县级类列
        if '县级类' not in gis_gdf.columns:
            print("警告: GIS数据中没有'县级类'列，将创建一个空列")
            gis_gdf['县级类'] = None

        # 将GIS数据转换为DataFrame以便与原代码兼容
        gis_df = pd.DataFrame(gis_gdf.drop(columns='geometry'))
        # 保存几何信息字典，用于后续匹配
        geometry_dict = {row['区划码']: row['geometry'] for _, row in gis_gdf.iterrows()}

        # 读取IO数据
        print("\n读取IO数据...")
        io_df = pd.read_stata(io_path)
        print(f"IO数据行数: {len(io_df)}")
        print(f"IO数据列名: {io_df.columns.tolist()}")

        # 读取TFP数据
        print("\n读取TFP数据...")
        tfp_df = pd.read_stata(tfp_path)
        print(f"TFP数据行数: {len(tfp_df)}")
        print(f"TFP数据列名: {tfp_df.columns.tolist()}")

        # 执行IO和TFP的leftmerge
        print("\n执行IO和TFP的leftmerge...")
        # 确定合并的键
        merge_keys = ['year', 'countyid']  # 根据实际情况调整

        # 执行leftmerge
        io_tfp_df = pd.merge(
            io_df,
            tfp_df,
            on=merge_keys,
            how='left'
        )

        print(f"Leftmerge后的数据行数: {len(io_tfp_df)}")
        print(f"Leftmerge后的数据列名: {io_tfp_df.columns.tolist()}")

        # 步骤1：直接ID匹配
        print("\n开始步骤1：直接ID匹配...")

        # 将GIS数据中的ID列转换为数值类型
        gis_df['区划码'] = pd.to_numeric(gis_df['区划码'], errors='coerce')

        # 创建一个字典，用于存储GIS数据
        county_to_gis = {}
        for _, row in gis_df.iterrows():
            county_id = row['区划码']
            if pd.notna(county_id):
                county_to_gis[int(county_id)] = {
                    'gis_name': row['地名'],
                    'gis_county': row['县级'],
                    'gis_city': row['地级'],
                    'gis_province': row['省级'],
                    'gis_county_type': row['县级类'] if '县级类' in row else None,
                    'gis_city_type': row['地级类'] if '地级类' in row else None,
                    'gis_province_type': row['省级类'] if '省级类' in row else None,
                    'gis_code': county_id
                }

        print(f"GIS数据字典中的条目数: {len(county_to_gis)}")

        # 为IO-TFP数据添加GIS列
        for col in ['gis_name', 'gis_county', 'gis_city', 'gis_province',
                   'gis_county_type', 'gis_city_type', 'gis_province_type', 'gis_code']:
            io_tfp_df[col] = None

        # 匹配数据
        matched_count = 0
        matched_indices = []

        for idx, row in io_tfp_df.iterrows():
            county_id = row['countyid']
            if county_id in county_to_gis:
                matched_count += 1
                matched_indices.append(idx)
                for col, value in county_to_gis[county_id].items():
                    io_tfp_df.loc[idx, col] = value

        print(f"直接匹配成功的记录数: {matched_count}/{len(io_tfp_df)} ({matched_count/len(io_tfp_df):.2%})")

        # 分离匹配和未匹配的记录
        step1_matched = io_tfp_df.loc[matched_indices].copy()
        step1_unmatched = io_tfp_df.drop(matched_indices).copy()

        # 添加匹配来源标记
        step1_matched['match_source'] = '直接ID匹配'

        # 保存步骤1的结果
        print(f"\n保存步骤1匹配结果到 {step1_matched_path}...")
        step1_matched.to_csv(step1_matched_path, index=False)

        print(f"保存步骤1未匹配记录到 {step1_unmatched_path}...")
        step1_unmatched.to_csv(step1_unmatched_path, index=False)

        # 步骤2：前两位+地名匹配
        print("\n开始步骤2：前两位+地名匹配...")

        # 处理GIS数据
        # 将区划码转为字符串并提取前两位作为省份代码
        gis_df_copy = gis_df.copy()
        gis_df_copy['区划码'] = gis_df_copy['区划码'].astype(str)
        gis_df_copy['province_code'] = gis_df_copy['区划码'].str[:2]
        # 清理地名
        gis_df_copy['clean_name'] = gis_df_copy['地名'].apply(clean_name)

        # 处理未匹配数据
        # 将countyid转为字符串并提取前两位作为省份代码
        step1_unmatched['countyid'] = step1_unmatched['countyid'].astype(str)
        step1_unmatched['province_code'] = step1_unmatched['countyid'].str[:2]

        # 确定county_name列的名称
        county_name_col = None
        for col in ['county_name', 'countyname']:
            if col in step1_unmatched.columns:
                county_name_col = col
                break

        if county_name_col is None:
            print("警告: 未找到county_name列，尝试查找包含'county'和'name'的列")
            for col in step1_unmatched.columns:
                if 'county' in col.lower() and 'name' in col.lower():
                    county_name_col = col
                    break

        if county_name_col is None:
            print("错误: 未找到county_name列，无法进行地名匹配")
            print("可用的列: ", step1_unmatched.columns.tolist())
            exit(1)

        print(f"使用'{county_name_col}'列作为地名匹配")

        # 清理地名
        step1_unmatched['clean_name'] = step1_unmatched[county_name_col].apply(clean_name)

        # 创建GIS数据的查找字典
        gis_lookup = {}
        for _, row in gis_df_copy.iterrows():
            key = (row['province_code'], row['clean_name'])
            if key not in gis_lookup:  # 避免重复
                gis_lookup[key] = {
                    'gis_name': row['地名'],
                    'gis_county': row['县级'],
                    'gis_city': row['地级'],
                    'gis_province': row['省级'],
                    'gis_county_type': row['县级类'] if '县级类' in row else None,
                    'gis_city_type': row['地级类'] if '地级类' in row else None,
                    'gis_province_type': row['省级类'] if '省级类' in row else None,
                    'gis_code': row['区划码']
                }

        print(f"GIS查找字典中的条目数: {len(gis_lookup)}")

        # 进行二次匹配
        second_match_count = 0
        matched_indices = []

        # 遍历未匹配数据
        for idx, row in step1_unmatched.iterrows():
            key = (row['province_code'], row['clean_name'])
            if key in gis_lookup:
                second_match_count += 1
                matched_indices.append(idx)
                for col, value in gis_lookup[key].items():
                    step1_unmatched.loc[idx, col] = value

        print(f"前两位+地名匹配成功的记录数: {second_match_count}/{len(step1_unmatched)} ({second_match_count/len(step1_unmatched):.2%})")

        # 分离匹配和未匹配的记录
        step2_matched = step1_unmatched.loc[matched_indices].copy()
        step2_unmatched = step1_unmatched.drop(matched_indices).copy()

        # 添加匹配来源标记
        step2_matched['match_source'] = '省份代码+地名匹配'

        # 删除临时列
        temp_cols = ['province_code', 'clean_name']
        if 'province_code' in step2_matched.columns:
            step2_matched = step2_matched.drop(temp_cols, axis=1)
        if 'province_code' in step2_unmatched.columns:
            step2_unmatched = step2_unmatched.drop(temp_cols, axis=1)

        # 保存步骤2的结果
        print(f"\n保存步骤2匹配结果到 {step2_matched_path}...")
        step2_matched.to_csv(step2_matched_path, index=False)

        print(f"保存步骤2未匹配记录到 {step2_unmatched_path}...")
        step2_unmatched.to_csv(step2_unmatched_path, index=False)

        # 步骤3：映射表匹配
        print("\n开始步骤3：映射表匹配...")

        # 读取映射表
        print("\n读取映射表...")
        mapping_df = pd.read_csv(mapping_table_path)
        print(f"映射表行数: {len(mapping_df)}")
        print(f"映射表列名: {mapping_df.columns.tolist()}")

        # 将区划码转为字符串
        step2_unmatched['countyid'] = step2_unmatched['countyid'].astype(str)
        mapping_df['代码'] = mapping_df['代码'].astype(str)

        # 创建一个简单的映射字典
        # 只考虑状态为"弃用"的记录
        old_to_new = {}
        for _, row in mapping_df.iterrows():
            if row['状态'] == '弃用' and pd.notna(row.get('新代码', None)) and row.get('新代码', '') != '':
                old_code = row['代码']
                new_codes_str = row['新代码']

                # 处理新代码字符串，去除年份标记
                new_codes = []
                for code in new_codes_str.split(';'):
                    clean_code = re.sub(r'\[\d+\]', '', code).strip()
                    if clean_code:
                        new_codes.append(clean_code)

                if new_codes:
                    old_to_new[old_code] = new_codes

        print(f"映射字典中的条目数: {len(old_to_new)}")

        # 创建GIS代码到GIS数据的映射
        gis_code_to_data = {}
        for _, row in gis_df.iterrows():
            code = str(row['区划码'])
            gis_code_to_data[code] = {
                'gis_name': row['地名'],
                'gis_county': row['县级'],
                'gis_city': row['地级'],
                'gis_province': row['省级'],
                'gis_county_type': row.get('县级类', None),
                'gis_city_type': row.get('地级类', None),
                'gis_province_type': row.get('省级类', None),
                'gis_code': code
            }

        # 使用映射进行匹配
        third_match_count = 0
        matched_indices = []

        # 遍历未匹配数据
        for idx, row in step2_unmatched.iterrows():
            county_id = row['countyid']

            # 检查当前代码是否在映射字典中
            if county_id in old_to_new:
                # 获取新代码列表
                new_codes = old_to_new[county_id]

                # 检查新代码是否在GIS数据中
                for new_code in new_codes:
                    if new_code in gis_code_to_data:
                        # 找到匹配，更新GIS信息
                        third_match_count += 1
                        matched_indices.append(idx)

                        # 获取GIS数据
                        gis_data = gis_code_to_data[new_code]

                        # 更新GIS信息
                        for col, value in gis_data.items():
                            step2_unmatched.loc[idx, col] = value

                        # 找到匹配后跳出循环
                        break

        print(f"映射表匹配成功的记录数: {third_match_count}/{len(step2_unmatched)} ({third_match_count/len(step2_unmatched):.2%})")

        # 分离匹配和未匹配的记录
        step3_matched = step2_unmatched.loc[matched_indices].copy()
        step3_unmatched = step2_unmatched.drop(matched_indices).copy()

        # 添加匹配来源标记
        step3_matched['match_source'] = '行政区划代码映射匹配'

        # 保存步骤3的结果
        print(f"\n保存步骤3匹配结果到 {step3_matched_path}...")
        step3_matched.to_csv(step3_matched_path, index=False)

        print(f"保存步骤3未匹配记录到 {step3_unmatched_path}...")
        step3_unmatched.to_csv(step3_unmatched_path, index=False)

        # 步骤4：自治州合并匹配
        print("\n开始步骤4：自治州合并匹配...")

        # 确定county_name列的名称
        county_name_col = None
        for col in ['county_name', 'county_name_x', 'county_name_y', 'countyname']:
            if col in step3_unmatched.columns:
                county_name_col = col
                break

        if county_name_col is None:
            print("警告: 未找到county_name列，尝试查找包含'county'和'name'的列")
            for col in step3_unmatched.columns:
                if 'county' in col.lower() and 'name' in col.lower():
                    county_name_col = col
                    break

        if county_name_col is None:
            print("错误: 未找到county_name列，无法进行自治州匹配")
            print("可用的列: ", step3_unmatched.columns.tolist())
            # 跳过步骤4
            step4_matched = pd.DataFrame(columns=step3_unmatched.columns)
            step4_unmatched = step3_unmatched.copy()
        else:
            print(f"使用'{county_name_col}'列作为自治州匹配")

            # 筛选出自治州记录
            autonomous_mask = step3_unmatched[county_name_col].str.contains('自治州', na=False)
            autonomous_df = step3_unmatched[autonomous_mask].copy()
            non_autonomous_df = step3_unmatched[~autonomous_mask].copy()
            print(f"自治州记录数: {len(autonomous_df)}")
            print(f"非自治州记录数: {len(non_autonomous_df)}")

            if len(autonomous_df) == 0:
                print("未找到任何自治州记录，跳过步骤4")
                step4_matched = pd.DataFrame(columns=step3_unmatched.columns)
                step4_unmatched = step3_unmatched.copy()
            else:
                # 创建自治州名称到GIS县级单位的映射
                autonomous_to_counties = {}
                for prefecture in gis_df['地级'].unique():
                    if pd.notna(prefecture) and '自治州' in str(prefecture):
                        counties = gis_df[gis_df['地级'] == prefecture]
                        autonomous_to_counties[prefecture] = counties

                print(f"GIS数据中找到{len(autonomous_to_counties)}个自治州")

                if len(autonomous_to_counties) == 0:
                    print("GIS数据中未找到任何自治州，跳过步骤4")
                    step4_matched = pd.DataFrame(columns=step3_unmatched.columns)
                    step4_unmatched = step3_unmatched.copy()
                else:
                    # 为每个自治州记录添加GIS信息
                    # 创建一个新的DataFrame来存储匹配结果
                    matched_rows = []

                    # 遍历每个自治州
                    for prefecture, counties in autonomous_to_counties.items():
                        # 查找对应的未匹配记录
                        prefecture_df = autonomous_df[autonomous_df[county_name_col] == prefecture]

                        if len(prefecture_df) == 0:
                            # 尝试清理名称后匹配
                            clean_prefecture = clean_name(prefecture)
                            for name in autonomous_df[county_name_col].unique():
                                if pd.notna(name) and clean_name(name) == clean_prefecture:
                                    prefecture_df = autonomous_df[autonomous_df[county_name_col] == name]
                                    break

                        if len(prefecture_df) > 0:
                            print(f"处理自治州: {prefecture}, 找到{len(prefecture_df)}条记录, {len(counties)}个县级单位")

                            # 为每条自治州记录创建多条匹配记录，每个县级单位一条
                            for _, row in prefecture_df.iterrows():
                                for _, county in counties.iterrows():
                                    # 创建一个新行，复制原始行的所有数据
                                    new_row = row.copy()

                                    # 添加GIS信息
                                    new_row['gis_name'] = county['地名']
                                    new_row['gis_county'] = county['县级']
                                    new_row['gis_city'] = county['地级']
                                    new_row['gis_province'] = county['省级']
                                    if '县级类' in county:
                                        new_row['gis_county_type'] = county['县级类']
                                    if '地级类' in county:
                                        new_row['gis_city_type'] = county['地级类']
                                    if '省级类' in county:
                                        new_row['gis_province_type'] = county['省级类']
                                    new_row['gis_code'] = county['区划码']

                                    # 添加匹配来源标记
                                    new_row['match_source'] = '自治州合并匹配'

                                    # 添加到结果列表
                                    matched_rows.append(new_row)
                        else:
                            print(f"警告: 未找到自治州 {prefecture} 的对应记录")

                    # 创建匹配结果DataFrame
                    if matched_rows:
                        step4_matched = pd.DataFrame(matched_rows)
                        print(f"\n成功匹配{len(step4_matched)}条记录，涉及{len(step4_matched[county_name_col].unique())}个自治州")
                    else:
                        step4_matched = pd.DataFrame(columns=autonomous_df.columns)
                        print("\n未找到任何匹配记录")

                    step4_unmatched = non_autonomous_df.copy()

        # 保存步骤4的结果
        print(f"\n保存步骤4匹配结果到 {step4_matched_path}...")
        step4_matched.to_csv(step4_matched_path, index=False)

        print(f"保存步骤4未匹配记录到 {step4_unmatched_path}...")
        step4_unmatched.to_csv(step4_unmatched_path, index=False)

        # 合并所有匹配成功的记录
        print("\n合并所有匹配成功的记录...")
        all_matched = pd.concat([step1_matched, step2_matched, step3_matched, step4_matched], ignore_index=True)
        print(f"合并后的记录数: {len(all_matched)}")

        # 保存所有匹配结果（CSV格式）
        print(f"\n保存所有匹配结果到 {all_matched_path}...")
        all_matched.to_csv(all_matched_path, index=False)

        # 添加几何信息并保存为GeoJSON
        print("\n添加几何信息并保存为GeoJSON...")

        # 为每条记录添加几何信息
        geometries = []
        for _, row in all_matched.iterrows():
            if pd.notna(row['gis_code']):
                # 尝试从几何信息字典中获取
                geo = geometry_dict.get(row['gis_code'])
                geometries.append(geo)
            else:
                geometries.append(None)

        # 处理自治州的几何信息
        autonomous_records = all_matched[all_matched['match_source'] == '自治州合并匹配']
        if len(autonomous_records) > 0:
            print(f"处理{len(autonomous_records)}条自治州记录的几何信息...")

            # 创建自治州名称到GIS县级单位的映射
            autonomous_to_counties = {}
            for prefecture in gis_gdf['地级'].unique():
                if pd.notna(prefecture) and '自治州' in str(prefecture):
                    counties = gis_gdf[gis_gdf['地级'] == prefecture]
                    autonomous_to_counties[prefecture] = counties

            # 合并每个自治州的几何信息
            autonomous_geometries = {}
            for prefecture, counties in autonomous_to_counties.items():
                if len(counties) > 0:
                    merged_geometry = unary_union(counties['geometry'].tolist())
                    autonomous_geometries[prefecture] = merged_geometry

            # 为自治州记录更新几何信息
            for idx, row in autonomous_records.iterrows():
                # 确定county_name列
                county_name_col = None
                for col in ['county_name', 'county_name_x', 'county_name_y', 'countyname']:
                    if col in row and pd.notna(row[col]):
                        county_name_col = col
                        break

                if county_name_col:
                    prefecture_name = row[county_name_col]
                    # 尝试直接匹配
                    if prefecture_name in autonomous_geometries:
                        # 找到匹配的索引
                        match_idx = all_matched.index.get_loc(idx)
                        geometries[match_idx] = autonomous_geometries[prefecture_name]
                    else:
                        # 尝试清理名称后匹配
                        clean_prefecture = clean_name(prefecture_name)
                        for name, geometry in autonomous_geometries.items():
                            if clean_name(name) == clean_prefecture:
                                # 找到匹配的索引
                                match_idx = all_matched.index.get_loc(idx)
                                geometries[match_idx] = geometry
                                break

        # 创建GeoDataFrame
        all_matched_gdf = gpd.GeoDataFrame(
            all_matched,
            geometry=geometries,
            crs=gis_gdf.crs
        )

        # 保存为GeoJSON
        print(f"保存包含几何信息的匹配结果到 {all_matched_geojson_path}...")
        all_matched_gdf.to_file(all_matched_geojson_path, driver='GeoJSON')

        # 保存自治州合并结果
        if 'autonomous_geometries' in locals() and len(autonomous_geometries) > 0:
            autonomous_data = []
            for name, geometry in autonomous_geometries.items():
                autonomous_data.append({
                    'name': name,
                    'geometry': geometry
                })

            autonomous_gdf = gpd.GeoDataFrame(
                autonomous_data,
                geometry='geometry',
                crs=gis_gdf.crs
            )

            print(f"保存自治州合并结果到 {autonomous_merged_geojson_path}...")
            autonomous_gdf.to_file(autonomous_merged_geojson_path, driver='GeoJSON')

        # 计算匹配率
        total_records = len(io_tfp_df)
        match_rate = len(all_matched) / total_records * 100
        print(f"\n总记录数: {total_records}")
        print(f"匹配成功记录数: {len(all_matched)}")
        print(f"匹配率: {match_rate:.2f}%")

        # 统计各种匹配来源的记录数
        print("\n各种匹配来源的记录数:")
        source_counts = all_matched['match_source'].value_counts()
        for source, count in source_counts.items():
            print(f"{source}: {count}条记录")

        print("\n处理完成!")

    except Exception as e:
        print(f"发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
