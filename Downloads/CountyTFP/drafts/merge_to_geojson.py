import pandas as pd
import geopandas as gpd
import pyreadstat
import json
import os

print("开始处理数据...")

# 检查是否安装了必要的库
try:
    import geopandas
except ImportError:
    print("正在安装geopandas...")
    import subprocess
    subprocess.check_call(["pip", "install", "geopandas"])
    import geopandas as gpd

# 读取Stata文件
print("读取bank_variables.dta...")
bank_df, meta = pyreadstat.read_dta("bank_variables.dta")
print(f"bank_variables.dta形状: {bank_df.shape}")

# 读取修复后的GeoJSON文件
print("读取all_matched_fixed.geojson...")
try:
    # 尝试使用geopandas读取
    geo_df = gpd.read_file("matching_results/all_matched_fixed.geojson")
    print(f"使用geopandas成功读取GeoJSON，形状: {geo_df.shape}")
except Exception as e:
    print(f"使用geopandas读取失败: {e}")
    print("尝试使用json手动读取...")
    
    # 如果geopandas读取失败，尝试手动读取
    with open("matching_results/all_matched_fixed.geojson", "r", encoding="utf-8") as f:
        geo_data = json.load(f)
    
    # 提取特征属性
    features = geo_data["features"]
    properties_list = [feature["properties"] for feature in features]
    geometries = [feature["geometry"] for feature in features]
    
    # 创建DataFrame
    geo_df = pd.DataFrame(properties_list)
    print(f"手动读取GeoJSON成功，形状: {geo_df.shape}")
    
    # 添加geometry列
    geo_df["geometry"] = geometries

# 数据预处理
print("\n数据预处理...")

# 确保countyid和year列是正确的数据类型
bank_df['countyid'] = bank_df['countyid'].astype(str)
bank_df['year'] = bank_df['year'].astype(int)

# 检查geo_df中的列名和数据类型
print(f"GeoJSON文件的列名: {geo_df.columns.tolist()[:10]}...")
print(f"countyid列的类型: {geo_df['countyid'].dtype if 'countyid' in geo_df.columns else '不存在'}")
print(f"year列的类型: {geo_df['year'].dtype if 'year' in geo_df.columns else '不存在'}")

# 确保geo_df中的countyid和year列是正确的数据类型
if 'countyid' in geo_df.columns:
    geo_df['countyid'] = geo_df['countyid'].astype(str)
if 'year' in geo_df.columns:
    geo_df['year'] = geo_df['year'].astype(int)

# 选择要合并的列
required_cols = ['year_PSBC', 'bank_type', 'InstituionCode', 'number_all_PSBC', 'number_PSBC', 'countyid', 'year']
merge_cols = [col for col in required_cols if col in bank_df.columns]
bank_subset = bank_df[merge_cols].copy()

# 确保bank_subset中的year列是整数类型
bank_subset['year'] = bank_subset['year'].astype(int)

# 进行left merge
print("\n进行left merge...")
if isinstance(geo_df, gpd.GeoDataFrame):
    # 如果是GeoDataFrame，使用pandas的merge然后转回GeoDataFrame
    merged_df = pd.merge(
        geo_df,
        bank_subset,
        left_on=['countyid', 'year'],
        right_on=['countyid', 'year'],
        how='left'
    )
    # 确保geometry列保留
    merged_geo_df = gpd.GeoDataFrame(merged_df, geometry='geometry')
else:
    # 如果不是GeoDataFrame，直接使用pandas的merge
    merged_geo_df = pd.merge(
        geo_df,
        bank_subset,
        left_on=['countyid', 'year'],
        right_on=['countyid', 'year'],
        how='left'
    )

print(f"合并后的数据形状: {merged_geo_df.shape}")

# 检查合并后的空值情况
bank_cols = [col for col in merge_cols if col not in ['countyid', 'year']]
null_counts = merged_geo_df[bank_cols].isnull().sum()
print("\n合并后的空值数量:")
for col, count in null_counts.items():
    print(f"  {col}: {count} ({count/len(merged_geo_df)*100:.2f}%)")

# 保存合并后的GeoJSON文件
output_file = "matching_results/all_matched_fixed_with_bank.geojson"
print(f"\n保存合并后的GeoJSON文件到 {output_file}...")

if isinstance(merged_geo_df, gpd.GeoDataFrame):
    # 如果是GeoDataFrame，直接使用to_file保存
    merged_geo_df.to_file(output_file, driver='GeoJSON')
else:
    # 如果不是GeoDataFrame，需要手动构建GeoJSON
    features = []
    for _, row in merged_geo_df.iterrows():
        properties = {col: row[col] for col in merged_geo_df.columns if col != 'geometry'}
        # 处理NaN值
        for key, value in properties.items():
            if pd.isna(value):
                properties[key] = None
        
        feature = {
            "type": "Feature",
            "properties": properties,
            "geometry": row['geometry']
        }
        features.append(feature)
    
    geo_data = {
        "type": "FeatureCollection",
        "features": features
    }
    
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(geo_data, f)

print("处理完成!")

# 另外，也保存一个CSV版本，方便查看和处理
csv_output_file = "matching_results/all_matched_fixed_with_bank.csv"
print(f"保存合并后的CSV文件到 {csv_output_file}...")

# 移除geometry列以便保存为CSV
if 'geometry' in merged_geo_df.columns:
    csv_df = merged_geo_df.drop(columns=['geometry'])
else:
    csv_df = merged_geo_df.copy()

csv_df.to_csv(csv_output_file, index=False)

print("所有处理完成!")

