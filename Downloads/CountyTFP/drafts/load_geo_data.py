import geopandas as gpd
import pandas as pd
import os
import time

def load_geo_data(force_reload=False):
    """
    加载地理数据，支持缓存机制
    
    参数:
        force_reload: 是否强制重新加载原始GeoJSON文件
    
    返回:
        加载的GeoDataFrame
    """
    # 原始GeoJSON文件路径 - 使用修复后且包含银行数据的GeoJSON文件
    geo_file = "matching_results/all_matched_fixed_with_bank.geojson"
    
    # 缓存文件路径
    cache_dir = "data_cache"
    if not os.path.exists(cache_dir):
        os.makedirs(cache_dir)
    
    # 使用Parquet格式作为缓存（支持地理数据）- 使用新的缓存文件名以区分
    cache_file = os.path.join(cache_dir, "geo_data_fixed_with_bank_cache.parquet")
    
    # 检查缓存是否存在且是否需要强制重新加载
    if os.path.exists(cache_file) and not force_reload:
        print(f"从缓存加载数据: {cache_file}")
        start_time = time.time()
        gdf = gpd.read_parquet(cache_file)
        print(f"加载完成，耗时: {time.time() - start_time:.2f}秒")
        return gdf
    
    # 如果缓存不存在或需要强制重新加载，则从原始GeoJSON加载
    print(f"从原始GeoJSON加载数据: {geo_file}")
    start_time = time.time()
    gdf = gpd.read_file(geo_file)
    print(f"加载完成，耗时: {time.time() - start_time:.2f}秒")
    
    # 数据预处理
    # 确保countyid是字符串类型
    if 'countyid' in gdf.columns:
        gdf['countyid'] = gdf['countyid'].astype(str)
    
    # 确保year是整数类型
    if 'year' in gdf.columns:
        gdf['year'] = pd.to_numeric(gdf['year'], errors='coerce').fillna(0).astype(int)
    
    # 确保number_all_PSBC是数值类型
    if 'number_all_PSBC' in gdf.columns:
        gdf['number_all_PSBC'] = pd.to_numeric(gdf['number_all_PSBC'], errors='coerce').fillna(0)
    
    # 保存到缓存
    print(f"保存数据到缓存: {cache_file}")
    gdf.to_parquet(cache_file)
    
    return gdf

# 示例用法
if __name__ == "__main__":
    # 首次运行会从GeoJSON加载并创建缓存
    gdf = load_geo_data()
    print(f"数据形状: {gdf.shape}")
    
    # 数据基本信息
    print("\n数据基本信息:")
    print(f"列名: {gdf.columns.tolist()}")
    print(f"数据类型:\n{gdf.dtypes}")
    
    # 检查关键列
    key_columns = ['countyid', 'year', 'number_all_PSBC', 'gis_province', 'gis_county_type']
    for col in key_columns:
        if col in gdf.columns:
            print(f"\n{col}列信息:")
            if col in ['countyid', 'gis_province', 'gis_county_type']:
                print(f"唯一值数量: {gdf[col].nunique()}")
                print(f"前10个唯一值: {sorted(gdf[col].unique())[:10]}")
            elif col == 'year':
                print(f"年份范围: {gdf[col].min()} 到 {gdf[col].max()}")
                year_counts = gdf[col].value_counts().sort_index()
                print(f"年份分布: {dict(year_counts)}")
            elif col == 'number_all_PSBC':
                print(f"最小值: {gdf[col].min()}")
                print(f"最大值: {gdf[col].max()}")
                print(f"平均值: {gdf[col].mean():.2f}")
                print(f"中位数: {gdf[col].median()}")
                print(f"为0的记录数: {(gdf[col] == 0).sum()} ({(gdf[col] == 0).sum()/len(gdf)*100:.2f}%)")
                print(f"大于0的记录数: {(gdf[col] > 0).sum()} ({(gdf[col] > 0).sum()/len(gdf)*100:.2f}%)")
        else:
            print(f"\n警告: 数据中缺少{col}列")
    
    # 后续运行会从缓存加载（除非指定force_reload=True）
    gdf = load_geo_data(force_reload=False)
    print(f"\n再次加载数据形状: {gdf.shape}")



