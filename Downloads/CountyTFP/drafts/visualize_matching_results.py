"""
匹配结果可视化脚本

本脚本用于可视化GIS数据匹配的结果，展示哪些区域成功匹配，哪些未匹配。
通过直接读取匹配过程中生成的文件，绘制地图显示匹配情况。

依赖文件:
- matching_results/all_matched.csv: 所有匹配成功的记录
- matching_results/all_matched.geojson: 包含几何信息的匹配结果
- 2015county/2015county.shp: 原始GIS数据
"""

import pandas as pd
import geopandas as gpd
import matplotlib.pyplot as plt
import os
import numpy as np
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.patches as mpatches

def main():
    print("开始可视化匹配结果...")
    
    # 文件路径
    all_matched_csv = 'matching_results/all_matched.csv'
    all_matched_geojson = 'matching_results/all_matched.geojson'
    original_shapefile = '2015county/2015county.shp'
    output_dir = 'matching_visualization'
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 检查文件是否存在
    for path in [all_matched_csv, original_shapefile]:
        if not os.path.exists(path):
            print(f"错误: 找不到文件 {path}")
            return
    
    # 读取原始GIS数据
    print("\n读取原始GIS数据...")
    original_gdf = gpd.read_file(original_shapefile)
    print(f"原始GIS数据行数: {len(original_gdf)}")
    
    # 读取匹配结果CSV
    print("\n读取匹配结果CSV...")
    matched_df = pd.read_csv(all_matched_csv)
    print(f"匹配结果行数: {len(matched_df)}")
    
    # 读取匹配结果GeoJSON（如果存在）
    matched_gdf = None
    if os.path.exists(all_matched_geojson):
        print("\n读取匹配结果GeoJSON...")
        matched_gdf = gpd.read_file(all_matched_geojson)
        print(f"匹配结果GeoJSON行数: {len(matched_gdf)}")
        
        # 检查几何信息
        valid_geometry_count = matched_gdf.geometry.notna().sum()
        print(f"有效几何信息记录数: {valid_geometry_count} ({valid_geometry_count/len(matched_gdf):.2%})")
    
    # 分析匹配结果
    print("\n分析匹配结果...")
    
    # 统计各种匹配来源的记录数
    if 'match_source' in matched_df.columns:
        source_counts = matched_df['match_source'].value_counts()
        print("\n各种匹配来源的记录数:")
        for source, count in source_counts.items():
            print(f"{source}: {count}条记录 ({count/len(matched_df):.2%})")
    
    # 统计每年的匹配记录数
    if 'year' in matched_df.columns:
        year_counts = matched_df['year'].value_counts().sort_index()
        print("\n每年的匹配记录数:")
        for year, count in year_counts.items():
            print(f"{year}年: {count}条记录")
    
    # 统计每个省的匹配记录数
    if 'gis_province' in matched_df.columns:
        province_counts = matched_df['gis_province'].value_counts()
        print("\n各省份的匹配记录数 (前10个):")
        for province, count in province_counts.head(10).items():
            print(f"{province}: {count}条记录")
    
    # 创建匹配状态地图
    print("\n创建匹配状态地图...")
    
    # 准备原始GIS数据
    original_gdf['区划码'] = original_gdf['区划码'].astype(str)
    
    # 获取匹配上的区划码
    matched_codes = set()
    if 'gis_code' in matched_df.columns:
        matched_codes = set(matched_df['gis_code'].astype(str).unique())
    
    # 标记匹配状态
    original_gdf['匹配状态'] = original_gdf['区划码'].apply(
        lambda x: '已匹配' if str(x) in matched_codes else '未匹配'
    )
    
    # 计算匹配率
    matched_count = sum(original_gdf['匹配状态'] == '已匹配')
    total_count = len(original_gdf)
    match_rate = matched_count / total_count * 100
    print(f"\n原始GIS数据中的匹配率: {matched_count}/{total_count} ({match_rate:.2f}%)")
    
    # 创建地图
    fig, ax = plt.subplots(1, 1, figsize=(15, 10))
    
    # 设置颜色
    colors = {'已匹配': '#2681B6', '未匹配': '#F9F8CA'}
    
    # 绘制地图
    original_gdf.plot(
        column='匹配状态',
        ax=ax,
        categorical=True,
        legend=False,
        color=[colors[status] for status in original_gdf['匹配状态']],
        edgecolor='black',
        linewidth=0.1
    )
    
    # 添加图例
    patches = [
        mpatches.Patch(color=colors['已匹配'], label='已匹配'),
        mpatches.Patch(color=colors['未匹配'], label='未匹配')
    ]
    ax.legend(handles=patches, loc='lower right', fontsize=12)
    
    # 设置标题
    ax.set_title(
        f'GIS数据匹配状态\n'
        f'总区域数: {total_count} | 已匹配: {matched_count} | 匹配率: {match_rate:.2f}%',
        fontsize=16
    )
    
    # 关闭坐标轴
    ax.set_axis_off()
    
    # 保存地图
    output_file = os.path.join(output_dir, 'matching_status_map.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"保存匹配状态地图到: {output_file}")
    
    # 如果有匹配结果GeoJSON，创建按匹配来源的地图
    if matched_gdf is not None and 'match_source' in matched_gdf.columns:
        print("\n创建按匹配来源的地图...")
        
        # 获取唯一的匹配来源
        match_sources = matched_gdf['match_source'].unique()
        
        # 为每个匹配来源创建一个地图
        for source in match_sources:
            print(f"创建{source}的地图...")
            
            # 筛选数据
            source_gdf = matched_gdf[matched_gdf['match_source'] == source]
            
            # 创建地图
            fig, ax = plt.subplots(1, 1, figsize=(15, 10))
            
            # 绘制所有区域（浅色）
            original_gdf.plot(
                ax=ax,
                color='#F9F8CA',
                edgecolor='black',
                linewidth=0.1
            )
            
            # 绘制匹配的区域（深色）
            if len(source_gdf) > 0 and source_gdf.geometry.notna().any():
                source_gdf.plot(
                    ax=ax,
                    color='#2681B6',
                    edgecolor='black',
                    linewidth=0.1
                )
            
            # 设置标题
            ax.set_title(
                f'匹配来源: {source}\n'
                f'匹配记录数: {len(source_gdf)} | 占总匹配记录的 {len(source_gdf)/len(matched_gdf):.2%}',
                fontsize=16
            )
            
            # 关闭坐标轴
            ax.set_axis_off()
            
            # 保存地图
            output_file = os.path.join(output_dir, f'matching_source_{source}.png')
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            plt.close()
            print(f"保存{source}的地图到: {output_file}")
    
    # 创建按年份的匹配地图
    if matched_gdf is not None and 'year' in matched_gdf.columns:
        print("\n创建按年份的匹配地图...")
        
        # 获取唯一的年份
        years = sorted(matched_gdf['year'].unique())
        
        # 为每个年份创建一个地图
        for year in years:
            print(f"创建{year}年的地图...")
            
            # 筛选数据
            year_gdf = matched_gdf[matched_gdf['year'] == year]
            
            # 创建地图
            fig, ax = plt.subplots(1, 1, figsize=(15, 10))
            
            # 绘制所有区域（浅色）
            original_gdf.plot(
                ax=ax,
                color='#F9F8CA',
                edgecolor='black',
                linewidth=0.1
            )
            
            # 绘制匹配的区域（深色）
            if len(year_gdf) > 0 and year_gdf.geometry.notna().any():
                year_gdf.plot(
                    ax=ax,
                    color='#2681B6',
                    edgecolor='black',
                    linewidth=0.1
                )
            
            # 设置标题
            ax.set_title(
                f'{year}年匹配结果\n'
                f'匹配记录数: {len(year_gdf)} | 占总匹配记录的 {len(year_gdf)/len(matched_gdf):.2%}',
                fontsize=16
            )
            
            # 关闭坐标轴
            ax.set_axis_off()
            
            # 保存地图
            output_file = os.path.join(output_dir, f'matching_year_{year}.png')
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            plt.close()
            print(f"保存{year}年的地图到: {output_file}")
    
    # 检查几何信息问题
    if matched_gdf is not None:
        print("\n检查几何信息问题...")
        
        # 统计无效几何信息的记录
        invalid_geometry = matched_gdf[matched_gdf.geometry.isna()]
        
        if len(invalid_geometry) > 0:
            print(f"发现{len(invalid_geometry)}条记录没有有效的几何信息")
            
            # 按匹配来源统计
            if 'match_source' in invalid_geometry.columns:
                invalid_by_source = invalid_geometry['match_source'].value_counts()
                print("\n无效几何信息按匹配来源统计:")
                for source, count in invalid_by_source.items():
                    print(f"{source}: {count}条记录")
            
            # 按年份统计
            if 'year' in invalid_geometry.columns:
                invalid_by_year = invalid_geometry['year'].value_counts().sort_index()
                print("\n无效几何信息按年份统计:")
                for year, count in invalid_by_year.items():
                    print(f"{year}年: {count}条记录")
            
            # 保存无效几何信息的记录
            invalid_output = os.path.join(output_dir, 'invalid_geometry_records.csv')
            invalid_geometry.drop(columns=['geometry']).to_csv(invalid_output, index=False)
            print(f"保存无效几何信息记录到: {invalid_output}")
    
    print("\n可视化完成!")

if __name__ == "__main__":
    main()