"""
投入产出指标时间序列范围面积图生成脚本

本脚本用于生成七个关键投入产出指标随时间变化的范围面积图，
展示每个指标的平均值和分布范围随时间的变化趋势。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from matplotlib.ticker import MaxNLocator
from load_geo_data import load_geo_data
import matplotlib as mpl

# 设置matplotlib参数，使图表接近顶级期刊风格
plt.rcParams.update({
    'font.family': 'serif',
    'font.serif': ['Times New Roman'],
    'font.size': 10,
    'axes.titlesize': 12,
    'axes.labelsize': 10,
    'xtick.labelsize': 9,
    'ytick.labelsize': 9,
    'legend.fontsize': 9,
    'figure.figsize': (8, 5),  # 黄金比例
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'savefig.bbox': 'tight',
    'savefig.pad_inches': 0.05,
})

print("开始创建投入产出指标时间序列范围面积图...")

# 创建输出目录
output_dir = "io_indicators_range_charts"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    print(f"创建输出目录: {output_dir}")

# 使用缓存机制加载数据
print("加载缓存数据...")
gdf = load_geo_data(force_reload=False)
print(f"数据形状: {gdf.shape}")

# 确保countyid是字符串类型
gdf['countyid'] = gdf['countyid'].astype(str)

# 筛选样本：年份在1997到2015之间，排除市辖区和地级市
filtered_data = gdf[
    (gdf['year'] >= 1997) & 
    (gdf['year'] <= 2015) & 
    (gdf['gis_county_type'] != "市辖区") &
    (~gdf['countyid'].str.endswith('00'))  # 排除地级市
].copy()

print(f"筛选后的样本数量: {len(filtered_data)}")
print(f"筛选后的唯一countyid数量: {filtered_data['countyid'].nunique()}")
print(f"筛选后的年份范围: {filtered_data['year'].min()} 到 {filtered_data['year'].max()}")

# 计算投入产出指标
print("\n计算投入产出指标...")

# 1. TFP (全要素生产率) - 直接使用数据中的tfp列
print("1. 使用数据中的TFP值")
# TFP已经在数据中，不需要额外计算

# 2. Y/L (劳动生产率) - agr_outputq / qlabor
print("2. 计算劳动生产率 (Y/L)")
filtered_data['Y_L'] = filtered_data['agr_outputq'] / filtered_data['qlabor']

# 3. Y/K (资本产出比) - agr_outputq / qcapital
print("3. 计算资本产出比 (Y/K)")
filtered_data['Y_K'] = filtered_data['agr_outputq'] / filtered_data['qcapital']

# 4. Y/Land (土地产出比) - agr_outputq / qland
print("4. 计算土地产出比 (Y/Land)")
filtered_data['Y_Land'] = filtered_data['agr_outputq'] / filtered_data['qland']

# 5. K/L (资本劳动比) - qcapital / qlabor
print("5. 计算资本劳动比 (K/L)")
filtered_data['K_L'] = filtered_data['qcapital'] / filtered_data['qlabor']

# 6. Land/L (土地劳动比) - qland / qlabor
print("6. 计算土地劳动比 (Land/L)")
filtered_data['Land_L'] = filtered_data['qland'] / filtered_data['qlabor']

# 7. unitcost (单位成本) - 计算各种投入成本之和除以产出
print("7. 计算单位成本 (unitcost)")

# 计算劳动成本
if all(col in filtered_data.columns for col in ['plabor', 'qlabor']):
    filtered_data['labor_cost'] = filtered_data['plabor'] * filtered_data['qlabor']
    print("   计算劳动成本: plabor * qlabor")

# 计算资本成本
if all(col in filtered_data.columns for col in ['pcapital', 'qcapital']):
    filtered_data['capital_cost'] = filtered_data['pcapital'] * filtered_data['qcapital']
    print("   计算资本成本: pcapital * qcapital")

# 计算土地成本
if all(col in filtered_data.columns for col in ['pland', 'qland']):
    filtered_data['land_cost'] = filtered_data['pland'] * filtered_data['qland']
    print("   计算土地成本: pland * qland")

# 检查是否有中间投入材料相关列
material_cols = [col for col in filtered_data.columns if 'mater' in col.lower() or 'inter' in col.lower()]
print(f"   发现可能的中间投入列: {material_cols}")

# 计算中间投入成本
if all(col in filtered_data.columns for col in ['pmater', 'qmater']):
    filtered_data['material_cost'] = filtered_data['pmater'] * filtered_data['qmater']
    print("   计算中间投入成本: pmater * qmater")
elif 'intermediate_cost' in filtered_data.columns:
    print("   使用现有的intermediate_cost列")
elif 'material_cost' in filtered_data.columns:
    print("   使用现有的material_cost列")

# 计算总成本
cost_columns = [col for col in ['labor_cost', 'capital_cost', 'land_cost', 'material_cost', 'intermediate_cost'] 
                if col in filtered_data.columns]

if cost_columns:
    # 计算总成本 = 所有投入成本之和
    filtered_data['total_cost'] = filtered_data[cost_columns].sum(axis=1)
    
    # 计算单位成本 = 总成本 / 产出
    filtered_data['unitcost'] = filtered_data['total_cost'] / filtered_data['agr_outputq']
    
    print(f"   计算总成本: {' + '.join(cost_columns)}")
    print(f"   计算单位成本: total_cost / agr_outputq")
else:
    print("   警告: 无法计算单位成本，缺少成本数据列")
    # 创建一个空的unitcost列
    filtered_data['unitcost'] = np.nan

# 处理无穷大和NaN值
indicators = ['tfp', 'Y_L', 'Y_K', 'Y_Land', 'K_L', 'Land_L', 'unitcost']
for indicator in indicators:
    filtered_data[indicator] = filtered_data[indicator].replace([np.inf, -np.inf], np.nan)
    # 计算有效值的数量和百分比
    valid_count = filtered_data[indicator].notna().sum()
    valid_percent = valid_count / len(filtered_data) * 100
    print(f"{indicator}有效值数量: {valid_count} ({valid_percent:.2f}%)")

# 创建PSBC存在变量和贷款机构存在变量
print("\n创建PSBC存在变量和贷款机构存在变量...")
# 确保number_all_PSBC为数值类型并填充缺失值
filtered_data['number_all_PSBC'] = pd.to_numeric(filtered_data['number_all_PSBC'], errors='coerce').fillna(0)

# 创建PSBC存在变量（0-1变量）
filtered_data['has_psbc'] = (filtered_data['number_all_PSBC'] > 0).astype(int)

# 创建贷款机构存在变量（0-1变量）
filtered_data['has_loan_inst'] = 0

# 获取所有唯一的countyid
counties = filtered_data['countyid'].unique()

# 按县和年份排序数据
filtered_data = filtered_data.sort_values(['countyid', 'year'])

# 用于存储每个县第一次出现贷款机构的年份
county_first_loan_year = {}

# 检查是否有贷款机构相关列
if 'InstituionCode' in filtered_data.columns:
    # 对每个县的每一年，检查是否有贷款机构
    for county in counties:
        county_data = filtered_data[filtered_data['countyid'] == county]
        
        # 按年份排序
        county_data = county_data.sort_values('year')
        
        # 检查每一年是否有贷款机构
        for _, row in county_data.iterrows():
            year = row['year']
            # 检查InstituionCode是否不为空且不以'B0018A'开头（表示有贷款机构）
            has_loan = (not pd.isna(row['InstituionCode'])) and (not str(row['InstituionCode']).startswith('B0018A'))
            
            # 如果有贷款机构且之前没有记录过
            if has_loan and county not in county_first_loan_year:
                county_first_loan_year[county] = year
        
        # 更新贷款机构存在变量
        for _, row in county_data.iterrows():
            year = row['year']
            if county in county_first_loan_year and year >= county_first_loan_year[county]:
                filtered_data.loc[(filtered_data['countyid'] == county) & (filtered_data['year'] == year), 'has_loan_inst'] = 1
else:
    print("警告: 未找到InstituionCode列，无法确定贷款机构存在情况")
    # 如果没有相关列，使用PSBC存在作为替代
    filtered_data['has_loan_inst'] = filtered_data['has_psbc']

# 统计PSBC和贷款机构存在情况
psbc_count = filtered_data['has_psbc'].sum()
loan_inst_count = filtered_data['has_loan_inst'].sum()
print(f"有PSBC的样本数量: {psbc_count} ({psbc_count/len(filtered_data)*100:.2f}%)")
print(f"有贷款机构的样本数量: {loan_inst_count} ({loan_inst_count/len(filtered_data)*100:.2f}%)")

# 对2009年的投入产出指标进行平滑处理
print("\n对2009年的投入产出指标进行平滑处理...")

# 确保数据按countyid和year排序
filtered_data = filtered_data.sort_values(['countyid', 'year'])

# 需要平滑的年份
missing_years = [2009]

# 获取所有唯一的countyid
counties = filtered_data['countyid'].unique()

# 创建一个空的DataFrame来存储平滑后的数据
smoothed_data = []

# 对每个县进行平滑处理
for county in counties:
    # 获取该县的数据
    county_data = filtered_data[filtered_data['countyid'] == county].copy()
    
    # 如果该县数据少于3个点，跳过平滑（需要足够的点来进行插值）
    if len(county_data) < 3:
        smoothed_data.append(county_data)
        continue
    
    # 对每个指标进行检查和平滑
    for indicator in indicators:
        # 检查2009年的数据
        if 2009 in county_data['year'].values:
            year_2009 = county_data[county_data['year'] == 2009]
            if not pd.isna(year_2009[indicator].values[0]):
                # 获取2008年和2010年的数据
                year_2008 = county_data[county_data['year'] == 2008] if 2008 in county_data['year'].values else None
                year_2010 = county_data[county_data['year'] == 2010] if 2010 in county_data['year'].values else None
                
                # 如果2008年和2010年都有数据，检查2009年是否异常
                if (year_2008 is not None and not pd.isna(year_2008[indicator].values[0]) and
                    year_2010 is not None and not pd.isna(year_2010[indicator].values[0])):
                    
                    # 计算2009年与相邻年份的比率
                    ratio_2008 = year_2009[indicator].values[0] / year_2008[indicator].values[0] if year_2008[indicator].values[0] != 0 else float('inf')
                    ratio_2010 = year_2009[indicator].values[0] / year_2010[indicator].values[0] if year_2010[indicator].values[0] != 0 else float('inf')
                    
                    # 如果2009年的值异常（比率小于0.1或大于10），使用线性插值替换
                    if ratio_2008 < 0.1 or ratio_2008 > 10 or ratio_2010 < 0.1 or ratio_2010 > 10:
                        # 使用2008年和2010年的平均值替换2009年的值
                        interpolated_value = (year_2008[indicator].values[0] + year_2010[indicator].values[0]) / 2
                        county_data.loc[county_data['year'] == 2009, indicator] = interpolated_value
                        print(f"平滑异常值: 县={county}, 2009年{indicator}从{year_2009[indicator].values[0]}调整为{interpolated_value}")
    
    # 添加到结果中
    smoothed_data.append(county_data)

# 合并所有平滑后的数据
filtered_data = pd.concat(smoothed_data, ignore_index=True)

# 检查平滑结果
for year in missing_years:
    year_data = filtered_data[filtered_data['year'] == year]
    print(f"{year}年平滑后的样本数量: {len(year_data)}")
    for indicator in indicators:
        valid_count = year_data[indicator].notna().sum()
        print(f"  {indicator}有效值数量: {valid_count} ({valid_count/len(year_data)*100:.2f}%)")

# 指标名称映射和描述
indicator_info = {
    'tfp': {
        'name': 'Total Factor Productivity',
        'description': 'Measures overall production efficiency',
        'higher_better': True,
        'color': '#1f77b4'  # 蓝色
    },
    'Y_L': {
        'name': 'Labor Productivity',
        'description': 'Output per unit of labor',
        'higher_better': True,
        'color': '#ff7f0e'  # 橙色
    },
    'Y_K': {
        'name': 'Capital Productivity',
        'description': 'Output per unit of capital',
        'higher_better': True,
        'color': '#2ca02c'  # 绿色
    },
    'Y_Land': {
        'name': 'Land Productivity',
        'description': 'Output per unit of land',
        'higher_better': True,
        'color': '#d62728'  # 红色
    },
    'K_L': {
        'name': 'Capital-Labor Ratio',
        'description': 'Capital per unit of labor',
        'higher_better': None,  # 中性指标
        'color': '#9467bd'  # 紫色
    },
    'Land_L': {
        'name': 'Land-Labor Ratio',
        'description': 'Land per unit of labor',
        'higher_better': None,  # 中性指标
        'color': '#8c564b'  # 棕色
    },
    'unitcost': {
        'name': 'Unit Cost',
        'description': 'Cost per unit of output',
        'higher_better': False,  # 更低更好
        'color': '#e377c2'  # 粉色
    }
}

# 为每个指标创建范围面积图
for indicator, info in indicator_info.items():
    print(f"\n创建{indicator}指标的范围面积图...")
    
    # 检查数据有效性
    valid_data = filtered_data[filtered_data[indicator].notna()]
    if len(valid_data) == 0:
        print(f"警告: {indicator}指标没有有效数据，跳过")
        continue
    
    # 按年份分组计算统计量
    yearly_stats = valid_data.groupby('year')[indicator].agg([
        ('mean', 'mean'),
        ('p5', lambda x: x.quantile(0.05)),
        ('p95', lambda x: x.quantile(0.95))
    ]).reset_index()
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(8, 5))
    
    # 绘制范围面积
    ax.fill_between(
        yearly_stats['year'], 
        yearly_stats['p5'], 
        yearly_stats['p95'], 
        alpha=0.3, 
        color=info['color'],
        label='5th-95th Percentile Range'
    )
    
    # 绘制平均值线
    ax.plot(
        yearly_stats['year'], 
        yearly_stats['mean'], 
        color=info['color'],
        linewidth=2,
        label='Mean'
    )
    
    # 设置图表标题和标签
    ax.set_title(f'Temporal Trends in {info["name"]} (1997-2015)')
    ax.set_xlabel('Year')
    ax.set_ylabel(info['name'])
    
    # 设置x轴刻度为整数年份
    ax.xaxis.set_major_locator(MaxNLocator(integer=True))
    
    # 添加网格线
    ax.grid(True, linestyle='--', alpha=0.7)
    
    # 添加图例
    ax.legend(loc='best', frameon=True, framealpha=0.8)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    output_file = os.path.join(output_dir, f"{indicator}_range_chart.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"保存范围面积图到: {output_file}")
    
    plt.close(fig)

"""
投入产出指标关系散点图生成脚本

本脚本用于生成四种关键投入产出指标组合的散点图，
按照PSBC和贷款机构存在与否分组，并进行LOWESS回归拟合。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from matplotlib.ticker import MaxNLocator
from load_geo_data import load_geo_data
from statsmodels.nonparametric.smoothers_lowess import lowess
import matplotlib as mpl

# 设置matplotlib参数，使图表接近顶级期刊风格
plt.rcParams.update({
    'font.family': 'serif',
    'font.serif': ['Times New Roman'],
    'font.size': 10,
    'axes.titlesize': 12,
    'axes.labelsize': 10,
    'xtick.labelsize': 9,
    'ytick.labelsize': 9,
    'legend.fontsize': 9,
    'figure.figsize': (8, 5),  # 黄金比例
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'savefig.bbox': 'tight',
    'savefig.pad_inches': 0.05,
})

print("开始创建投入产出指标关系散点图...")

# 创建输出目录
output_dir = "io_indicators_scatter_plots"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    print(f"创建输出目录: {output_dir}")

# 使用缓存机制加载数据
print("加载缓存数据...")
gdf = load_geo_data(force_reload=False)
print(f"数据形状: {gdf.shape}")

# 确保countyid是字符串类型
gdf['countyid'] = gdf['countyid'].astype(str)

# 筛选样本：年份在1997到2015之间，排除市辖区和地级市
filtered_data = gdf[
    (gdf['year'] >= 1997) & 
    (gdf['year'] <= 2015) & 
    (gdf['gis_county_type'] != "市辖区") &
    (~gdf['countyid'].str.endswith('00'))  # 排除地级市
].copy()

print(f"筛选后的样本数量: {len(filtered_data)}")
print(f"筛选后的唯一countyid数量: {filtered_data['countyid'].nunique()}")
print(f"筛选后的年份范围: {filtered_data['year'].min()} 到 {filtered_data['year'].max()}")

# 计算投入产出指标
print("\n计算投入产出指标...")

# 1. TFP (全要素生产率) - 直接使用数据中的tfp列
print("1. 使用数据中的TFP值")
# TFP已经在数据中，不需要额外计算

# 2. Y/L (劳动生产率) - agr_outputq / qlabor
print("2. 计算劳动生产率 (Y/L)")
filtered_data['Y_L'] = filtered_data['agr_outputq'] / filtered_data['qlabor']

# 3. Y/K (资本产出比) - agr_outputq / qcapital
print("3. 计算资本产出比 (Y/K)")
filtered_data['Y_K'] = filtered_data['agr_outputq'] / filtered_data['qcapital']

# 4. Y/Land (土地产出比) - agr_outputq / qland
print("4. 计算土地产出比 (Y/Land)")
filtered_data['Y_Land'] = filtered_data['agr_outputq'] / filtered_data['qland']

# 5. K/L (资本劳动比) - qcapital / qlabor
print("5. 计算资本劳动比 (K/L)")
filtered_data['K_L'] = filtered_data['qcapital'] / filtered_data['qlabor']

# 6. Land/L (土地劳动比) - qland / qlabor
print("6. 计算土地劳动比 (Land/L)")
filtered_data['Land_L'] = filtered_data['qland'] / filtered_data['qlabor']

# 7. unitcost (单位成本) - 计算各种投入成本之和除以产出
print("7. 计算单位成本 (unitcost)")

# 计算劳动成本
if all(col in filtered_data.columns for col in ['plabor', 'qlabor']):
    filtered_data['labor_cost'] = filtered_data['plabor'] * filtered_data['qlabor']
    print("   计算劳动成本: plabor * qlabor")

# 计算资本成本
if all(col in filtered_data.columns for col in ['pcapital', 'qcapital']):
    filtered_data['capital_cost'] = filtered_data['pcapital'] * filtered_data['qcapital']
    print("   计算资本成本: pcapital * qcapital")

# 计算土地成本
if all(col in filtered_data.columns for col in ['pland', 'qland']):
    filtered_data['land_cost'] = filtered_data['pland'] * filtered_data['qland']
    print("   计算土地成本: pland * qland")

# 检查是否有中间投入材料相关列
material_cols = [col for col in filtered_data.columns if 'mater' in col.lower() or 'inter' in col.lower()]
print(f"   发现可能的中间投入列: {material_cols}")

# 计算中间投入成本
if all(col in filtered_data.columns for col in ['pmater', 'qmater']):
    filtered_data['material_cost'] = filtered_data['pmater'] * filtered_data['qmater']
    print("   计算中间投入成本: pmater * qmater")
elif 'intermediate_cost' in filtered_data.columns:
    print("   使用现有的intermediate_cost列")
elif 'material_cost' in filtered_data.columns:
    print("   使用现有的material_cost列")

# 计算总成本
cost_columns = [col for col in ['labor_cost', 'capital_cost', 'land_cost', 'material_cost', 'intermediate_cost'] 
                if col in filtered_data.columns]

if cost_columns:
    # 计算总成本 = 所有投入成本之和
    filtered_data['total_cost'] = filtered_data[cost_columns].sum(axis=1)
    
    # 计算单位成本 = 总成本 / 产出
    filtered_data['unitcost'] = filtered_data['total_cost'] / filtered_data['agr_outputq']
    
    print(f"   计算总成本: {' + '.join(cost_columns)}")
    print(f"   计算单位成本: total_cost / agr_outputq")
else:
    print("   警告: 无法计算单位成本，缺少成本数据列")
    # 创建一个空的unitcost列
    filtered_data['unitcost'] = np.nan

# 处理无穷大和NaN值
indicators = ['tfp', 'Y_L', 'Y_K', 'Y_Land', 'K_L', 'Land_L', 'unitcost']
for indicator in indicators:
    filtered_data[indicator] = filtered_data[indicator].replace([np.inf, -np.inf], np.nan)
    # 计算有效值的数量和百分比
    valid_count = filtered_data[indicator].notna().sum()
    valid_percent = valid_count / len(filtered_data) * 100
    print(f"{indicator}有效值数量: {valid_count} ({valid_percent:.2f}%)")

# 创建PSBC存在变量和贷款机构存在变量
print("\n创建PSBC存在变量和贷款机构存在变量...")
# 确保number_all_PSBC为数值类型并填充缺失值
filtered_data['number_all_PSBC'] = pd.to_numeric(filtered_data['number_all_PSBC'], errors='coerce').fillna(0)

# 创建PSBC存在变量（0-1变量）
filtered_data['has_psbc'] = (filtered_data['number_all_PSBC'] > 0).astype(int)

# 创建贷款机构存在变量（0-1变量）
filtered_data['has_loan_inst'] = 0

# 获取所有唯一的countyid
counties = filtered_data['countyid'].unique()

# 按县和年份排序数据
filtered_data = filtered_data.sort_values(['countyid', 'year'])

# 用于存储每个县第一次出现贷款机构的年份
county_first_loan_year = {}

# 检查是否有贷款机构相关列
if 'InstituionCode' in filtered_data.columns:
    # 对每个县的每一年，检查是否有贷款机构
    for county in counties:
        county_data = filtered_data[filtered_data['countyid'] == county]
        
        # 按年份排序
        county_data = county_data.sort_values('year')
        
        # 检查每一年是否有贷款机构
        for _, row in county_data.iterrows():
            year = row['year']
            # 检查InstituionCode是否不为空且不以'B0018A'开头（表示有贷款机构）
            has_loan = (not pd.isna(row['InstituionCode'])) and (not str(row['InstituionCode']).startswith('B0018A'))
            
            # 如果有贷款机构且之前没有记录过
            if has_loan and county not in county_first_loan_year:
                county_first_loan_year[county] = year
        
        # 更新贷款机构存在变量
        for _, row in county_data.iterrows():
            year = row['year']
            if county in county_first_loan_year and year >= county_first_loan_year[county]:
                filtered_data.loc[(filtered_data['countyid'] == county) & (filtered_data['year'] == year), 'has_loan_inst'] = 1
else:
    print("警告: 未找到InstituionCode列，无法确定贷款机构存在情况")
    # 如果没有相关列，使用PSBC存在作为替代
    filtered_data['has_loan_inst'] = filtered_data['has_psbc']

# 统计PSBC和贷款机构存在情况
psbc_count = filtered_data['has_psbc'].sum()
loan_inst_count = filtered_data['has_loan_inst'].sum()
print(f"有PSBC的样本数量: {psbc_count} ({psbc_count/len(filtered_data)*100:.2f}%)")
print(f"有贷款机构的样本数量: {loan_inst_count} ({loan_inst_count/len(filtered_data)*100:.2f}%)")

# 对2009年的投入产出指标进行平滑处理
print("\n对2009年的投入产出指标进行平滑处理...")

# 确保数据按countyid和year排序
filtered_data = filtered_data.sort_values(['countyid', 'year'])

# 需要平滑的年份
missing_years = [2009]

# 获取所有唯一的countyid
counties = filtered_data['countyid'].unique()

# 创建一个空的DataFrame来存储平滑后的数据
smoothed_data = []

# 对每个县进行平滑处理
for county in counties:
    # 获取该县的数据
    county_data = filtered_data[filtered_data['countyid'] == county].copy()
    
    # 如果该县数据少于3个点，跳过平滑（需要足够的点来进行插值）
    if len(county_data) < 3:
        smoothed_data.append(county_data)
        continue
    
    # 对每个指标进行检查和平滑
    for indicator in indicators:
        # 检查2009年的数据
        if 2009 in county_data['year'].values:
            year_2009 = county_data[county_data['year'] == 2009]
            if not pd.isna(year_2009[indicator].values[0]):
                # 获取2008年和2010年的数据
                year_2008 = county_data[county_data['year'] == 2008] if 2008 in county_data['year'].values else None
                year_2010 = county_data[county_data['year'] == 2010] if 2010 in county_data['year'].values else None
                
                # 如果2008年和2010年都有数据，检查2009年是否异常
                if (year_2008 is not None and not pd.isna(year_2008[indicator].values[0]) and
                    year_2010 is not None and not pd.isna(year_2010[indicator].values[0])):
                    
                    # 计算2009年与相邻年份的比率
                    ratio_2008 = year_2009[indicator].values[0] / year_2008[indicator].values[0] if year_2008[indicator].values[0] != 0 else float('inf')
                    ratio_2010 = year_2009[indicator].values[0] / year_2010[indicator].values[0] if year_2010[indicator].values[0] != 0 else float('inf')
                    
                    # 如果2009年的值异常（比率小于0.1或大于10），使用线性插值替换
                    if ratio_2008 < 0.1 or ratio_2008 > 10 or ratio_2010 < 0.1 or ratio_2010 > 10:
                        # 使用2008年和2010年的平均值替换2009年的值
                        interpolated_value = (year_2008[indicator].values[0] + year_2010[indicator].values[0]) / 2
                        county_data.loc[county_data['year'] == 2009, indicator] = interpolated_value
                        print(f"平滑异常值: 县={county}, 2009年{indicator}从{year_2009[indicator].values[0]}调整为{interpolated_value}")
    
    # 添加到结果中
    smoothed_data.append(county_data)

# 合并所有平滑后的数据
filtered_data = pd.concat(smoothed_data, ignore_index=True)

# 检查平滑结果
for year in missing_years:
    year_data = filtered_data[filtered_data['year'] == year]
    print(f"{year}年平滑后的样本数量: {len(year_data)}")
    for indicator in indicators:
        valid_count = year_data[indicator].notna().sum()
        print(f"  {indicator}有效值数量: {valid_count} ({valid_count/len(year_data)*100:.2f}%)")

# 定义去均值函数
def demean_by_county(df, columns):
    """按县去均值处理"""
    result = df.copy()
    for col in columns:
        # 计算每个县的均值
        county_means = df.groupby('countyid')[col].transform('mean')
        # 去均值
        result[col] = df[col] - county_means
    return result

# 定义要绘制的散点图组合
scatter_plots = [
    {
        'x': 'K_L',
        'y': 'Y_K',
        'title': 'Capital Productivity vs Capital-Labor Ratio',
        'xlabel': 'Capital-Labor Ratio (K/L)',
        'ylabel': 'Capital Productivity (Y/K)',
        'log_transform': True,
        'demean': True
    },
    {
        'x': 'K_L',
        'y': 'unitcost',
        'title': 'Unit Cost vs Capital-Labor Ratio',
        'xlabel': 'Capital-Labor Ratio (K/L)',
        'ylabel': 'Unit Cost',
        'log_transform': True,
        'demean': True
    },
    {
        'x': 'Land_L',
        'y': 'Y_L',
        'title': 'Labor Productivity vs Land-Labor Ratio',
        'xlabel': 'Land-Labor Ratio (Land/L)',
        'ylabel': 'Labor Productivity (Y/L)',
        'log_transform': True,
        'demean': True
    },
    {
        'x': 'Land_L',
        'y': 'K_L',
        'title': 'Capital-Labor Ratio vs Land-Labor Ratio',
        'xlabel': 'Land-Labor Ratio (Land/L)',
        'ylabel': 'Capital-Labor Ratio (K/L)',
        'log_transform': True,
        'demean': True
    }
]

# 绘制按PSBC存在分组的散点图
for plot_info in scatter_plots:
    plt.figure(figsize=(10, 8))
    
    # 获取数据
    x = plot_info['x']
    y = plot_info['y']
    
    # 按PSBC存在分组
    group0 = filtered_data[filtered_data['has_psbc'] == 0].copy()
    group1 = filtered_data[filtered_data['has_psbc'] == 1].copy()
    
    # 检查是否需要对数变换
    log_transform = plot_info.get('log_transform', False)
    
    if log_transform:
        # 确保数据为正值（对数变换要求）
        group0 = group0[(group0[x] > 0) & (group0[y] > 0)]
        group1 = group1[(group1[x] > 0) & (group1[y] > 0)]
        
        # 应用对数变换
        group0[x] = np.log(group0[x])
        group0[y] = np.log(group0[y])
        group1[x] = np.log(group1[x])
        group1[y] = np.log(group1[y])
    
    # 检查是否需要去均值
    demean = plot_info.get('demean', False)
    
    if demean:
        # 对两组数据分别进行去均值处理
        group0 = demean_by_county(group0, [x, y])
        group1 = demean_by_county(group1, [x, y])
    
    # 绘制散点图
    plt.scatter(group0[x], group0[y], color='#cccccc', s=25, alpha=0.6, marker='o', 
                edgecolor='#555555', linewidth=0.5, label='Without PSBC')
    plt.scatter(group1[x], group1[y], color='#555555', s=25, alpha=0.6, marker='^', 
                edgecolor='black', linewidth=0.5, label='With PSBC')
    
    # 使用LOWESS回归
    if len(group0) > 10:
        lowess_result0 = lowess(group0[y], group0[x], frac=0.5, it=3, return_sorted=True)
        x_lowess0, y_lowess0 = lowess_result0[:, 0], lowess_result0[:, 1]
        plt.plot(x_lowess0, y_lowess0, color='#1f77b4', linewidth=2.5, linestyle='-', 
                 label='LOWESS trend (No PSBC)')
    
    if len(group1) > 10:
        lowess_result1 = lowess(group1[y], group1[x], frac=0.5, it=3, return_sorted=True)
        x_lowess1, y_lowess1 = lowess_result1[:, 0], lowess_result1[:, 1]
        plt.plot(x_lowess1, y_lowess1, color='#d62728', linewidth=2.5, linestyle='--', 
                 label='LOWESS trend (Has PSBC)')
    
    # 添加标题和标签
    transform_text = " (Log, Demeaned)" if log_transform and demean else ""
    plt.title(f'{plot_info["title"]}{transform_text}', fontsize=16)
    plt.xlabel(f'{plot_info["xlabel"]}{transform_text}', fontsize=14)
    plt.ylabel(f'{plot_info["ylabel"]}{transform_text}', fontsize=14)
    
    # 添加图例
    plt.legend(loc='best', fontsize=12)
    
    # 添加网格线
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    suffix = "_log_demean" if log_transform and demean else ""
    output_file = os.path.join(output_dir, f"{y}_vs_{x}_by_psbc_lowess{suffix}.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"保存LOWESS回归图表: {output_file}")

# 绘制按贷款机构存在分组的散点图
for plot_info in scatter_plots:
    plt.figure(figsize=(10, 8))
    
    # 获取数据
    x = plot_info['x']
    y = plot_info['y']
    
    # 按贷款机构存在分组
    group0 = filtered_data[filtered_data['has_loan_inst'] == 0].copy()
    group1 = filtered_data[filtered_data['has_loan_inst'] == 1].copy()
    
    # 检查是否需要对数变换
    log_transform = plot_info.get('log_transform', False)
    
    if log_transform:
        # 确保数据为正值（对数变换要求）
        group0 = group0[(group0[x] > 0) & (group0[y] > 0)]
        group1 = group1[(group1[x] > 0) & (group1[y] > 0)]
        
        # 应用对数变换
        group0[x] = np.log(group0[x])
        group0[y] = np.log(group0[y])
        group1[x] = np.log(group1[x])
        group1[y] = np.log(group1[y])
    
    # 检查是否需要去均值
    demean = plot_info.get('demean', False)
    
    if demean:
        # 对两组数据分别进行去均值处理
        group0 = demean_by_county(group0, [x, y])
        group1 = demean_by_county(group1, [x, y])
    
    # 绘制散点图
    plt.scatter(group0[x], group0[y], color='#cccccc', s=25, alpha=0.6, marker='o', 
                edgecolor='#555555', linewidth=0.5, label='Without Loan Inst')
    plt.scatter(group1[x], group1[y], color='#555555', s=25, alpha=0.6, marker='^', 
                edgecolor='black', linewidth=0.5, label='With Loan Inst')
    
    # 使用LOWESS回归
    if len(group0) > 10:
        lowess_result0 = lowess(group0[y], group0[x], frac=0.5, it=3, return_sorted=True)
        x_lowess0, y_lowess0 = lowess_result0[:, 0], lowess_result0[:, 1]
        plt.plot(x_lowess0, y_lowess0, color='#1f77b4', linewidth=2.5, linestyle='-', 
                 label='LOWESS trend (No Loan Inst)')
    
    if len(group1) > 10:
        lowess_result1 = lowess(group1[y], group1[x], frac=0.5, it=3, return_sorted=True)
        x_lowess1, y_lowess1 = lowess_result1[:, 0], lowess_result1[:, 1]
        plt.plot(x_lowess1, y_lowess1, color='#d62728', linewidth=2.5, linestyle='--', 
                 label='LOWESS trend (Has Loan Inst)')
    
    # 添加标题和标签
    transform_text = " (Log, Demeaned)" if log_transform and demean else ""
    plt.title(f'{plot_info["title"]}{transform_text}', fontsize=16)
    plt.xlabel(f'{plot_info["xlabel"]}{transform_text}', fontsize=14)
    plt.ylabel(f'{plot_info["ylabel"]}{transform_text}', fontsize=14)
    
    # 添加图例
    plt.legend(loc='best', fontsize=12)
    
    # 添加网格线
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    suffix = "_log_demean" if log_transform and demean else ""
    output_file = os.path.join(output_dir, f"{y}_vs_{x}_by_loan_inst_lowess{suffix}.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"保存LOWESS回归图表: {output_file}")
