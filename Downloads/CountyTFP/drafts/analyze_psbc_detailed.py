import pandas as pd
import numpy as np
import os

# 读取Excel文件
file_path = "PSBC.xlsx"
print(f"正在读取文件: {file_path}")

# 读取Excel文件，不使用标题行
df = pd.read_excel(file_path, header=None)

# 跳过第一行（如果第一行是空的）
if df.iloc[0].isna().all():
    df = df.iloc[1:].reset_index(drop=True)

# 根据数据内容推测每列的含义
column_meanings = {
    0: "序号/ID",
    1: "机构编码",
    2: "机构名称",
    3: "机构类型代码",
    4: "成立日期",
    5: "终止日期",
    6: "状态码",
    7: "省份",
    8: "城市",
    9: "区县",
    10: "经度",
    11: "纬度",
    12: "总行名称",
    13: "行业代码",
    14: "详细地址",
    15: "邮政编码",
    16: "未知字段1",
    17: "未知字段2",
    18: "简称",
    19: "银行类型",
    20: "银行规模类型",
    21: "营业所类型",
    22: "行政区划代码",
    23: "监管机构",
    24: "经营范围"
}

# 重命名列
df.columns = [column_meanings.get(col, f"未知列{col}") for col in df.columns]

# 显示数据基本信息
print(f"\n数据形状 (行数, 列数): {df.shape}")

# 显示前5行数据（只显示部分列）
important_columns = ["序号/ID", "机构编码", "机构名称", "成立日期", "省份", "城市", "区县", "详细地址", "营业所类型", "监管机构"]
print("\n前5行数据（重要列）:")
print(df[important_columns].head(5).to_string())

# 显示每列的非空值数量和唯一值数量
print("\n每列的数据统计:")
for col in df.columns:
    non_null_count = df[col].count()
    unique_count = df[col].nunique()
    unique_ratio = unique_count / non_null_count if non_null_count > 0 else 0
    print(f"{col}:")
    print(f"  - 非空值数量: {non_null_count} ({non_null_count/len(df)*100:.1f}%)")
    print(f"  - 唯一值数量: {unique_count}")
    print(f"  - 唯一值比例: {unique_ratio:.2f}")
    
    # 显示前几个唯一值（如果唯一值数量较少）
    if 1 < unique_count <= 20:
        unique_values = df[col].dropna().unique()
        print(f"  - 唯一值: {', '.join(str(val) for val in unique_values[:10])}")
        if len(unique_values) > 10:
            print(f"    ... 以及 {len(unique_values)-10} 个其他值")
    print("")

# 分析机构分布
print("\n机构地理分布:")
province_counts = df["省份"].value_counts().head(10)
print(f"省份分布 (前10):\n{province_counts}")

city_counts = df["城市"].value_counts().head(10)
print(f"\n城市分布 (前10):\n{city_counts}")

# 分析成立日期分布
print("\n成立日期分布:")
df["成立日期"] = pd.to_datetime(df["成立日期"], errors="coerce")
year_counts = df["成立日期"].dt.year.value_counts().sort_index()
print(year_counts)

# 分析营业所类型分布
print("\n营业所类型分布:")
branch_type_counts = df["营业所类型"].value_counts()
print(branch_type_counts)

# 输出最终的列含义推测
print("\n最终列含义推测:")
for col_idx, (col_name, meaning) in enumerate(column_meanings.items()):
    print(f"列 {col_idx}: {meaning}")
