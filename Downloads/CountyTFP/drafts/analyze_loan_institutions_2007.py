"""
分析2007年贷款机构数据

此脚本专门分析2007年的贷款机构数据，检查符合条件的机构数量
并提供详细的统计信息。
"""

import pandas as pd
import numpy as np
from load_geo_data import load_geo_data

print("开始分析2007年贷款机构数据...")

# 使用缓存机制加载数据
gdf = load_geo_data(force_reload=False)

# 确保countyid是字符串类型
gdf['countyid'] = gdf['countyid'].astype(str)

# 只筛选2007年的数据，排除市辖区和地级市
data_2007 = gdf[
    (gdf['year'] == 2007) & 
    (gdf['gis_county_type'] != "市辖区") &
    (~gdf['countyid'].str.endswith('00'))  # 排除地级市
].copy()

print(f"2007年数据样本数量: {len(data_2007)}")
print(f"2007年唯一countyid数量: {data_2007['countyid'].nunique()}")

# 分析InstituionCode列
print("\nInstituionCode列分析:")
print(f"非空值数量: {data_2007['InstituionCode'].notna().sum()}")
print(f"空值数量: {data_2007['InstituionCode'].isna().sum()}")
print(f"空值百分比: {data_2007['InstituionCode'].isna().mean() * 100:.2f}%")

# 分析非空InstituionCode
non_null_inst = data_2007[data_2007['InstituionCode'].notna()]
print(f"\n非空InstituionCode数量: {len(non_null_inst)}")

# 检查以B0018A开头的机构
b0018a_inst = non_null_inst[non_null_inst['InstituionCode'].astype(str).str.startswith('B0018A')]
print(f"以B0018A开头的机构数量: {len(b0018a_inst)}")
print(f"以B0018A开头的机构百分比: {len(b0018a_inst) / len(non_null_inst) * 100:.2f}%")

# 符合贷款机构条件的记录
loan_inst = non_null_inst[~non_null_inst['InstituionCode'].astype(str).str.startswith('B0018A')]
print(f"\n符合贷款机构条件的记录数量: {len(loan_inst)}")
print(f"符合贷款机构条件的县数量: {loan_inst['countyid'].nunique()}")

# 分析每个县的贷款机构数量
county_inst_counts = loan_inst.groupby('countyid')['InstituionCode'].count()
print(f"\n每个县的贷款机构数量统计:")
print(f"最小值: {county_inst_counts.min()}")
print(f"最大值: {county_inst_counts.max()}")
print(f"平均值: {county_inst_counts.mean():.2f}")
print(f"中位数: {county_inst_counts.median()}")

# 分析贷款机构数量分布
print("\n贷款机构数量分布:")
inst_counts_dist = county_inst_counts.value_counts().sort_index()
for count, freq in inst_counts_dist.items():
    print(f"  {count}个机构的县数量: {freq}")

# 检查是否有重复的机构代码
print("\n检查重复的机构代码:")
# 对每个县，检查是否有重复的机构代码
county_unique_codes = {}
for county, group in loan_inst.groupby('countyid'):
    unique_codes = group['InstituionCode'].unique()
    county_unique_codes[county] = len(unique_codes)
    
unique_codes_counts = pd.Series(county_unique_codes)
print(f"每个县的唯一机构代码数量统计:")
print(f"最小值: {unique_codes_counts.min()}")
print(f"最大值: {unique_codes_counts.max()}")
print(f"平均值: {unique_codes_counts.mean():.2f}")
print(f"中位数: {unique_codes_counts.median()}")

# 检查机构代码的格式和模式
print("\n机构代码格式分析:")
code_lengths = loan_inst['InstituionCode'].astype(str).str.len()
print(f"机构代码长度范围: {code_lengths.min()} 到 {code_lengths.max()}")
print(f"最常见的机构代码长度: {code_lengths.value_counts().index[0]}")

# 显示一些示例机构代码
print("\n机构代码示例:")
sample_codes = loan_inst['InstituionCode'].sample(min(5, len(loan_inst))).tolist()
for code in sample_codes:
    print(f"  {code}")

# 如果有其他可能相关的列，也进行分析
other_columns = ['InstituionName'] if 'InstituionName' in loan_inst.columns else []
for col in other_columns:
    if col in loan_inst.columns:
        print(f"\n{col}列分析:")
        print(f"非空值数量: {loan_inst[col].notna().sum()}")
        print(f"唯一值数量: {loan_inst[col].nunique()}")
        print(f"示例值: {loan_inst[col].sample(min(5, len(loan_inst))).tolist()}")

print("\n分析完成")