"""
Loan Institutions Cumulative Distribution Map Generator

This script generates maps showing the cumulative distribution of loan institutions.
Loan institutions are determined by:
1. InstituionCode is not null and does not start with 'B0018A'
2. Year is 2007 or later (when loan business started)

Required variables:
- InstituionCode: Institution code, used to determine if it's a loan institution
- year: Data year (2007-2015)
- countyid: County-level administrative region ID
- geometry: Geographic boundary data

Dependencies:
- Geographic data loaded through load_geo_data() function
- Provincial boundary shapefile
"""

import pandas as pd
import geopandas as gpd
import matplotlib.pyplot as plt
import numpy as np
import os
from matplotlib.colors import LinearSegmentedColormap, BoundaryNorm
from load_geo_data import load_geo_data
import imageio.v2 as imageio
from mpl_toolkits.axes_grid1 import make_axes_locatable

print("Starting to create loan institutions cumulative distribution maps...")

# Load data using cache mechanism
gdf = load_geo_data(force_reload=False)

# Ensure countyid is string type
gdf['countyid'] = gdf['countyid'].astype(str)

# Filter samples: years between 1997 and 2015, excluding urban districts and prefecture-level cities
filtered_data = gdf[
    (gdf['year'] >= 1997) & 
    (gdf['year'] <= 2015) & 
    (gdf['gis_county_type'] != "市辖区") &
    (~gdf['countyid'].str.endswith('00'))  # Exclude prefecture-level cities
].copy()

print(f"Number of samples after filtering: {len(filtered_data)}")
print(f"Number of unique countyids after filtering: {filtered_data['countyid'].nunique()}")

# Load provincial boundary data
province_path = "RAW/Province/province.shp"
provinces = gpd.read_file(province_path)
print(f"Loaded provincial boundary data: {len(provinces)} provinces")

# Create a new column to store cumulative loan institutions
filtered_data['cumulative_loan_inst'] = 0

# Sort by county ID and year
filtered_data = filtered_data.sort_values(['countyid', 'year'])

# Dictionary to store cumulative loan institutions for each county
county_cumulative = {}

# Iterate through data to calculate cumulative loan institutions
print("Calculating cumulative loan institutions for each county...")
for idx, row in filtered_data.iterrows():
    county = row['countyid']
    
    # If this county has a loan institution in this year (InstituionCode is not null and doesn't start with B0018A)
    if pd.notna(row['InstituionCode']) and not str(row['InstituionCode']).startswith('B0018A'):
        # Add 1 to the cumulative total (we're counting institutions, not branches)
        if county in county_cumulative:
            county_cumulative[county] += 1
        else:
            county_cumulative[county] = 1
    
    # Update cumulative loan institutions (as of current year)
    filtered_data.at[idx, 'cumulative_loan_inst'] = county_cumulative.get(county, 0)

# Analyze cumulative institution data distribution to determine appropriate color intervals
inst_values = filtered_data['cumulative_loan_inst'].sort_values()
print(f"Loan institutions cumulative distribution:")
print(f"Minimum: {inst_values.min()}")
print(f"25th percentile: {inst_values.quantile(0.25)}")
print(f"Median: {inst_values.median()}")
print(f"75th percentile: {inst_values.quantile(0.75)}")
print(f"90th percentile: {inst_values.quantile(0.9)}")
print(f"95th percentile: {inst_values.quantile(0.95)}")
print(f"99th percentile: {inst_values.quantile(0.99)}")
print(f"Maximum: {inst_values.max()}")

# Set color boundaries for cumulative institutions
inst_q75 = inst_values.quantile(0.75)
inst_q90 = inst_values.quantile(0.90)
inst_q95 = inst_values.quantile(0.95)
inst_q99 = inst_values.quantile(0.99)
inst_max = inst_values.max()

# Set color boundaries
inst_bounds = [0, 1, inst_q75, inst_q90, inst_q95, inst_max]
# Ensure boundaries are integers and strictly increasing
inst_bounds = [int(np.ceil(b)) for b in inst_bounds]
inst_bounds = sorted(list(set(inst_bounds)))  # Remove possible duplicates
print(f"Color boundaries: {inst_bounds}")

# Create custom color map (light to dark)
colors = ['#F9F8CA', '#96D2B0', '#35B9C5', '#2681B6', '#1E469B']

# Ensure number of colors matches number of boundaries
if len(colors) != len(inst_bounds) - 1:
    if len(inst_bounds) - 1 < len(colors):
        inst_colors = colors[:len(inst_bounds)-1]
    else:
        cmap_temp = LinearSegmentedColormap.from_list('inst_cmap', colors, N=len(inst_bounds)-1)
        inst_colors = [cmap_temp(i/(len(inst_bounds)-2)) for i in range(len(inst_bounds)-1)]
else:
    inst_colors = colors

# Create discrete color map
inst_cmap = LinearSegmentedColormap.from_list('inst_cmap', inst_colors, N=len(inst_bounds)-1)
inst_norm = BoundaryNorm(inst_bounds, inst_cmap.N)

# Create output directory
output_dir = "loan_institutions_maps"

if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    print(f"Created output directory: {output_dir}")

# Create maps for each year
years = range(2007, 2016)  # Start from 2007
map_files = []  # To store map file paths

for year in years:
    print(f"Creating map for {year}...")
    
    # Create a copy of data for current year
    year_data = filtered_data[filtered_data['year'] == year].copy()
    
    # Calculate loan institution statistics for current year
    total_inst = year_data['cumulative_loan_inst'].sum()
    counties_with_inst = year_data[year_data['cumulative_loan_inst'] > 0]['countyid'].nunique()
    total_counties = year_data['countyid'].nunique()
    coverage_percentage = (counties_with_inst / total_counties * 100) if total_counties > 0 else 0
    
    # Create map
    fig, ax = plt.subplots(1, 1, figsize=(15, 10))
    
    # Draw county fill map using custom color range
    year_data.plot(
        column='cumulative_loan_inst',
        ax=ax,
        cmap=inst_cmap,
        norm=inst_norm,
        edgecolor='none',
        legend=False
    )
    
    # Draw provincial boundaries
    provinces.boundary.plot(
        ax=ax,
        edgecolor='black',
        linewidth=0.5,
        zorder=2
    )
    
    # Set map title and layout
    ax.set_title(
        f'Cumulative Loan Institutions by County ({year})\n'
        f'Total Institutions: {int(total_inst)} | Counties with Loan Institutions: {counties_with_inst} out of {total_counties} ({coverage_percentage:.1f}%)',
        fontsize=16
    )
    
    # Remove coordinate axes
    ax.set_axis_off()
    
    # Add color bar legend
    divider = make_axes_locatable(ax)
    cax = divider.append_axes("right", size="2%", pad=0.1)
    
    # Create color bar
    sm = plt.cm.ScalarMappable(cmap=inst_cmap, norm=inst_norm)
    sm._A = []  # Empty array for matplotlib internal calculation
    cbar = fig.colorbar(sm, cax=cax)
    cbar.set_label('Number of Loan Institutions', fontsize=12)
    
    # Set color bar ticks
    cbar.set_ticks(inst_bounds)
    cbar.set_ticklabels([str(int(b)) for b in inst_bounds])
    
    # Add data source and notes
    plt.annotate(
        'Note: Excluding urban districts and prefecture-level cities.\n'
        'Source: Institution data (1997-2015)',
        xy=(0.02, 0.02),
        xycoords='figure fraction',
        fontsize=10,
        bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8)
    )
    
    # Save map
    output_file = os.path.join(output_dir, f"loan_institutions_map_{year}.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close(fig)
    print(f"Saved map to: {output_file}")
    
    # Add to file list for creating GIF
    map_files.append(output_file)

# Create GIF animation
print("Creating GIF animation...")
gif_path = os.path.join(output_dir, "loan_institutions_map_animation.gif")

# Read all images
images = []
for file_path in map_files:
    images.append(imageio.imread(file_path))

# Create GIF, set frame duration to 2 seconds
imageio.mimsave(gif_path, images, duration=2.0)
print(f"GIF animation saved to: {gif_path}")

print("All maps created successfully!")