import pandas as pd
import numpy as np

# 读取Excel文件
file_path = "PSBC.xlsx"
print(f"正在读取文件: {file_path}")

# 读取Excel文件，不使用标题行
df = pd.read_excel(file_path, header=None)

# 跳过第一行（如果第一行是空的）
if df.iloc[0].isna().all() or df.iloc[0].isna().sum() > 20:
    df = df.iloc[1:].reset_index(drop=True)

# 根据数据内容推测每列的含义
column_meanings = {
    0: "序号/ID",
    1: "机构编码",
    2: "机构名称",
    3: "机构类型代码",
    4: "成立日期",
    5: "终止日期",
    6: "状态码",
    7: "省份",
    8: "城市",
    9: "区县",
    10: "经度",
    11: "纬度",
    12: "总行名称",
    13: "行业代码",
    14: "详细地址",
    15: "邮政编码",
    16: "未知字段1",
    17: "未知字段2",
    18: "简称",
    19: "银行类型",
    20: "银行规模类型",
    21: "营业所类型",
    22: "行政区划代码",
    23: "监管机构",
    24: "经营范围"
}

# 重命名列
df.columns = [column_meanings.get(col, f"未知列{col}") for col in df.columns]

# 显示数据基本信息
print(f"\n数据形状 (行数, 列数): {df.shape}")

# 显示前5行数据（只显示部分列）
important_columns = ["序号/ID", "机构编码", "机构名称", "成立日期", "省份", "城市", "区县", "详细地址", "营业所类型"]
print("\n前5行数据（重要列）:")
print(df[important_columns].head(5))

# 分析营业所类型分布
print("\n营业所类型分布:")
branch_type_counts = df["营业所类型"].value_counts()
print(branch_type_counts)

# 分析省份分布
print("\n省份分布 (前10):")
province_counts = df["省份"].value_counts().head(10)
print(province_counts)

# 分析成立日期分布
print("\n成立日期分布 (按年份):")
df["成立日期"] = pd.to_datetime(df["成立日期"], errors="coerce")
year_counts = df["成立日期"].dt.year.value_counts().sort_index().head(10)
print(year_counts)

# 输出最终的列含义推测
print("\n最终列含义推测:")
for col_idx, meaning in column_meanings.items():
    print(f"列 {col_idx}: {meaning}")

# 显示每列的非空值比例
print("\n每列的非空值比例:")
for col in df.columns:
    non_null_ratio = df[col].count() / len(df) * 100
    print(f"{col}: {non_null_ratio:.1f}%")

# 显示状态码和机构类型代码的分布
if "状态码" in df.columns:
    print("\n状态码分布:")
    status_counts = df["状态码"].value_counts()
    print(status_counts)

if "机构类型代码" in df.columns:
    print("\n机构类型代码分布:")
    type_counts = df["机构类型代码"].value_counts()
    print(type_counts)
