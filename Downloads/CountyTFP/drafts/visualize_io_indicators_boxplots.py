"""
投入产出指标分组箱线图生成脚本

本脚本用于生成七个关键投入产出指标的分组箱线图，
按照PSBC网点开设情况(有/无)进行分组，展示不同组别的指标分布差异。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from matplotlib.ticker import MaxNLocator
from load_geo_data import load_geo_data
import matplotlib as mpl

# 设置matplotlib参数，使图表接近顶级期刊风格
plt.rcParams.update({
    'font.family': 'serif',
    'font.serif': ['Times New Roman'],
    'font.size': 12,           # 基础字体大小增大
    'axes.titlesize': 16,      # 标题字体增大
    'axes.labelsize': 14,      # 轴标签字体增大
    'xtick.labelsize': 12,     # x轴刻度标签增大
    'ytick.labelsize': 12,     # y轴刻度标签增大
    'legend.fontsize': 12,     # 图例字体增大
    'figure.figsize': (12, 8), # 图表尺寸调整
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'savefig.bbox': 'tight',
    'savefig.pad_inches': 0.1, # 增加边距
})

print("开始创建投入产出指标分组箱线图...")

# 创建输出目录
output_dir = "io_indicators_boxplots"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    print(f"创建输出目录: {output_dir}")

# 使用缓存机制加载数据
print("加载缓存数据...")
gdf = load_geo_data(force_reload=False)
print(f"数据形状: {gdf.shape}")

# 确保countyid是字符串类型
gdf['countyid'] = gdf['countyid'].astype(str)

# 筛选样本：年份在1997到2015之间，排除市辖区和地级市
filtered_data = gdf[
    (gdf['year'] >= 1997) & 
    (gdf['year'] <= 2015) & 
    (gdf['gis_county_type'] != "市辖区") &
    (~gdf['countyid'].str.endswith('00'))  # 排除地级市
].copy()

print(f"筛选后的样本数量: {len(filtered_data)}")
print(f"筛选后的唯一countyid数量: {filtered_data['countyid'].nunique()}")
print(f"筛选后的年份范围: {filtered_data['year'].min()} 到 {filtered_data['year'].max()}")

# 计算投入产出指标
print("\n计算投入产出指标...")

# 1. TFP (全要素生产率) - 直接使用数据中的tfp列
print("1. 使用数据中的TFP值")
# TFP已经在数据中，不需要额外计算

# 2. Y/L (劳动生产率) - agr_outputq / qlabor
print("2. 计算劳动生产率 (Y/L)")
filtered_data['Y_L'] = filtered_data['agr_outputq'] / filtered_data['qlabor']

# 3. Y/K (资本产出比) - agr_outputq / qcapital
print("3. 计算资本产出比 (Y/K)")
filtered_data['Y_K'] = filtered_data['agr_outputq'] / filtered_data['qcapital']

# 4. Y/Land (土地产出比) - agr_outputq / qland
print("4. 计算土地产出比 (Y/Land)")
filtered_data['Y_Land'] = filtered_data['agr_outputq'] / filtered_data['qland']

# 5. K/L (资本劳动比) - qcapital / qlabor
print("5. 计算资本劳动比 (K/L)")
filtered_data['K_L'] = filtered_data['qcapital'] / filtered_data['qlabor']

# 6. Land/L (土地劳动比) - qland / qlabor
print("6. 计算土地劳动比 (Land/L)")
filtered_data['Land_L'] = filtered_data['qland'] / filtered_data['qlabor']

# 7. unitcost (单位成本) - 计算各种投入成本之和除以产出
print("7. 计算单位成本 (unitcost)")

# 计算劳动成本
if all(col in filtered_data.columns for col in ['plabor', 'qlabor']):
    filtered_data['labor_cost'] = filtered_data['plabor'] * filtered_data['qlabor']
    print("   计算劳动成本: plabor * qlabor")

# 计算资本成本
if all(col in filtered_data.columns for col in ['pcapital', 'qcapital']):
    filtered_data['capital_cost'] = filtered_data['pcapital'] * filtered_data['qcapital']
    print("   计算资本成本: pcapital * qcapital")

# 计算土地成本
if all(col in filtered_data.columns for col in ['pland', 'qland']):
    filtered_data['land_cost'] = filtered_data['pland'] * filtered_data['qland']
    print("   计算土地成本: pland * qland")

# 检查是否有中间投入材料相关列
material_cols = [col for col in filtered_data.columns if 'mater' in col.lower() or 'inter' in col.lower()]
print(f"   发现可能的中间投入列: {material_cols}")

# 计算中间投入成本
if all(col in filtered_data.columns for col in ['pmater', 'qmater']):
    filtered_data['material_cost'] = filtered_data['pmater'] * filtered_data['qmater']
    print("   计算中间投入成本: pmater * qmater")
elif 'intermediate_cost' in filtered_data.columns:
    print("   使用现有的intermediate_cost列")
elif 'material_cost' in filtered_data.columns:
    print("   使用现有的material_cost列")

# 计算总成本
cost_columns = [col for col in ['labor_cost', 'capital_cost', 'land_cost', 'material_cost', 'intermediate_cost'] 
                if col in filtered_data.columns]

if cost_columns:
    # 计算总成本 = 所有投入成本之和
    filtered_data['total_cost'] = filtered_data[cost_columns].sum(axis=1)
    
    # 计算单位成本 = 总成本 / 产出
    filtered_data['unitcost'] = filtered_data['total_cost'] / filtered_data['agr_outputq']
    
    print(f"   计算总成本: {' + '.join(cost_columns)}")
    print(f"   计算单位成本: total_cost / agr_outputq")
else:
    print("   警告: 无法计算单位成本，缺少成本数据列")
    # 创建一个空的unitcost列
    filtered_data['unitcost'] = np.nan

# 处理无穷大和NaN值
indicators = ['tfp', 'Y_L', 'Y_K', 'Y_Land', 'K_L', 'Land_L', 'unitcost']
for indicator in indicators:
    filtered_data[indicator] = filtered_data[indicator].replace([np.inf, -np.inf], np.nan)
    # 计算有效值的数量和百分比
    valid_count = filtered_data[indicator].notna().sum()
    valid_percent = valid_count / len(filtered_data) * 100
    print(f"{indicator}有效值数量: {valid_count} ({valid_percent:.2f}%)")

# 确保number_all_PSBC列存在，如果不存在则创建并设为0
if 'number_all_PSBC' not in filtered_data.columns:
    print("警告: 数据中不存在number_all_PSBC列，创建并设为0")
    filtered_data['number_all_PSBC'] = 0
else:
    # 确保number_all_PSBC为数值类型，并将NaN替换为0
    filtered_data['number_all_PSBC'] = pd.to_numeric(filtered_data['number_all_PSBC'], errors='coerce').fillna(0)

# 指标名称映射和描述
indicator_info = {
    'tfp': {
        'name': 'Total Factor Productivity',
        'description': 'Measures overall production efficiency',
        'higher_better': True,
        'color': '#1f77b4'  # 蓝色
    },
    'Y_L': {
        'name': 'Labor Productivity',
        'description': 'Output per unit of labor',
        'higher_better': True,
        'color': '#ff7f0e'  # 橙色
    },
    'Y_K': {
        'name': 'Capital Productivity',
        'description': 'Output per unit of capital',
        'higher_better': True,
        'color': '#2ca02c'  # 绿色
    },
    'Y_Land': {
        'name': 'Land Productivity',
        'description': 'Output per unit of land',
        'higher_better': True,
        'color': '#d62728'  # 红色
    },
    'K_L': {
        'name': 'Capital-Labor Ratio',
        'description': 'Capital per unit of labor',
        'higher_better': None,  # 中性指标
        'color': '#9467bd'  # 紫色
    },
    'Land_L': {
        'name': 'Land-Labor Ratio',
        'description': 'Land per unit of labor',
        'higher_better': None,  # 中性指标
        'color': '#8c564b'  # 棕色
    },
    'unitcost': {
        'name': 'Unit Cost',
        'description': 'Cost per unit of output',
        'higher_better': False,  # 更低更好
        'color': '#e377c2'  # 粉色
    }
}

# 为每个指标创建分组箱线图
for indicator, info in indicator_info.items():
    print(f"\n创建{indicator}指标的分组箱线图...")
    
    # 检查数据有效性
    valid_data = filtered_data[filtered_data[indicator].notna()].copy()
    if len(valid_data) == 0:
        print(f"警告: {indicator}指标没有有效数据，跳过")
        continue
    
    # 创建分组标签
    valid_data['PSBC_status'] = valid_data['number_all_PSBC'].apply(
        lambda x: 'With PSBC' if x > 0 else 'Without PSBC'
    )
    
    # 获取年份列表
    years = sorted(valid_data['year'].unique())
    
    # 创建图表 - 增加图表尺寸以适应更大的字体
    fig, ax = plt.subplots(figsize=(16, 9))
    
    # 设置箱线图的位置
    positions = []
    labels = []
    
    # 使用色盲友好且黑白打印友好的配色方案
    without_psbc_color = '#4575b4'  # 深蓝色
    with_psbc_color = '#d73027'     # 深红色
    
    for i, year in enumerate(years):
        # 每年有两个箱，位置在2i和2i+1
        positions.extend([2*i, 2*i+1])
        labels.extend([str(year), ''])
    
    # 准备箱线图数据
    boxplot_data = []
    for year in years:
        # 获取当年无PSBC网点的县的指标值
        without_psbc = valid_data[(valid_data['year'] == year) & 
                                 (valid_data['PSBC_status'] == 'Without PSBC')][indicator].values
        boxplot_data.append(without_psbc)
        
        # 获取当年有PSBC网点的县的指标值
        with_psbc = valid_data[(valid_data['year'] == year) & 
                              (valid_data['PSBC_status'] == 'With PSBC')][indicator].values
        boxplot_data.append(with_psbc)
    
    # 绘制箱线图
    bplot = ax.boxplot(
        boxplot_data,
        positions=positions,
        patch_artist=True,      # 填充箱体
        widths=0.7,             # 增加箱宽
        showfliers=False,       # 不显示异常值点
        medianprops={'color': 'black', 'linewidth': 2.0},  # 中位数线加粗
        boxprops={'alpha': 0.8, 'linewidth': 1.5},         # 箱体边框加粗
        whiskerprops={'linewidth': 1.5},                   # 须线加粗
        capprops={'linewidth': 1.5}                        # 须端线加粗
    )
    
    # 设置箱体颜色和填充样式
    for i, box in enumerate(bplot['boxes']):
        if i % 2 == 0:  # 无PSBC组
            box.set_facecolor(without_psbc_color)
            box.set_hatch('/')  # 添加斜线填充图案
        else:  # 有PSBC组
            box.set_facecolor(with_psbc_color)
            box.set_hatch('\\')  # 添加反斜线填充图案
    
    # 设置x轴刻度和标签
    ax.set_xticks(positions[::2])  # 每年只显示一个刻度
    ax.set_xticklabels(years, rotation=45, ha='right')  # 调整标签对齐方式
    
    # 设置图表标题和标签 - 使用更大的字体
    ax.set_title(f'Distribution of {info["name"]} by PSBC Presence (1997-2015)', 
                 fontweight='bold', fontsize=18, pad=15)  # 增加标题字体和上边距
    ax.set_xlabel('Year', fontweight='bold', fontsize=16, labelpad=10)  # 增加x轴标签字体和边距
    ax.set_ylabel(info['name'], fontweight='bold', fontsize=16, labelpad=10)  # 增加y轴标签字体和边距
    
    # 添加图例 - 增大图例并放置在更合适的位置
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor=without_psbc_color, edgecolor='black', alpha=0.8, hatch='/', label='Without PSBC'),
        Patch(facecolor=with_psbc_color, edgecolor='black', alpha=0.8, hatch='\\', label='With PSBC')
    ]
    legend = ax.legend(handles=legend_elements, 
                      loc='upper right',  # 放在右上角
                      frameon=True, 
                      framealpha=0.9,
                      fontsize=14,  # 增大图例字体
                      edgecolor='black',
                      borderpad=1,  # 增加图例内边距
                      handletextpad=0.5)  # 调整图例元素与文本的间距
    legend.get_frame().set_linewidth(1.5)  # 加粗图例边框
    
    # 添加网格线 - 调整网格线样式使其不干扰数据展示
    ax.grid(True, linestyle='--', alpha=0.3, axis='y', linewidth=1)
    
    # 添加注释 - 增大字体
    ax.annotate('excludes outside values', xy=(0.01, 0.01), xycoords='figure fraction', 
                fontsize=12, color='gray', style='italic')
    
    # 设置y轴范围 - 根据数据实际分布调整
    # 计算所有数据的5%和95%分位数，避免极端值影响显示
    all_data = np.concatenate(boxplot_data)
    if len(all_data) > 0:
        # 使用百分位数而不是最大最小值，避免极端值影响
        lower_bound = np.nanpercentile(all_data, 1)  # 1%分位数
        upper_bound = np.nanpercentile(all_data, 99)  # 99%分位数
        
        # 为不同指标设置不同的边距比例
        if indicator in ['tfp', 'unitcost']:
            # TFP和单位成本可能有较大波动，使用较大边距
            margin_ratio = 0.2
        elif indicator in ['Y_L', 'Y_K', 'Y_Land']:
            # 生产率指标使用中等边距
            margin_ratio = 0.15
        else:
            # 其他指标使用较小边距
            margin_ratio = 0.1
            
        # 计算边距
        data_range = upper_bound - lower_bound
        margin = data_range * margin_ratio
        
        # 设置y轴范围
        y_min = lower_bound - margin
        y_max = upper_bound + margin
        
        # 确保范围包含0（如果数据跨越0）
        if lower_bound < 0 < upper_bound:
            y_min = min(y_min, -margin)  # 确保负值有足够空间
            y_max = max(y_max, margin)   # 确保正值有足够空间
            
        ax.set_ylim(y_min, y_max)
        
        # 根据数据范围调整y轴刻度数量
        ax.yaxis.set_major_locator(MaxNLocator(nbins=6))  # 设置约6个主刻度
        
        print(f"  设置{indicator}的y轴范围: [{y_min:.4f}, {y_max:.4f}]")
    
    # 美化图表边框
    for spine in ax.spines.values():
        spine.set_linewidth(1.5)  # 加粗图表边框
    
    # 调整刻度线长度和宽度
    ax.tick_params(axis='both', which='major', width=1.5, length=6, pad=6)
    
    # 调整布局，确保所有元素都能完整显示
    plt.tight_layout(pad=2.0)  # 增加边距
    
    # 保存图表
    output_file = os.path.join(output_dir, f"{indicator}_psbc_boxplot.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"保存分组箱线图到: {output_file}")
    
    plt.close(fig)

print("\n所有分组箱线图创建完成!")


