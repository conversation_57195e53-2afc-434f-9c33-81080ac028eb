"""
检查缓存数据列名脚本

本脚本用于加载缓存的地理数据并显示其列名，
以便了解可用于计算投入产出指标的变量。
"""

import pandas as pd
import geopandas as gpd
from load_geo_data import load_geo_data

print("加载缓存数据并检查列名...")

# 使用缓存机制加载数据
gdf = load_geo_data(force_reload=False)

# 显示数据基本信息
print(f"\n数据形状 (行数, 列数): {gdf.shape}")

# 显示所有列名
print("\n数据列名:")
for i, col in enumerate(gdf.columns):
    print(f"{i+1}. {col}")

# 检查是否存在投入产出相关的列
io_related_keywords = ['tfp', 'output', 'labor', 'capital', 'land', 'cost', 
                       'y', 'l', 'k', 'production', 'input', 'gdp']

print("\n可能与投入产出相关的列:")
io_related_cols = []
for col in gdf.columns:
    col_lower = col.lower()
    for keyword in io_related_keywords:
        if keyword in col_lower:
            io_related_cols.append(col)
            print(f"- {col}")
            break

# 显示数据类型
print("\n数据类型:")
print(gdf.dtypes)

# 如果存在投入产出相关的列，显示其基本统计信息
if io_related_cols:
    print("\n投入产出相关列的基本统计信息:")
    print(gdf[io_related_cols].describe().T)

print("\n检查完成!")