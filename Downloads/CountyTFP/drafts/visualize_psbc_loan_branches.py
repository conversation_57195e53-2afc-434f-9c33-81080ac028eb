"""
PSBC可贷款网点累计分布和密度地图生成脚本

本脚本用于生成中国邮政储蓄银行(PSBC)可贷款网点的累计分布和密度地图。
可贷款网点的判断标准：
1. InstituionCode不为空且不以'B0018A'开头
2. 年份大于等于2007（PSBC贷款业务从2007年开始）

所需变量:
- number_PSBC: 每个县每年新增的PSBC网点数量
- InstituionCode: 机构代码，用于判断是否为可贷款网点
- year: 数据年份 (2007-2015)
- countyid: 县级行政区ID
- geometry: 地理边界数据（用于计算面积）

依赖文件:
- 通过load_geo_data()函数加载的地理数据
- 省级边界shapefile文件
"""

import pandas as pd
import geopandas as gpd
import matplotlib.pyplot as plt
import numpy as np
import os
from matplotlib.colors import LinearSegmentedColormap, BoundaryNorm
from load_geo_data import load_geo_data
import imageio.v2 as imageio
from mpl_toolkits.axes_grid1 import make_axes_locatable

print("开始创建PSBC可贷款网点累计分布和密度地图...")

# 使用缓存机制加载数据
gdf = load_geo_data(force_reload=False)

# 确保countyid是字符串类型
gdf['countyid'] = gdf['countyid'].astype(str)

# 筛选样本：year在07到15之间，且gis_county_type不是"市辖区"，且countyid不以00结尾
filtered_data = gdf[
    (gdf['year'] >= 2007) & 
    (gdf['year'] <= 2015) & 
    (gdf['gis_county_type'] != "市辖区") &
    (~gdf['countyid'].str.endswith('00'))  # 排除地级市
].copy()

print(f"筛选后的样本数量: {len(filtered_data)}")
print(f"筛选后的唯一countyid数量: {filtered_data['countyid'].nunique()}")

# 加载省级边界数据
province_path = "RAW/Province/province.shp"
provinces = gpd.read_file(province_path)
print(f"加载省级边界数据: {len(provinces)}个省份")

# 准备数据：确保number_PSBC为数值类型，并将NaN替换为0
filtered_data['number_PSBC'] = pd.to_numeric(filtered_data['number_PSBC'], errors='coerce').fillna(0)

# 创建一个新列来存储累计可贷款网点数
filtered_data['cumulative_loan_branches'] = 0

# 按县ID和年份排序
filtered_data = filtered_data.sort_values(['countyid', 'year'])

# 用于存储每个县的累计可贷款网点数
county_cumulative = {}

# 遍历数据计算累计可贷款网点数
print("计算每个县的累计可贷款网点数...")
for idx, row in filtered_data.iterrows():
    county = row['countyid']
    
    # 如果该县该年有新开点（InstituionCode不为空且不以B0018A开头）
    if pd.notna(row['InstituionCode']) and not str(row['InstituionCode']).startswith('B0018A'):
        # 将当年的number_PSBC（新增量）加入累计总数
        if county in county_cumulative:
            county_cumulative[county] += row['number_PSBC']
        else:
            county_cumulative[county] = row['number_PSBC']
    
    # 更新累计可贷款网点数（截至当前年份）
    filtered_data.at[idx, 'cumulative_loan_branches'] = county_cumulative.get(county, 0)

# 计算每个县的面积（平方公里）
print("计算每个县的面积...")
if filtered_data.crs.to_string() == 'EPSG:4326':
    # 转换为适合中国区域的投影坐标系
    filtered_data_proj = filtered_data.to_crs('EPSG:3857')  # Web Mercator
    # 计算面积（平方公里）
    filtered_data['area_sqkm'] = filtered_data_proj.geometry.area / 1e6
else:
    # 如果已经是投影坐标系，直接计算面积
    filtered_data['area_sqkm'] = filtered_data.geometry.area / 1e6

# 计算密度：累计网点数/面积（每平方公里的网点数）
filtered_data['loan_branches_density'] = filtered_data['cumulative_loan_branches'] / filtered_data['area_sqkm']
# 处理可能的无穷大值（当面积为0时）
filtered_data['loan_branches_density'] = filtered_data['loan_branches_density'].replace([np.inf, -np.inf], np.nan).fillna(0)

# 分析累计网点数据分布，确定合适的颜色区间
branches_values = filtered_data['cumulative_loan_branches'].sort_values()
print(f"PSBC累计可贷款网点分布：")
print(f"最小值: {branches_values.min()}")
print(f"25%分位数: {branches_values.quantile(0.25)}")
print(f"中位数: {branches_values.median()}")
print(f"75%分位数: {branches_values.quantile(0.75)}")
print(f"90%分位数: {branches_values.quantile(0.9)}")
print(f"95%分位数: {branches_values.quantile(0.95)}")
print(f"99%分位数: {branches_values.quantile(0.99)}")
print(f"最大值: {branches_values.max()}")

# 分析密度数据分布，确定合适的颜色区间
density_values = filtered_data['loan_branches_density'].sort_values()
print(f"PSBC可贷款网点密度分布（每平方公里网点数）：")
print(f"最小值: {density_values.min()}")
print(f"25%分位数: {density_values.quantile(0.25)}")
print(f"中位数: {density_values.median()}")
print(f"75%分位数: {density_values.quantile(0.75)}")
print(f"90%分位数: {density_values.quantile(0.9)}")
print(f"95%分位数: {density_values.quantile(0.95)}")
print(f"99%分位数: {density_values.quantile(0.99)}")
print(f"最大值: {density_values.max()}")

# 设置累计网点数的颜色边界
branches_q75 = branches_values.quantile(0.75)
branches_q90 = branches_values.quantile(0.90)
branches_q95 = branches_values.quantile(0.95)
branches_q99 = branches_values.quantile(0.99)
branches_max = branches_values.max()

branches_bounds = [0, 1, branches_q75, branches_q90, branches_q95, branches_max]
branches_bounds = [int(np.ceil(b)) for b in branches_bounds]
branches_bounds = sorted(list(set(branches_bounds)))  # 去除可能的重复值
print(f"累计网点数颜色边界: {branches_bounds}")

# 设置密度的颜色边界
density_q75 = density_values.quantile(0.75)
density_q90 = density_values.quantile(0.90)
density_q95 = density_values.quantile(0.95)
density_q99 = density_values.quantile(0.99)
density_max = density_values.max()

density_bounds = [0, 0.0001, density_q75, density_q90, density_q95, density_max]
density_bounds = [float(np.ceil(b * 10000) / 10000) for b in density_bounds]
density_bounds = sorted(list(set(density_bounds)))  # 去除可能的重复值
print(f"密度颜色边界: {density_bounds}")

# 创建自定义颜色映射（从浅到深）
colors = ['#F9F8CA', '#96D2B0', '#35B9C5', '#2681B6', '#1E469B']

# 确保颜色数量与边界数量匹配（累计网点数）
if len(colors) != len(branches_bounds) - 1:
    if len(branches_bounds) - 1 < len(colors):
        branches_colors = colors[:len(branches_bounds)-1]
    else:
        cmap_temp = LinearSegmentedColormap.from_list('branches_cmap', colors, N=len(branches_bounds)-1)
        branches_colors = [cmap_temp(i/(len(branches_bounds)-2)) for i in range(len(branches_bounds)-1)]
else:
    branches_colors = colors

# 确保颜色数量与边界数量匹配（密度）
if len(colors) != len(density_bounds) - 1:
    if len(density_bounds) - 1 < len(colors):
        density_colors = colors[:len(density_bounds)-1]
    else:
        cmap_temp = LinearSegmentedColormap.from_list('density_cmap', colors, N=len(density_bounds)-1)
        density_colors = [cmap_temp(i/(len(density_bounds)-2)) for i in range(len(density_bounds)-1)]
else:
    density_colors = colors

# 创建离散颜色映射
branches_cmap = LinearSegmentedColormap.from_list('branches_cmap', branches_colors, N=len(branches_bounds)-1)
branches_norm = BoundaryNorm(branches_bounds, branches_cmap.N)

density_cmap = LinearSegmentedColormap.from_list('density_cmap', density_colors, N=len(density_bounds)-1)
density_norm = BoundaryNorm(density_bounds, density_cmap.N)

# 创建输出目录
branches_output_dir = "psbc_loan_branches_maps"
density_output_dir = "psbc_loan_branches_density_maps"

if not os.path.exists(branches_output_dir):
    os.makedirs(branches_output_dir)
    print(f"创建输出目录: {branches_output_dir}")

if not os.path.exists(density_output_dir):
    os.makedirs(density_output_dir)
    print(f"创建输出目录: {density_output_dir}")

# 为每年创建地图
years = range(2007, 2016)  # 修改为从2007年开始
branches_map_files = []  # 用于存储累计网点数地图文件路径
density_map_files = []   # 用于存储密度地图文件路径

for year in years:
    print(f"创建{year}年的地图...")
    
    # 创建当年的数据副本
    year_data = filtered_data[filtered_data['year'] == year].copy()
    
    # 计算当年PSBC可贷款网点的统计信息
    total_branches = year_data['cumulative_loan_branches'].sum()
    counties_with_branches = year_data[year_data['cumulative_loan_branches'] > 0]['countyid'].nunique()
    total_counties = year_data['countyid'].nunique()
    coverage_percentage = (counties_with_branches / total_counties * 100) if total_counties > 0 else 0
    
    # 计算平均密度
    avg_density = year_data[year_data['cumulative_loan_branches'] > 0]['loan_branches_density'].mean()
    
    # 创建累计网点数地图
    fig1, ax1 = plt.subplots(1, 1, figsize=(15, 10))
    
    # 绘制county填充地图，使用自定义颜色范围
    year_data.plot(
        column='cumulative_loan_branches',
        ax=ax1,
        cmap=branches_cmap,
        norm=branches_norm,
        edgecolor='none',
        legend=False
    )
    
    # 绘制省界
    provinces.boundary.plot(
        ax=ax1,
        edgecolor='black',
        linewidth=0.5,
        zorder=2
    )
    
    # 设置地图标题和布局
    ax1.set_title(
        f'PSBC Cumulative Loan Branches by County ({year})\n'
        f'Total Branches: {int(total_branches)} | Counties with Loan Branches: {counties_with_branches} out of {total_counties} ({coverage_percentage:.1f}%)',
        fontsize=16
    )
    
    # 移除坐标轴
    ax1.set_axis_off()
    
    # 添加颜色条图例
    divider1 = make_axes_locatable(ax1)
    cax1 = divider1.append_axes("right", size="2%", pad=0.1)
    
    # 创建颜色条
    sm1 = plt.cm.ScalarMappable(cmap=branches_cmap, norm=branches_norm)
    sm1._A = []  # 空数组，用于matplotlib内部计算
    cbar1 = fig1.colorbar(sm1, cax=cax1)
    cbar1.set_label('Number of PSBC Loan Branches', fontsize=12)
    
    # 设置颜色条刻度
    cbar1.set_ticks(branches_bounds)
    
    # 添加数据来源和注释
    plt.annotate(
        'Note: Excluding urban districts and prefecture-level cities.\n'
        'Source: PSBC branch data (2001-2015)',
        xy=(0.02, 0.02),
        xycoords='figure fraction',
        fontsize=10,
        bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8)
    )
    
    # 保存累计网点数地图
    branches_output_file = os.path.join(branches_output_dir, f"psbc_loan_branches_map_{year}.png")
    plt.savefig(branches_output_file, dpi=300, bbox_inches='tight')
    plt.close(fig1)
    print(f"保存累计网点数地图到: {branches_output_file}")
    
    # 添加到文件列表，用于创建GIF
    branches_map_files.append(branches_output_file)
    
    # 创建密度地图
    fig2, ax2 = plt.subplots(1, 1, figsize=(15, 10))
    
    # 绘制county填充地图，使用自定义颜色范围
    year_data.plot(
        column='loan_branches_density',
        ax=ax2,
        cmap=density_cmap,
        norm=density_norm,
        edgecolor='none',
        legend=False
    )
    
    # 绘制省界
    provinces.boundary.plot(
        ax=ax2,
        edgecolor='black',
        linewidth=0.5,
        zorder=2
    )
    
    # 设置地图标题和布局
    ax2.set_title(
        f'PSBC Loan Branch Density by County ({year})\n'
        f'Total Branches: {int(total_branches)} | Counties with Loan Branches: {counties_with_branches} out of {total_counties} ({coverage_percentage:.1f}%)\n'
        f'Average Density: {avg_density:.4f} branches per sq km',
        fontsize=16
    )
    
    # 移除坐标轴
    ax2.set_axis_off()
    
    # 添加颜色条图例
    divider2 = make_axes_locatable(ax2)
    cax2 = divider2.append_axes("right", size="2%", pad=0.1)
    
    # 创建颜色条
    sm2 = plt.cm.ScalarMappable(cmap=density_cmap, norm=density_norm)
    sm2._A = []  # 空数组，用于matplotlib内部计算
    cbar2 = fig2.colorbar(sm2, cax=cax2)
    cbar2.set_label('PSBC Loan Branches per sq km', fontsize=12)
    
    # 设置颜色条刻度
    cbar2.set_ticks(density_bounds)
    # 格式化刻度标签，使用科学计数法显示小数
    cbar2.set_ticklabels([f"{b:.4f}" for b in density_bounds])
    
    # 添加数据来源和注释
    plt.annotate(
        'Note: Excluding urban districts and prefecture-level cities.\n'
        'Source: PSBC branch data (2001-2015)',
        xy=(0.02, 0.02),
        xycoords='figure fraction',
        fontsize=10,
        bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8)
    )
    
    # 保存密度地图
    density_output_file = os.path.join(density_output_dir, f"psbc_loan_branches_density_map_{year}.png")
    plt.savefig(density_output_file, dpi=300, bbox_inches='tight')
    plt.close(fig2)
    print(f"保存密度地图到: {density_output_file}")
    
    # 添加到文件列表，用于创建GIF
    density_map_files.append(density_output_file)

# 创建累计网点数GIF动画
print("创建累计网点数GIF动画...")
branches_gif_path = os.path.join(branches_output_dir, "psbc_loan_branches_map_animation.gif")

# 读取所有图片
branches_images = []
for file_path in branches_map_files:
    branches_images.append(imageio.imread(file_path))

# 创建GIF，设置每帧停留时间为2秒
imageio.mimsave(branches_gif_path, branches_images, duration=2.0)
print(f"累计网点数GIF动画已保存到: {branches_gif_path}")

# 创建密度GIF动画
print("创建密度GIF动画...")
density_gif_path = os.path.join(density_output_dir, "psbc_loan_branches_density_map_animation.gif")

# 读取所有图片
density_images = []
for file_path in density_map_files:
    density_images.append(imageio.imread(file_path))

# 创建GIF，设置每帧停留时间为2秒
imageio.mimsave(density_gif_path, density_images, duration=2.0)
print(f"密度GIF动画已保存到: {density_gif_path}")

print("所有地图创建完成!")

