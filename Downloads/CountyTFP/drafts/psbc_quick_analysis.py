import pandas as pd

# 读取Excel文件，只读取前1000行以加快处理速度
print("正在读取PSBC.xlsx文件的前1000行...")
df = pd.read_excel("PSBC.xlsx", header=None, nrows=1000)

# 跳过第一行（如果第一行是空的）
if df.iloc[0].isna().all() or df.iloc[0].isna().sum() > 20:
    df = df.iloc[1:].reset_index(drop=True)

# 根据数据内容推测每列的含义
column_meanings = {
    0: "序号/ID",
    1: "机构编码",
    2: "机构名称",
    3: "机构类型代码",
    4: "成立日期",
    5: "终止日期",
    6: "状态码",
    7: "省份",
    8: "城市",
    9: "区县",
    10: "经度",
    11: "纬度",
    12: "总行名称",
    13: "行业代码",
    14: "详细地址",
    15: "邮政编码",
    16: "未知字段1",
    17: "未知字段2",
    18: "简称",
    19: "银行类型",
    20: "银行规模类型",
    21: "营业所类型",
    22: "行政区划代码",
    23: "监管机构",
    24: "经营范围"
}

# 显示数据基本信息
print(f"\n数据形状 (前1000行): {df.shape}")

# 显示前5行数据（只显示部分列）
important_columns = [0, 1, 2, 4, 7, 8, 9, 21]
print("\n前5行数据（重要列）:")
for i in range(min(5, len(df))):
    row = df.iloc[i]
    print(f"\n行 {i+1}:")
    for col in important_columns:
        col_name = column_meanings.get(col, f"列{col}")
        value = row[col]
        print(f"  {col_name}: {value}")

# 输出列含义推测
print("\n列含义推测:")
for col_idx, meaning in column_meanings.items():
    print(f"列 {col_idx}: {meaning}")

# 显示中文内容的列
print("\n包含中文内容的列:")
for col in df.columns:
    if df[col].dtype == 'object':  # 只检查字符串类型的列
        sample = df[col].dropna().head(1).values
        if len(sample) > 0:
            sample_str = str(sample[0])
            if any('\u4e00' <= char <= '\u9fff' for char in sample_str):
                print(f"列 {col} ({column_meanings.get(col, '未知')}): {sample_str[:50]}...")
