"""
PSBC网点统计图表生成脚本

本脚本用于生成中国邮政储蓄银行(PSBC)网点数量的统计图表，包括：
1. 每年有PSBC网点的县数量柱状图
2. 累计覆盖县数量的柱状图和百分比折线图
3. 年度与累计覆盖县数量对比图

所需变量:
- number_all_PSBC: 每个县的PSBC网点数量
- year: 数据年份 (1997-2015)
- countyid: 县级行政区ID
- gis_county_type: 县级行政区类型 (可选，用于筛选)

依赖文件:
- 通过load_geo_data()函数加载的地理数据
"""

import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
from load_geo_data import load_geo_data

print("开始分析PSBC网点数据...")

# 使用缓存机制加载数据
gdf = load_geo_data()

# 检查数据基本情况
print(f"\n原始数据总记录数: {len(gdf)}")
print(f"原始数据中唯一countyid数量: {gdf['countyid'].nunique()}")
print(f"原始数据年份范围: {gdf['year'].min()} 到 {gdf['year'].max()}")

# 确保countyid是字符串类型
gdf['countyid'] = gdf['countyid'].astype(str)

# 筛选样本：year在97到15之间
filtered_data = gdf[
    (gdf['year'] >= 1997) & 
    (gdf['year'] <= 2015)
].copy()

print(f"\n筛选后的样本数量: {len(filtered_data)}")
print(f"筛选后的唯一countyid数量: {filtered_data['countyid'].nunique()}")

# 准备数据：计算累计覆盖的county数量
years = range(1997, 2016)
total_counties_annual = []  # 每年的唯一county数量
counties_with_psbc_annual = []  # 每年有PSBC网点的county数量
counties_with_psbc_cumulative = []  # 累计有PSBC网点的county数量
percentages_annual = []  # 每年的覆盖率
percentages_cumulative = []  # 累计覆盖率

# 用于跟踪累计覆盖的county
cumulative_counties_with_psbc = set()

for year in years:
    year_data = filtered_data[filtered_data['year'] == year]
    total_count = year_data['countyid'].nunique()
    total_counties_annual.append(total_count)
    
    # 计算当年number_all_PSBC不为0的county
    counties_with_bank = year_data[
        (year_data['number_all_PSBC'].notna()) & 
        (year_data['number_all_PSBC'] > 0)
    ]['countyid'].unique()
    
    counties_with_psbc_annual.append(len(counties_with_bank))
    
    # 更新累计覆盖的county集合
    cumulative_counties_with_psbc.update(counties_with_bank)
    counties_with_psbc_cumulative.append(len(cumulative_counties_with_psbc))
    
    # 计算年度覆盖率
    percentage_annual = (len(counties_with_bank) / total_count * 100) if total_count > 0 else 0
    percentages_annual.append(percentage_annual)
    
    # 计算累计覆盖率（相对于当年的总county数）
    percentage_cumulative = (len(cumulative_counties_with_psbc) / total_count * 100) if total_count > 0 else 0
    percentages_cumulative.append(percentage_cumulative)
    
    print(f"{year}年:")
    print(f"  - 唯一countyid数量: {total_count}")
    print(f"  - 当年有PSBC网点的county数量: {len(counties_with_bank)} (占比 {percentage_annual:.2f}%)")
    print(f"  - 累计有PSBC网点的county数量: {len(cumulative_counties_with_psbc)} (占比 {percentage_cumulative:.2f}%)")

# 创建输出目录
output_dir = "psbc_charts"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    print(f"创建输出目录: {output_dir}")

# 创建图表1：每年有PSBC网点的county数量
plt.figure(figsize=(12, 6))
bars = plt.bar(years, counties_with_psbc_annual, color='#3182bd', width=0.7)

# 在柱子上方添加数值标签
for bar, count in zip(bars, counties_with_psbc_annual):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 5, 
             str(count), ha='center', va='bottom', fontsize=9)

plt.title('Number of Counties with PSBC Branches by Year', fontsize=14)
plt.xlabel('Year', fontsize=12)
plt.ylabel('Number of Counties', fontsize=12)
plt.xticks(years, [str(year) for year in years], rotation=45)
plt.grid(axis='y', linestyle='--', alpha=0.7)
plt.tight_layout()

# 保存图表
output_file = os.path.join(output_dir, "counties_with_psbc_annual.png")
plt.savefig(output_file, dpi=300, bbox_inches='tight')
plt.close()
print(f"保存柱状图到: {output_file}")

# 创建图表2：累计有PSBC网点的county数量
fig, ax1 = plt.subplots(figsize=(12, 6))

# 绘制柱状图（累计有PSBC网点的county数量）
bars = ax1.bar(years, counties_with_psbc_cumulative, color='#3182bd', width=0.7)

# 在柱子上方添加数值标签
for bar, count in zip(bars, counties_with_psbc_cumulative):
    ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 5, 
             str(count), ha='center', va='bottom', fontsize=9)

ax1.set_title('Cumulative Number of Counties with PSBC Branches (1997-2015)', fontsize=14)
ax1.set_xlabel('Year', fontsize=12)
ax1.set_ylabel('Cumulative Number of Counties with PSBC', fontsize=12)
ax1.set_xticks(years)
ax1.set_xticklabels([str(year) for year in years], rotation=45)
ax1.grid(axis='y', linestyle='--', alpha=0.7)

# 创建第二个Y轴显示累计百分比
ax2 = ax1.twinx()
ax2.plot(years, percentages_cumulative, 'r-', marker='o', linewidth=2)
ax2.set_ylabel('Cumulative Percentage of Counties with PSBC (%)', color='r', fontsize=12)
ax2.tick_params(axis='y', labelcolor='r')
ax2.set_ylim(0, max(percentages_cumulative) * 1.2)  # 设置适当的Y轴范围

# 在折线图上添加百分比标签
for i, percentage in enumerate(percentages_cumulative):
    ax2.text(years[i], percentage + 1, f"{percentage:.1f}%", 
             color='r', ha='center', va='bottom', fontsize=8)

plt.tight_layout()

# 保存图表
output_file = os.path.join(output_dir, "cumulative_counties_with_psbc.png")
plt.savefig(output_file, dpi=300, bbox_inches='tight')
plt.close()
print(f"保存柱状图到: {output_file}")

# 创建图表3：对比年度和累计有PSBC网点的county数量
fig, ax = plt.subplots(figsize=(14, 7))

# 设置柱状图的位置
x = np.arange(len(years))
width = 0.35

# 绘制年度数量柱状图
annual_bars = ax.bar(x - width/2, counties_with_psbc_annual, width, label='Annual', color='#9ecae1')
# 绘制累计数量柱状图
cumulative_bars = ax.bar(x + width/2, counties_with_psbc_cumulative, width, label='Cumulative', color='#3182bd')

# 添加数值标签
for bars, counts in [(annual_bars, counties_with_psbc_annual), (cumulative_bars, counties_with_psbc_cumulative)]:
    for bar, count in zip(bars, counts):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2, height + 5,
                str(count), ha='center', va='bottom', fontsize=8)

ax.set_title('Annual vs. Cumulative Number of Counties with PSBC Branches (1997-2015)', fontsize=14)
ax.set_xlabel('Year', fontsize=12)
ax.set_ylabel('Number of Counties', fontsize=12)
ax.set_xticks(x)
ax.set_xticklabels([str(year) for year in years], rotation=45)
ax.legend()
ax.grid(axis='y', linestyle='--', alpha=0.7)

plt.tight_layout()

# 保存图表
output_file = os.path.join(output_dir, "annual_vs_cumulative_counties_with_psbc.png")
plt.savefig(output_file, dpi=300, bbox_inches='tight')
plt.close()
print(f"保存柱状图到: {output_file}")

print("所有图表创建完成!")
```
</augment