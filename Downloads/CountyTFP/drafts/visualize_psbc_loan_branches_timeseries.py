"""
PSBC Loan Branches Cumulative Distribution Time Series Map Generator

This script generates time series maps showing the cumulative distribution of PSBC loan branches.
Loan branches are determined by:
1. InstituionCode is not null and does not start with 'B0018A'
2. Year is 2007 or later (PSBC loan business started in 2007)

Required variables:
- number_PSBC: Number of new PSBC branches in each county each year
- InstituionCode: Institution code, used to determine if it's a loan branch
- year: Data year (2007-2015)
- countyid: County-level administrative region ID
- geometry: Geographic boundary data

Dependencies:
- Geographic data loaded through load_geo_data() function
- Provincial boundary shapefile
"""

import pandas as pd
import geopandas as gpd
import matplotlib.pyplot as plt
import numpy as np
import os
from matplotlib.colors import LinearSegmentedColormap, BoundaryNorm
from load_geo_data import load_geo_data
import imageio.v2 as imageio
from mpl_toolkits.axes_grid1 import make_axes_locatable

print("Starting to create PSBC loan branches cumulative distribution time series maps...")

# Load data using cache mechanism
gdf = load_geo_data(force_reload=False)

# Ensure countyid is string type
gdf['countyid'] = gdf['countyid'].astype(str)

# Filter samples: years between 2001 and 2015, excluding urban districts and prefecture-level cities
filtered_data = gdf[
    (gdf['year'] >= 2001) & 
    (gdf['year'] <= 2015) & 
    (gdf['gis_county_type'] != "市辖区") &
    (~gdf['countyid'].str.endswith('00'))  # Exclude prefecture-level cities
].copy()

print(f"Number of samples after filtering: {len(filtered_data)}")
print(f"Number of unique countyids after filtering: {filtered_data['countyid'].nunique()}")

# Load provincial boundary data
province_path = "RAW/Province/province.shp"
provinces = gpd.read_file(province_path)
print(f"Loaded provincial boundary data: {len(provinces)} provinces")

# Prepare data: ensure number_PSBC is numeric type and replace NaN with 0
filtered_data['number_PSBC'] = pd.to_numeric(filtered_data['number_PSBC'], errors='coerce').fillna(0)

# Create a new column to store cumulative loan branches
filtered_data['cumulative_loan_branches'] = 0

# Sort by county ID and year
filtered_data = filtered_data.sort_values(['countyid', 'year'])

# Dictionary to store cumulative loan branches for each county
county_cumulative = {}

# Iterate through data to calculate cumulative loan branches
print("Calculating cumulative loan branches for each county...")
for idx, row in filtered_data.iterrows():
    county = row['countyid']
    
    # If this county has a new branch in this year (InstituionCode is not null and doesn't start with B0018A)
    if pd.notna(row['InstituionCode']) and not str(row['InstituionCode']).startswith('B0018A'):
        # Add the number_PSBC (new branches) to the cumulative total
        if county in county_cumulative:
            county_cumulative[county] += row['number_PSBC']
        else:
            county_cumulative[county] = row['number_PSBC']
    
    # Update cumulative loan branches (as of current year)
    filtered_data.at[idx, 'cumulative_loan_branches'] = county_cumulative.get(county, 0)

# Analyze cumulative branch data distribution to determine appropriate color intervals
branches_values = filtered_data['cumulative_loan_branches'].sort_values()
print(f"PSBC cumulative loan branches distribution:")
print(f"Minimum: {branches_values.min()}")
print(f"25th percentile: {branches_values.quantile(0.25)}")
print(f"Median: {branches_values.median()}")
print(f"75th percentile: {branches_values.quantile(0.75)}")
print(f"90th percentile: {branches_values.quantile(0.9)}")
print(f"95th percentile: {branches_values.quantile(0.95)}")
print(f"99th percentile: {branches_values.quantile(0.99)}")
print(f"Maximum: {branches_values.max()}")

# Set color boundaries for cumulative branches
branches_q75 = branches_values.quantile(0.75)
branches_q90 = branches_values.quantile(0.90)
branches_q95 = branches_values.quantile(0.95)
branches_q99 = branches_values.quantile(0.99)
branches_max = branches_values.max()

# Set color boundaries
branches_bounds = [0, 1, branches_q75, branches_q90, branches_q95, branches_max]
# Ensure boundaries are integers and strictly increasing
branches_bounds = [int(np.ceil(b)) for b in branches_bounds]
branches_bounds = sorted(list(set(branches_bounds)))  # Remove possible duplicates
print(f"Color boundaries: {branches_bounds}")

# Create custom color map (light to dark)
colors = ['#F9F8CA', '#96D2B0', '#35B9C5', '#2681B6', '#1E469B']

# Ensure number of colors matches number of boundaries
if len(colors) != len(branches_bounds) - 1:
    if len(branches_bounds) - 1 < len(colors):
        branches_colors = colors[:len(branches_bounds)-1]
    else:
        cmap_temp = LinearSegmentedColormap.from_list('branches_cmap', colors, N=len(branches_bounds)-1)
        branches_colors = [cmap_temp(i/(len(branches_bounds)-2)) for i in range(len(branches_bounds)-1)]
else:
    branches_colors = colors

# Create discrete color map
branches_cmap = LinearSegmentedColormap.from_list('branches_cmap', branches_colors, N=len(branches_bounds)-1)
branches_norm = BoundaryNorm(branches_bounds, branches_cmap.N)

# Create output directory
branches_output_dir = "psbc_loan_branches_timeseries_maps"

if not os.path.exists(branches_output_dir):
    os.makedirs(branches_output_dir)
    print(f"Created output directory: {branches_output_dir}")

# Create maps for each year
years = range(2007, 2016)  # Start from 2007
branches_map_files = []  # To store cumulative branch map file paths

for year in years:
    print(f"Creating map for {year}...")
    
    # Create a copy of data for current year
    year_data = filtered_data[filtered_data['year'] == year].copy()
    
    # Calculate PSBC loan branch statistics for current year
    total_branches = year_data['cumulative_loan_branches'].sum()
    counties_with_branches = year_data[year_data['cumulative_loan_branches'] > 0]['countyid'].nunique()
    total_counties = year_data['countyid'].nunique()
    coverage_percentage = (counties_with_branches / total_counties * 100) if total_counties > 0 else 0
    
    # Create cumulative branches map
    fig1, ax1 = plt.subplots(1, 1, figsize=(15, 10))
    
    # Draw county fill map using custom color range
    year_data.plot(
        column='cumulative_loan_branches',
        ax=ax1,
        cmap=branches_cmap,
        norm=branches_norm,
        edgecolor='none',
        legend=False
    )
    
    # Draw provincial boundaries
    provinces.boundary.plot(
        ax=ax1,
        edgecolor='black',
        linewidth=0.5,
        zorder=2
    )
    
    # Set map title and layout
    ax1.set_title(
        f'PSBC Cumulative Loan Branches by County ({year})\n'
        f'Total Branches: {int(total_branches)} | Counties with Loan Branches: {counties_with_branches} out of {total_counties} ({coverage_percentage:.1f}%)',
        fontsize=16
    )
    
    # Remove coordinate axes
    ax1.set_axis_off()
    
    # Add color bar legend
    divider1 = make_axes_locatable(ax1)
    cax1 = divider1.append_axes("right", size="2%", pad=0.1)
    
    # Create color bar
    sm1 = plt.cm.ScalarMappable(cmap=branches_cmap, norm=branches_norm)
    sm1._A = []  # Empty array for matplotlib internal calculation
    cbar1 = fig1.colorbar(sm1, cax=cax1)
    cbar1.set_label('Number of PSBC Loan Branches', fontsize=12)
    
    # Set color bar ticks
    cbar1.set_ticks(branches_bounds)
    cbar1.set_ticklabels([str(int(b)) for b in branches_bounds])
    
    # Add data source and notes
    plt.annotate(
        'Note: Excluding urban districts and prefecture-level cities.\n'
        'Source: PSBC branch data (2001-2015)',
        xy=(0.02, 0.02),
        xycoords='figure fraction',
        fontsize=10,
        bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8)
    )
    
    # Save cumulative branches map
    branches_output_file = os.path.join(branches_output_dir, f"psbc_loan_branches_timeseries_map_{year}.png")
    plt.savefig(branches_output_file, dpi=300, bbox_inches='tight')
    plt.close(fig1)
    print(f"Saved cumulative branches map to: {branches_output_file}")
    
    # Add to file list for creating GIF
    branches_map_files.append(branches_output_file)

# Create cumulative branches GIF animation
print("Creating cumulative branches GIF animation...")
branches_gif_path = os.path.join(branches_output_dir, "psbc_loan_branches_timeseries_map_animation.gif")

# Read all images
branches_images = []
for file_path in branches_map_files:
    branches_images.append(imageio.imread(file_path))

# Create GIF, set frame duration to 2 seconds
imageio.mimsave(branches_gif_path, branches_images, duration=2.0)
print(f"Cumulative branches GIF animation saved to: {branches_gif_path}")

print("All maps created successfully!")