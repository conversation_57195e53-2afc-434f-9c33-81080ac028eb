"""
投入产出指标时间序列范围面积图生成脚本

本脚本用于生成七个关键投入产出指标随时间变化的范围面积图，
展示每个指标的平均值和分布范围随时间的变化趋势。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from matplotlib.ticker import MaxNLocator
from load_geo_data import load_geo_data
import matplotlib as mpl

# 设置matplotlib参数，使图表接近顶级期刊风格
plt.rcParams.update({
    'font.family': 'serif',
    'font.serif': ['Times New Roman'],
    'font.size': 10,
    'axes.titlesize': 12,
    'axes.labelsize': 10,
    'xtick.labelsize': 9,
    'ytick.labelsize': 9,
    'legend.fontsize': 9,
    'figure.figsize': (8, 5),  # 黄金比例
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'savefig.bbox': 'tight',
    'savefig.pad_inches': 0.05,
})

print("开始创建投入产出指标时间序列范围面积图...")

# 创建输出目录
output_dir = "io_indicators_range_charts"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    print(f"创建输出目录: {output_dir}")

# 使用缓存机制加载数据
print("加载缓存数据...")
gdf = load_geo_data(force_reload=False)
print(f"数据形状: {gdf.shape}")

# 确保countyid是字符串类型
gdf['countyid'] = gdf['countyid'].astype(str)

# 筛选样本：年份在1997到2015之间，排除市辖区和地级市
filtered_data = gdf[
    (gdf['year'] >= 1997) & 
    (gdf['year'] <= 2015) & 
    (gdf['gis_county_type'] != "市辖区") &
    (~gdf['countyid'].str.endswith('00'))  # 排除地级市
].copy()

print(f"筛选后的样本数量: {len(filtered_data)}")
print(f"筛选后的唯一countyid数量: {filtered_data['countyid'].nunique()}")
print(f"筛选后的年份范围: {filtered_data['year'].min()} 到 {filtered_data['year'].max()}")

# 计算投入产出指标
print("\n计算投入产出指标...")

# 1. TFP (全要素生产率) - 直接使用数据中的tfp列
print("1. 使用数据中的TFP值")
# TFP已经在数据中，不需要额外计算

# 2. Y/L (劳动生产率) - agr_outputq / qlabor
print("2. 计算劳动生产率 (Y/L)")
filtered_data['Y_L'] = filtered_data['agr_outputq'] / filtered_data['qlabor']

# 3. Y/K (资本产出比) - agr_outputq / qcapital
print("3. 计算资本产出比 (Y/K)")
filtered_data['Y_K'] = filtered_data['agr_outputq'] / filtered_data['qcapital']

# 4. Y/Land (土地产出比) - agr_outputq / qland
print("4. 计算土地产出比 (Y/Land)")
filtered_data['Y_Land'] = filtered_data['agr_outputq'] / filtered_data['qland']

# 5. K/L (资本劳动比) - qcapital / qlabor
print("5. 计算资本劳动比 (K/L)")
filtered_data['K_L'] = filtered_data['qcapital'] / filtered_data['qlabor']

# 6. Land/L (土地劳动比) - qland / qlabor
print("6. 计算土地劳动比 (Land/L)")
filtered_data['Land_L'] = filtered_data['qland'] / filtered_data['qlabor']

# 7. unitcost (单位成本) - 计算各种投入成本之和除以产出
print("7. 计算单位成本 (unitcost)")

# 计算劳动成本
if all(col in filtered_data.columns for col in ['plabor', 'qlabor']):
    filtered_data['labor_cost'] = filtered_data['plabor'] * filtered_data['qlabor']
    print("   计算劳动成本: plabor * qlabor")

# 计算资本成本
if all(col in filtered_data.columns for col in ['pcapital', 'qcapital']):
    filtered_data['capital_cost'] = filtered_data['pcapital'] * filtered_data['qcapital']
    print("   计算资本成本: pcapital * qcapital")

# 计算土地成本
if all(col in filtered_data.columns for col in ['pland', 'qland']):
    filtered_data['land_cost'] = filtered_data['pland'] * filtered_data['qland']
    print("   计算土地成本: pland * qland")

# 检查是否有中间投入材料相关列
material_cols = [col for col in filtered_data.columns if 'mater' in col.lower() or 'inter' in col.lower()]
print(f"   发现可能的中间投入列: {material_cols}")

# 计算中间投入成本
if all(col in filtered_data.columns for col in ['pmater', 'qmater']):
    filtered_data['material_cost'] = filtered_data['pmater'] * filtered_data['qmater']
    print("   计算中间投入成本: pmater * qmater")
elif 'intermediate_cost' in filtered_data.columns:
    print("   使用现有的intermediate_cost列")
elif 'material_cost' in filtered_data.columns:
    print("   使用现有的material_cost列")

# 计算总成本
cost_columns = [col for col in ['labor_cost', 'capital_cost', 'land_cost', 'material_cost', 'intermediate_cost'] 
                if col in filtered_data.columns]

if cost_columns:
    # 计算总成本 = 所有投入成本之和
    filtered_data['total_cost'] = filtered_data[cost_columns].sum(axis=1)
    
    # 计算单位成本 = 总成本 / 产出
    filtered_data['unitcost'] = filtered_data['total_cost'] / filtered_data['agr_outputq']
    
    print(f"   计算总成本: {' + '.join(cost_columns)}")
    print(f"   计算单位成本: total_cost / agr_outputq")
else:
    print("   警告: 无法计算单位成本，缺少成本数据列")
    # 创建一个空的unitcost列
    filtered_data['unitcost'] = np.nan

# 处理无穷大和NaN值
indicators = ['tfp', 'Y_L', 'Y_K', 'Y_Land', 'K_L', 'Land_L', 'unitcost']
for indicator in indicators:
    filtered_data[indicator] = filtered_data[indicator].replace([np.inf, -np.inf], np.nan)
    # 计算有效值的数量和百分比
    valid_count = filtered_data[indicator].notna().sum()
    valid_percent = valid_count / len(filtered_data) * 100
    print(f"{indicator}有效值数量: {valid_count} ({valid_percent:.2f}%)")

# 指标名称映射和描述
indicator_info = {
    'tfp': {
        'name': 'Total Factor Productivity',
        'description': 'Measures overall production efficiency',
        'higher_better': True,
        'color': '#1f77b4'  # 蓝色
    },
    'Y_L': {
        'name': 'Labor Productivity',
        'description': 'Output per unit of labor',
        'higher_better': True,
        'color': '#ff7f0e'  # 橙色
    },
    'Y_K': {
        'name': 'Capital Productivity',
        'description': 'Output per unit of capital',
        'higher_better': True,
        'color': '#2ca02c'  # 绿色
    },
    'Y_Land': {
        'name': 'Land Productivity',
        'description': 'Output per unit of land',
        'higher_better': True,
        'color': '#d62728'  # 红色
    },
    'K_L': {
        'name': 'Capital-Labor Ratio',
        'description': 'Capital per unit of labor',
        'higher_better': None,  # 中性指标
        'color': '#9467bd'  # 紫色
    },
    'Land_L': {
        'name': 'Land-Labor Ratio',
        'description': 'Land per unit of labor',
        'higher_better': None,  # 中性指标
        'color': '#8c564b'  # 棕色
    },
    'unitcost': {
        'name': 'Unit Cost',
        'description': 'Cost per unit of output',
        'higher_better': False,  # 更低更好
        'color': '#e377c2'  # 粉色
    }
}

# 为每个指标创建范围面积图
for indicator, info in indicator_info.items():
    print(f"\n创建{indicator}指标的范围面积图...")
    
    # 检查数据有效性
    valid_data = filtered_data[filtered_data[indicator].notna()]
    if len(valid_data) == 0:
        print(f"警告: {indicator}指标没有有效数据，跳过")
        continue
    
    # 按年份分组计算统计量
    yearly_stats = valid_data.groupby('year')[indicator].agg([
        ('mean', 'mean'),
        ('p5', lambda x: x.quantile(0.05)),
        ('p95', lambda x: x.quantile(0.95))
    ]).reset_index()
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(8, 5))
    
    # 绘制范围面积
    ax.fill_between(
        yearly_stats['year'], 
        yearly_stats['p5'], 
        yearly_stats['p95'], 
        alpha=0.3, 
        color=info['color'],
        label='5th-95th Percentile Range'
    )
    
    # 绘制平均值线
    ax.plot(
        yearly_stats['year'], 
        yearly_stats['mean'], 
        color=info['color'],
        linewidth=2,
        label='Mean'
    )
    
    # 设置图表标题和标签
    ax.set_title(f'Temporal Trends in {info["name"]} (1997-2015)')
    ax.set_xlabel('Year')
    ax.set_ylabel(info['name'])
    
    # 设置x轴刻度为整数年份
    ax.xaxis.set_major_locator(MaxNLocator(integer=True))
    
    # 添加网格线
    ax.grid(True, linestyle='--', alpha=0.7)
    
    # 添加图例
    ax.legend(loc='best', frameon=True, framealpha=0.8)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    output_file = os.path.join(output_dir, f"{indicator}_range_chart.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"保存范围面积图到: {output_file}")
    
    plt.close(fig)

print("\n所有范围面积图创建完成!")

