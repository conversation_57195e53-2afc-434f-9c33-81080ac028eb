"""
贷款机构数据排查脚本

本脚本用于检查InstituionCode列的数据情况，
排查为什么找不到符合条件的贷款县。
"""

import pandas as pd
import numpy as np
from load_geo_data import load_geo_data

print("开始排查贷款机构数据...")

# 使用缓存机制加载数据
gdf = load_geo_data(force_reload=False)
print(f"数据形状: {gdf.shape}")

# 确保countyid是字符串类型
gdf['countyid'] = gdf['countyid'].astype(str)

# 筛选样本：年份在1997到2015之间，排除市辖区和地级市
filtered_data = gdf[
    (gdf['year'] >= 1997) & 
    (gdf['year'] <= 2015) & 
    (gdf['gis_county_type'] != "市辖区") &
    (~gdf['countyid'].str.endswith('00'))  # 排除地级市
].copy()

print(f"筛选后的样本数量: {len(filtered_data)}")
print(f"筛选后的唯一countyid数量: {filtered_data['countyid'].nunique()}")
print(f"筛选后的年份范围: {filtered_data['year'].min()} 到 {filtered_data['year'].max()}")

# 检查InstituionCode列是否存在
if 'InstituionCode' not in filtered_data.columns:
    print("\n警告: 数据中不存在InstituionCode列")
    print("可用的列名:")
    for col in filtered_data.columns:
        print(f"  - {col}")
    
    # 检查是否有类似的列名
    similar_cols = [col for col in filtered_data.columns if 'institu' in col.lower() or 'code' in col.lower()]
    if similar_cols:
        print("\n发现可能相关的列:")
        for col in similar_cols:
            print(f"  - {col}")
else:
    print("\nInstituionCode列存在，进行数据检查...")
    
    # 检查InstituionCode列的基本情况
    non_null_count = filtered_data['InstituionCode'].notna().sum()
    print(f"InstituionCode非空值数量: {non_null_count} ({non_null_count/len(filtered_data)*100:.2f}%)")
    
    # 检查InstituionCode的值分布
    if non_null_count > 0:
        # 检查以B0018A开头的值数量
        b0018a_count = filtered_data[filtered_data['InstituionCode'].notna()]['InstituionCode'].astype(str).str.startswith('B0018A').sum()
        print(f"以B0018A开头的InstituionCode数量: {b0018a_count} ({b0018a_count/non_null_count*100:.2f}%)")
        
        # 检查不以B0018A开头的值数量
        non_b0018a_count = non_null_count - b0018a_count
        print(f"不以B0018A开头的InstituionCode数量: {non_b0018a_count} ({non_b0018a_count/non_null_count*100:.2f}%)")
        
        # 检查不同年份的InstituionCode分布
        print("\n各年份InstituionCode情况:")
        for year in sorted(filtered_data['year'].unique()):
            year_data = filtered_data[filtered_data['year'] == year]
            year_non_null = year_data['InstituionCode'].notna().sum()
            year_b0018a = year_data[year_data['InstituionCode'].notna()]['InstituionCode'].astype(str).str.startswith('B0018A').sum()
            year_non_b0018a = year_non_null - year_b0018a
            
            print(f"  {year}年: 非空值 {year_non_null}, B0018A开头 {year_b0018a}, 非B0018A开头 {year_non_b0018a}")
        
        # 检查2017年及以后的数据
        if filtered_data['year'].max() >= 2017:
            print("\n2017年及以后的InstituionCode情况:")
            post_2017_data = filtered_data[filtered_data['year'] >= 2017]
            post_2017_counties = post_2017_data['countyid'].nunique()
            
            post_2017_non_null = post_2017_data['InstituionCode'].notna().sum()
            post_2017_b0018a = post_2017_data[post_2017_data['InstituionCode'].notna()]['InstituionCode'].astype(str).str.startswith('B0018A').sum()
            post_2017_non_b0018a = post_2017_non_null - post_2017_b0018a
            
            print(f"  2017年及以后的县数量: {post_2017_counties}")
            print(f"  InstituionCode非空值数量: {post_2017_non_null}")
            print(f"  B0018A开头的数量: {post_2017_b0018a}")
            print(f"  非B0018A开头的数量: {post_2017_non_b0018a}")
            
            # 检查有非B0018A开头InstituionCode的县
            if post_2017_non_b0018a > 0:
                counties_with_non_b0018a = post_2017_data[
                    post_2017_data['InstituionCode'].notna() & 
                    ~post_2017_data['InstituionCode'].astype(str).str.startswith('B0018A')
                ]['countyid'].unique()
                
                print(f"  2017年及以后有非B0018A开头InstituionCode的县数量: {len(counties_with_non_b0018a)}")
                if len(counties_with_non_b0018a) > 0:
                    print(f"  示例县ID: {', '.join(counties_with_non_b0018a[:5])}")
        else:
            print("\n警告: 数据中没有2017年及以后的记录")
            print(f"数据年份范围: {filtered_data['year'].min()} 到 {filtered_data['year'].max()}")

print("\n排查完成")