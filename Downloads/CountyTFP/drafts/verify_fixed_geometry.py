"""
验证修复后的GeoJSON文件是否包含所有匹配步骤的几何信息

本脚本用于验证修复后的GeoJSON文件是否真的包含了所有匹配步骤（包括直接ID匹配）的几何信息。
"""

import geopandas as gpd
import pandas as pd
import matplotlib.pyplot as plt
import os

def main():
    print("开始验证修复后的GeoJSON文件...")
    
    # 文件路径
    original_geojson = 'matching_results/all_matched.geojson'
    fixed_geojson = 'matching_results/all_matched_fixed.geojson'
    output_dir = 'verification_results'
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 检查文件是否存在
    if not os.path.exists(fixed_geojson):
        print(f"错误: 找不到修复后的GeoJSON文件 {fixed_geojson}")
        return
    
    # 读取修复后的GeoJSON文件
    print("\n读取修复后的GeoJSON文件...")
    fixed_gdf = gpd.read_file(fixed_geojson)
    print(f"修复后的GeoJSON文件行数: {len(fixed_gdf)}")
    
    # 检查几何信息
    valid_geometry_count = fixed_gdf.geometry.notna().sum()
    print(f"有效几何信息记录数: {valid_geometry_count}/{len(fixed_gdf)} ({valid_geometry_count/len(fixed_gdf):.2%})")
    
    # 按匹配来源统计有效几何信息
    if 'match_source' in fixed_gdf.columns:
        print("\n按匹配来源统计有效几何信息:")
        match_sources = fixed_gdf['match_source'].unique()
        
        for source in match_sources:
            source_gdf = fixed_gdf[fixed_gdf['match_source'] == source]
            valid_count = source_gdf.geometry.notna().sum()
            print(f"{source}: {valid_count}/{len(source_gdf)} ({valid_count/len(source_gdf):.2%})")
    
    # 如果原始GeoJSON文件存在，比较两者
    if os.path.exists(original_geojson):
        print("\n比较原始GeoJSON和修复后的GeoJSON...")
        original_gdf = gpd.read_file(original_geojson)
        print(f"原始GeoJSON文件行数: {len(original_gdf)}")
        
        # 检查原始几何信息
        original_valid_count = original_gdf.geometry.notna().sum()
        print(f"原始GeoJSON有效几何信息记录数: {original_valid_count}/{len(original_gdf)} ({original_valid_count/len(original_gdf):.2%})")
        
        # 按匹配来源比较
        if 'match_source' in original_gdf.columns and 'match_source' in fixed_gdf.columns:
            print("\n按匹配来源比较原始和修复后的GeoJSON:")
            
            for source in match_sources:
                original_source_gdf = original_gdf[original_gdf['match_source'] == source]
                fixed_source_gdf = fixed_gdf[fixed_gdf['match_source'] == source]
                
                original_valid = original_source_gdf.geometry.notna().sum()
                fixed_valid = fixed_source_gdf.geometry.notna().sum()
                
                original_pct = original_valid/len(original_source_gdf) if len(original_source_gdf) > 0 else 0
                fixed_pct = fixed_valid/len(fixed_source_gdf) if len(fixed_source_gdf) > 0 else 0
                
                improvement = fixed_pct - original_pct
                
                print(f"{source}:")
                print(f"  原始: {original_valid}/{len(original_source_gdf)} ({original_pct:.2%})")
                print(f"  修复后: {fixed_valid}/{len(fixed_source_gdf)} ({fixed_pct:.2%})")
                print(f"  改进: {improvement:.2%}")
    
    # 创建可视化
    print("\n创建可视化...")
    
    # 创建地图，显示各匹配来源的几何信息分布
    if 'match_source' in fixed_gdf.columns:
        # 定义颜色映射
        colors = {
            '直接ID匹配': '#1f77b4',      # 蓝色
            '省份代码+地名匹配': '#ff7f0e',  # 橙色
            '行政区划代码映射匹配': '#2ca02c',  # 绿色
            '自治州合并匹配': '#d62728',     # 红色
            '地名匹配': '#ff7f0e',        # 橙色
            '映射表匹配': '#2ca02c'        # 绿色
        }
        
        # 创建地图
        fig, ax = plt.subplots(1, 1, figsize=(15, 10))
        
        # 按匹配来源绘制
        for source in match_sources:
            source_gdf = fixed_gdf[fixed_gdf['match_source'] == source]
            valid_source_gdf = source_gdf[source_gdf.geometry.notna()]
            
            if len(valid_source_gdf) > 0:
                valid_source_gdf.plot(
                    ax=ax,
                    color=colors.get(source, '#999999'),
                    edgecolor='black',
                    linewidth=0.1,
                    alpha=0.7
                )
        
        # 添加图例
        patches = []
        for source in match_sources:
            if source in colors:
                source_gdf = fixed_gdf[fixed_gdf['match_source'] == source]
                valid_count = source_gdf.geometry.notna().sum()
                label = f"{source} ({valid_count}/{len(source_gdf)})"
                patches.append(plt.matplotlib.patches.Patch(color=colors[source], label=label))
        
        ax.legend(handles=patches, loc='lower right', fontsize=12)
        
        # 设置标题
        ax.set_title('修复后各匹配来源的几何信息分布', fontsize=16)
        
        # 关闭坐标轴
        ax.set_axis_off()
        
        # 保存地图
        map_file = os.path.join(output_dir, 'fixed_geometry_verification_map.png')
        plt.savefig(map_file, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"地图已保存到: {map_file}")
    
    print("\n验证完成!")

if __name__ == "__main__":
    main()