"""
PSBC网点密度地图生成脚本（使用修复后的GeoJSON）

本脚本用于生成中国邮政储蓄银行(PSBC)网点密度的县级地图和动画。
使用修复后的GeoJSON文件，确保所有匹配的记录都有正确的几何信息。

所需变量:
- number_all_PSBC: 每个县的PSBC网点数量
- year: 数据年份 (2001-2015)
- countyid: 县级行政区ID
- gis_county_type: 县级行政区类型
- gis_province: 省份名称
- geometry: 地理边界数据

依赖文件:
- 通过load_geo_data()函数加载的地理数据
- 省级边界shapefile文件
"""

import pandas as pd
import geopandas as gpd
import matplotlib.pyplot as plt
import numpy as np
import os
from matplotlib.colors import LinearSegmentedColormap, BoundaryNorm
from load_geo_data import load_geo_data
import imageio.v2 as imageio
import matplotlib.ticker as ticker
from mpl_toolkits.axes_grid1 import make_axes_locatable

print("开始创建PSBC网点数量地图（使用修复后的GeoJSON）...")

# 使用缓存机制加载数据
gdf = load_geo_data(force_reload=True)

# 确保countyid是字符串类型
gdf['countyid'] = gdf['countyid'].astype(str)

# 筛选样本：year在01到15之间
filtered_data = gdf[
    (gdf['year'] >= 2001) & 
    (gdf['year'] <= 2015)
].copy()

print(f"筛选后的样本数量: {len(filtered_data)}")
print(f"筛选后的唯一countyid数量: {filtered_data['countyid'].nunique()}")

# 加载省级边界数据
province_path = "RAW/Province/province.shp"
provinces = gpd.read_file(province_path)
print(f"加载省级边界数据: {len(provinces)}个省份")

# 准备数据：确保number_all_PSBC为数值类型，并将NaN替换为0
filtered_data['number_all_PSBC'] = pd.to_numeric(filtered_data['number_all_PSBC'], errors='coerce').fillna(0)

# 创建输出目录
output_dir = "psbc_density_maps_fixed"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    print(f"创建输出目录: {output_dir}")

# 定义颜色范围
# 使用分位数来定义颜色边界，确保每个颜色区间有大致相同数量的县
non_zero_values = filtered_data[filtered_data['number_all_PSBC'] > 0]['number_all_PSBC']
quantiles = [0] + list(non_zero_values.quantile([0.2, 0.4, 0.6, 0.8, 1.0]))
bounds = [0, 1] + [round(q) for q in quantiles[1:]]
bounds = sorted(list(set(bounds)))  # 去除重复值并排序

print(f"颜色边界: {bounds}")

# 创建颜色映射
colors = ['#f7fbff', '#deebf7', '#c6dbef', '#9ecae1', '#6baed6', '#4292c6', '#2171b5', '#08519c', '#08306b']
cmap = LinearSegmentedColormap.from_list('psbc_cmap', colors, N=len(bounds)-1)
norm = BoundaryNorm(bounds, cmap.N)

# 为每年创建地图
years = range(2001, 2016)
map_files = []  # 用于存储地图文件路径，后续创建GIF

for year in years:
    print(f"创建{year}年的地图...")
    
    # 创建当年的数据副本
    year_data = filtered_data[filtered_data['year'] == year].copy()
    
    # 计算当年PSBC网点的统计信息
    total_branches = year_data['number_all_PSBC'].sum()
    counties_with_branches = year_data[year_data['number_all_PSBC'] > 0]['countyid'].nunique()
    total_counties = year_data['countyid'].nunique()
    coverage_percentage = (counties_with_branches / total_counties * 100) if total_counties > 0 else 0
    
    # 创建地图
    fig, ax = plt.subplots(1, 1, figsize=(15, 10))
    
    # 绘制county填充地图，使用自定义颜色范围
    year_data.plot(
        column='number_all_PSBC',
        ax=ax,
        cmap=cmap,
        norm=norm,
        edgecolor='none',
        legend=False
    )
    
    # 绘制省界
    provinces.boundary.plot(
        ax=ax,
        edgecolor='black',
        linewidth=0.5,
        zorder=2
    )
    
    # 添加颜色条
    divider = make_axes_locatable(ax)
    cax = divider.append_axes("right", size="2%", pad=0.1)
    
    # 创建颜色条
    sm = plt.cm.ScalarMappable(cmap=cmap, norm=norm)
    sm.set_array([])
    
    # 添加颜色条，使用自定义刻度
    cbar = fig.colorbar(sm, cax=cax)
    cbar.set_label('PSBC网点数量', fontsize=12)
    
    # 设置颜色条刻度
    cbar.set_ticks(bounds)
    cbar.set_ticklabels([str(b) for b in bounds])
    
    # 设置标题
    ax.set_title(
        f'{year}年中国邮政储蓄银行(PSBC)网点分布\n'
        f'总网点数: {total_branches:.0f} | 覆盖县数: {counties_with_branches}/{total_counties} ({coverage_percentage:.2f}%)',
        fontsize=16
    )
    
    # 关闭坐标轴
    ax.set_axis_off()
    
    # 保存地图
    output_file = os.path.join(output_dir, f"psbc_density_map_{year}.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"保存地图到: {output_file}")
    
    # 添加到文件列表，用于创建GIF
    map_files.append(output_file)

# 创建GIF动画
print("创建GIF动画...")
gif_path = os.path.join(output_dir, "psbc_density_map_animation.gif")

# 读取所有图片
images = []
for file_path in map_files:
    images.append(imageio.imread(file_path))

# 创建GIF，设置每帧停留时间为2秒
imageio.mimsave(gif_path, images, duration=2.0)
print(f"GIF动画已保存到: {gif_path}")

print("所有地图创建完成!")
