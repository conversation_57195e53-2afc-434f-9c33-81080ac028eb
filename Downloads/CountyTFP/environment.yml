name: county-tfp-analysis
channels:
  - conda-forge
  - defaults
dependencies:
  - python=3.8
  - pandas>=1.3.0
  - numpy>=1.20.0
  - matplotlib>=3.4.0
  - seaborn>=0.11.0
  - plotly>=5.3.0
  - scikit-learn>=1.0.0
  - statsmodels>=0.13.0
  - jupyter>=1.0.0
  - ipykernel>=6.0.0
  - openpyxl>=3.0.0
  - xlrd>=2.0.0
  - geopandas>=0.10.0
  - pyarrow>=6.0.0
  - pytest>=6.0.0
  - black>=21.5b2
  - flake8>=3.9.0
  - pip>=21.1.0
  - pip:
    - jupyter-black>=0.3.1
