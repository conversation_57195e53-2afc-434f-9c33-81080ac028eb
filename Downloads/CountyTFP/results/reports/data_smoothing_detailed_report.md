# 数据平滑处理详细报告

## 概述

本报告详细记录了对1997-2015年县级投入产出和TFP数据的平滑处理过程，包括每个变量的异常值识别标准、处理方法和具体效果。

## 处理策略框架

### 1. 缺失值处理
- **线性插值**: 连续缺失≤2年，使用线性插值
- **增长率估测**: 连续缺失>2年，使用省份历史平均增长率

### 2. 异常值处理层次
- **全国性异常年份**: 2005年和2009年系统性调整
- **省份异常值**: Z分数>5的省份系统性偏差
- **个别极端值**: Z分数>分位数方法的极端值

## 各变量详细处理情况

### 价格变量 (P Variables)

#### 1. plabor (劳动力价格)
**原始状况:**
- 缺失率: 3.87% (2,231个缺失值)
- 主要问题: 早期年份(1997-2003)系统性偏低

**处理方法:**
- **缺失值处理**: 线性插值填补925个，增长率估测填补部分
- **全国性异常**: 未检测到需要调整的年份
- **极端值处理**: 
  - 识别标准: 1%和99%分位数
  - 处理数量: 1,122个极端值
  - 处理方式: 高于99%分位数的值替换为99%分位数值，低于1%分位数的值替换为1%分位数值

**处理效果:**
- 缺失值从2,231减少到1,306 (改善41.5%)
- 极端值得到有效控制

#### 2. pland (土地价格)
**原始状况:**
- 缺失率: 4.77% (2,747个缺失值)
- 主要问题: 1997-2004年系统性偏低，2011-2015年快速上涨

**处理方法:**
- **缺失值处理**: 主要通过增长率估测，改善有限(仅25个)
- **全国性异常**: 未触发调整阈值(偏差<50%)
- **极端值处理**: 
  - 处理数量: 973个极端值
  - 主要集中在2011-2015年的高价格异常值

**处理效果:**
- 缺失值改善微小，主要原因是连续缺失段较长
- 极端高价格得到控制

#### 3. pcapital (资本价格)
**原始状况:**
- 缺失率: 0.00% (无缺失)
- 数据质量较好

**处理方法:**
- **极端值处理**: 91个极端值
- 主要是个别县份的异常高资本价格

#### 4. pmater (材料价格)
**原始状况:**
- 缺失率: 0.00% (无缺失)
- 2008-2015年系统性上涨

**处理方法:**
- **全国性异常**: 未触发调整
- **极端值处理**: 893个极端值
- 主要处理2008年后的价格跳跃异常

### 数量变量 (Q Variables)

#### 5. qlabor (劳动力数量)
**原始状况:**
- 缺失率: 0.00% (无缺失)
- 数据相对稳定

**处理方法:**
- **极端值处理**: 577个极端值
- 主要是个别大县的劳动力规模异常

#### 6. qland (土地数量)
**原始状况:**
- 缺失率: 4.72% (2,722个缺失值)
- 与pland缺失模式类似

**处理方法:**
- **缺失值处理**: 改善效果有限(0个改善)
- **极端值处理**: 1,098个极端值
- 主要问题: 连续缺失段较长，难以通过插值解决

#### 7. qcapital (资本数量)
**原始状况:**
- 缺失率: 0.00% (无缺失)
- 存在显著的年际波动和地区差异

**处理方法:**
- **极端值处理**: 1,154个极端值(所有变量中最多)
- 主要问题: 2010-2015年部分省份资本存量异常增长
- 处理重点: 重庆、内蒙古、福建等省份的极端高值

#### 8. qmater (材料数量)
**原始状况:**
- 缺失率: 0.15% (89个缺失值)
- 整体数据质量较好

**处理方法:**
- **缺失值处理**: 线性插值改善18个
- **极端值处理**: 576个极端值

### 产出变量

#### 9. agr_outputq (农业产出数量)
**原始状况:**
- 缺失率: 1.89% (1,087个缺失值)
- **重大发现**: 2005年和2009年存在全国性系统异常

**处理方法:**
- **缺失值处理**: 线性插值和增长率估测，改善1,082个(99.5%改善率)
- **全国性异常年份调整**:
  - **2005年**: 调整因子0.570 (向下调整43%)
    - 原因: 该年度数据系统性偏高
    - 影响: 调整了约3,000个数据点
  - **2009年**: 调整因子35.567 (向上调整3456%)
    - 原因: 该年度数据系统性偏低，可能是统计口径变化
    - 影响: 调整了约3,100个数据点
- **极端值处理**: 1,151个极端值

**处理效果:**
- 缺失值几乎完全解决(99.5%改善)
- 2005年和2009年的系统性偏差得到纠正

### TFP变量

#### 10. tfp (全要素生产率)
**原始状况:**
- 缺失率: 11.10% (6,397个缺失值，所有变量中最高)
- 数据质量问题最严重

**处理方法:**
- **缺失值处理**: 
  - 线性插值: 适用于短期缺失
  - 增长率估测: 基于省份TFP增长趋势
  - 改善849个缺失值(13.3%改善率)
- **极端值处理**: 1,042个极端值
- **特殊处理**: 
  - 对于TFP<0的异常值，替换为1%分位数
  - 对于TFP异常高的值，替换为99%分位数

**处理效果:**
- 缺失值有所改善但仍然较高
- 极端值得到控制
- 建议后续分析时对TFP数据保持谨慎

## 异常值识别标准

### Z分数方法
- **省份异常**: |Z分数| > 5 (基于全国中位数和MAD)
- **年份异常**: |偏差| > 50% (基于时期中位数)
- **个别异常**: 1%和99%分位数方法

### MAD (中位数绝对偏差)
- 使用MAD而非标准差，更稳健地处理异常值
- 当MAD=0时，使用中位数的0.1%作为替代

## 处理原则

### 保守性原则
- 仅对严重异常进行调整
- 保持数据的原始分布特征
- 避免过度平滑

### 分层处理
1. **系统性异常优先**: 先处理全国性和省份系统性问题
2. **个别异常其次**: 再处理个别极端值
3. **保持相对关系**: 调整时保持变量间的相对关系

### 透明性原则
- 所有调整都有明确记录
- 调整因子和影响范围清晰可查
- 便于后续验证和调整

## 建议

### 后续分析注意事项
1. **TFP数据**: 仍有较高缺失率，分析时需要稳健性检验
2. **2005年和2009年**: 虽已调整，但这两年的数据仍需特别关注
3. **土地相关变量**: pland和qland的缺失模式相似，可能反映统计制度问题
4. **资本数据**: qcapital的极端值最多，反映了这一时期的快速资本积累

### 数据质量评估
- **高质量**: pcapital, pmater, qlabor, qmater
- **中等质量**: plabor, qcapital, agr_outputq (经调整后)
- **需谨慎使用**: pland, qland, tfp

## 技术细节

### 增长率计算方法
- 使用省份年度中位数计算增长率
- 采用中位数增长率而非均值，更稳健
- 全国平均增长率作为备用(默认2%)

### 插值限制
- 最大插值间隔: 2年
- 双向插值: 前向和后向同时进行
- 仅在有足够邻近数据点时进行插值

### 极端值替换
- 使用分位数替换而非删除
- 保持样本量不变
- 维持数据的基本分布形状
