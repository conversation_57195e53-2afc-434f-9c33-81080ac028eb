import pandas as pd
import numpy as np
from datetime import datetime
import pickle


def load_final_merged_counties(counties_file='/Users/<USER>/Downloads/CountyTFP/notebooks/shared_data/county_filter_results.pkl'):
    """
    加载final merged counties的ID列表
    
    参数:
        counties_file: 保存县ID的pickle文件路径
    
    返回:
        list: 县ID列表
    """
    try:
        with open(counties_file, 'rb') as f:
            data = pickle.load(f)
        
        # 根据数据结构提取县ID
        if isinstance(data, dict):
            if 'filtered_county_gdf' in data:
                # 如果是包含filtered_county_gdf的字典
                county_ids = data['filtered_county_gdf']['区划码'].astype(str).tolist()
            elif '区划码' in data:
                # 如果直接是GeoDataFrame数据
                county_ids = data['区划码'].astype(str).tolist()
            else:
                # 尝试从其他可能的键中获取
                for key in data.keys():
                    if hasattr(data[key], 'columns') and '区划码' in data[key].columns:
                        county_ids = data[key]['区划码'].astype(str).tolist()
                        break
                else:
                    raise ValueError("无法从数据中找到区划码列")
        elif hasattr(data, 'columns') and '区划码' in data.columns:
            # 如果直接是DataFrame
            county_ids = data['区划码'].astype(str).tolist()
        else:
            raise ValueError("不支持的数据格式")
        
        print(f"从 {counties_file} 加载了 {len(county_ids)} 个县ID")
        return county_ids
        
    except Exception as e:
        print(f"加载县ID文件失败: {e}")
        print("将返回None，使用所有县进行分析")
        return None

def analyze_county_branch_diffusion(
    input_file='/Users/<USER>/Downloads/CountyTFP/data/processed/psbc.geojson',
    branch_types=None,  # 可以是 ['A'], ['S'], ['L'], ['A', 'S'] 等
    target_counties=None,  # 目标县ID列表，用于地区筛选
    start_year=1997,
    end_year=2020,
    county_id_col='GisCountyID',
    institution_code_col='InstitutionCode',
    open_date_col='OpenDate'
):
    """
    计算每个县首次开设网点的年份和累积分布
    
    参数:
        input_file: 输入文件路径（绝对路径）
        branch_types: 网点类型列表，基于InstitutionCode第六位字符
                     None表示所有类型，['A']表示储蓄网点，['L','S','B','U']表示贷款相关
        target_counties: 目标县ID列表，用于地区筛选
                        None表示所有县，或传入县ID列表进行筛选
        start_year: 分析起始年份
        end_year: 分析结束年份
        county_id_col: 县级ID列名
        institution_code_col: 机构代码列名
        open_date_col: 开业日期列名
    
    返回:
        tuple: (county_first_year_df, annual_cumulative_df)
            - county_first_year_df: DataFrame，列为[county_id, first_year]
            - annual_cumulative_df: DataFrame，列为[year, new_counties, cumulative_counties, cumulative_ratio]
    """
    
    # 1. 数据加载和预处理
    print(f"加载数据: {input_file}")
    if input_file.endswith('.geojson'):
        import geopandas as gpd
        df = gpd.read_file(input_file)
    else:
        df = pd.read_csv(input_file)
    
    print(f"原始数据: {len(df)}条记录")
    
    # 检查必要列
    required_cols = [county_id_col, institution_code_col, open_date_col]
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"缺少必要列: {missing_cols}")
    
    # 2. 数据清洗
    # 过滤有效的县级ID和开业日期
    df = df[df[county_id_col].notna()].copy()
    df[county_id_col] = df[county_id_col].astype(str)
    df[open_date_col] = pd.to_datetime(df[open_date_col], errors='coerce')
    df = df[df[open_date_col].notna()].copy()
    
    # 提取开业年份
    df['open_year'] = df[open_date_col].dt.year
    
    # 筛选年份范围
    df = df[(df['open_year'] >= start_year) & (df['open_year'] <= end_year)].copy()
    
    # 3. 筛选目标县（地区筛选）
    if target_counties is not None:
        print(f"筛选目标县: {len(target_counties)}个县")
        # 确保target_counties也是字符串类型
        target_counties_str = [str(c) for c in target_counties]
        df = df[df[county_id_col].isin(target_counties_str)].copy()
        print(f"地区筛选后数据: {len(df)}条记录，涉及{df[county_id_col].nunique()}个县")
    
    # 4. 筛选网点类型
    if branch_types is not None:
        print(f"筛选网点类型: {branch_types}")
        df['branch_type_code'] = df[institution_code_col].astype(str).str[5]
        df = df[df['branch_type_code'].isin(branch_types)].copy()
    
    print(f"最终筛选后数据: {len(df)}条记录，涉及{df[county_id_col].nunique()}个县")
    
    if len(df) == 0:
        print("警告: 筛选后无数据!")
        return None, None
    
    # 5. 计算每个县第一次开设网点的年份
    county_first_year = df.groupby(county_id_col)['open_year'].min().reset_index()
    county_first_year.columns = [county_id_col, 'first_year']
    
    # 6. 计算年度累积分布
    years = list(range(start_year, end_year + 1))
    annual_stats = []
    
    # 计算每年的新增县和累积县
    cumulative_counties = set()
    total_potential_counties = df[county_id_col].nunique()  # 总的涉及县数
    
    for year in years:
        # 该年新开设网点的县
        new_counties_this_year = set(df[df['open_year'] == year][county_id_col].unique())
        
        # 更新累积县集合
        cumulative_counties.update(new_counties_this_year)
        
        # 计算统计数据
        new_count = len(new_counties_this_year)
        cumulative_count = len(cumulative_counties)
        cumulative_ratio = cumulative_count / total_potential_counties if total_potential_counties > 0 else 0
        
        annual_stats.append({
            'year': year,
            'new_counties': new_count,
            'cumulative_counties': cumulative_count,
            'cumulative_ratio': cumulative_ratio
        })
    
    annual_cumulative = pd.DataFrame(annual_stats)
    
    # 7. 输出统计信息
    print(f"\n=== 分析结果 ===")
    print(f"网点类型: {'所有类型' if branch_types is None else branch_types}")
    print(f"目标县数: {'所有县' if target_counties is None else len(target_counties)}")
    print(f"分析时间段: {start_year}-{end_year}")
    print(f"总网点数: {len(df)}")
    print(f"涉及县数: {total_potential_counties}")
    print(f"首次开点最早年份: {county_first_year['first_year'].min()}")
    print(f"首次开点最晚年份: {county_first_year['first_year'].max()}")
    print(f"最终覆盖县数: {annual_cumulative['cumulative_counties'].iloc[-1]}")
    print(f"最终覆盖率: {annual_cumulative['cumulative_ratio'].iloc[-1]:.2%}")
    
    return county_first_year, annual_cumulative


# 简单的保存函数
def save_diffusion_results(county_first_year, annual_cumulative, output_prefix='psbc_diffusion'):
    """
    保存分析结果
    
    参数:
        county_first_year: 县级首次开点年份DataFrame
        annual_cumulative: 年度累积统计DataFrame
        output_prefix: 输出文件前缀
    """
    if county_first_year is not None:
        county_first_year.to_csv(f'{output_prefix}_county_first_year.csv', index=False)
        print(f"已保存: {output_prefix}_county_first_year.csv")
    
    if annual_cumulative is not None:
        annual_cumulative.to_csv(f'{output_prefix}_annual_cumulative.csv', index=False)
        print(f"已保存: {output_prefix}_annual_cumulative.csv")


# 示例用法
if __name__ == "__main__":
    # 加载final merged counties
    final_counties = load_final_merged_counties()
    
    # 示例1: 分析所有类型网点（仅限final merged counties）
    print("=== 分析所有类型网点 (final merged counties) ===")
    county_first, annual_cum = analyze_county_branch_diffusion(
        target_counties=final_counties
    )
    if county_first is not None:
        save_diffusion_results(county_first, annual_cum, 'all_branches_filtered')
    
    # 示例2: 分析储蓄网点（仅限final merged counties）
    print("\n=== 分析储蓄网点 (A类, final merged counties) ===")
    county_first_a, annual_cum_a = analyze_county_branch_diffusion(
        branch_types=['A'],
        target_counties=final_counties
    )
    if county_first_a is not None:
        save_diffusion_results(county_first_a, annual_cum_a, 'savings_branches_filtered')
    
    # 示例3: 分析贷款相关网点（仅限final merged counties）
    print("\n=== 分析贷款相关网点 (final merged counties) ===")
    county_first_loan, annual_cum_loan = analyze_county_branch_diffusion(
        branch_types=['L', 'S', 'B', 'U'],
        target_counties=final_counties
    )
    if county_first_loan is not None:
        save_diffusion_results(county_first_loan, annual_cum_loan, 'loan_branches_filtered') 