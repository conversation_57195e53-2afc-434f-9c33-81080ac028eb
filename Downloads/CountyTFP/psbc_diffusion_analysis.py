import geopandas as gpd
import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict

def analyze_psbc_diffusion(
    input_file='data/processed/psbc.geojson',
    branch_types=None,  # 可以是 ['A'], ['S'], ['L'], ['A', 'S'] 等
    start_year=1997,
    end_year=2020,
    county_id_col='GisCountyID',
    institution_code_col='InstitutionCode',
    open_date_col='OpenDate',
    close_date_col='CloseDate'
):
    """
    分析PSBC网点扩张的时空分布
    
    参数:
        input_file: 输入文件路径
        branch_types: 网点类型列表，基于InstitutionCode第六位字符
                     None表示所有类型
                     ['A'] = 储蓄网点
                     ['S'] = 支行
                     ['L'] = 贷款网点
                     ['A', 'S'] = 储蓄网点和支行
                     等等
        start_year: 分析起始年份
        end_year: 分析结束年份
        county_id_col: 县级ID列名
        institution_code_col: 机构代码列名
        open_date_col: 开业日期列名
        close_date_col: 关业日期列名
    
    返回:
        dict: 包含分析结果的字典
            - 'first_branch_year': DataFrame，每个县第一次出现网点的年份
            - 'annual_cumulative': DataFrame，每年累积的县数量
            - 'branch_timeline': DataFrame，每个网点的时间线
            - 'county_branch_counts': DataFrame，每个县每年的网点数量
    """
    
    print(f"加载数据: {input_file}")
    if input_file.endswith('.geojson'):
        df = gpd.read_file(input_file)
    else:
        df = pd.read_csv(input_file)
    
    print(f"原始数据形状: {df.shape}")
    
    # 数据清洗和预处理
    print("数据预处理...")
    
    # 确保必要列存在
    required_cols = [county_id_col, institution_code_col, open_date_col]
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"缺少必要列: {missing_cols}")
    
    # 过滤掉没有县级ID的记录
    df = df[df[county_id_col].notna()].copy()
    df[county_id_col] = df[county_id_col].astype(str)
    
    # 处理日期
    df[open_date_col] = pd.to_datetime(df[open_date_col], errors='coerce')
    if close_date_col in df.columns:
        df[close_date_col] = pd.to_datetime(df[close_date_col], errors='coerce')
    
    # 过滤掉没有开业日期的记录
    df = df[df[open_date_col].notna()].copy()
    
    # 筛选网点类型
    if branch_types is not None:
        print(f"筛选网点类型: {branch_types}")
        # 提取InstitutionCode第六位字符
        df['branch_type_code'] = df[institution_code_col].astype(str).str[5]
        df = df[df['branch_type_code'].isin(branch_types)].copy()
        print(f"筛选后数据形状: {df.shape}")
    
    # 提取开业年份
    df['open_year'] = df[open_date_col].dt.year
    
    # 筛选年份范围
    df = df[(df['open_year'] >= start_year) & (df['open_year'] <= end_year)].copy()
    print(f"年份筛选后数据形状: {df.shape}")
    
    if len(df) == 0:
        print("警告: 筛选后无数据!")
        return None
    
    # 分析1: 每个县第一次出现网点的年份
    print("计算每个县第一次出现网点的年份...")
    first_branch_year = df.groupby(county_id_col)['open_year'].min().reset_index()
    first_branch_year.columns = [county_id_col, 'first_branch_year']
    
    # 分析2: 每年累积的县数量（CDF）
    print("计算累积分布...")
    annual_data = []
    years = range(start_year, end_year + 1)
    
    cumulative_counties = set()
    for year in years:
        # 在这一年开设了网点的县
        new_counties = set(df[df['open_year'] == year][county_id_col].unique())
        cumulative_counties.update(new_counties)
        
        annual_data.append({
            'year': year,
            'new_counties_count': len(new_counties),
            'cumulative_counties_count': len(cumulative_counties),
            'new_counties': list(new_counties) if new_counties else []
        })
    
    annual_cumulative = pd.DataFrame(annual_data)
    
    # 分析3: 网点时间线（每个网点的存续期间）
    print("构建网点时间线...")
    branch_timeline = df[[county_id_col, institution_code_col, open_date_col, close_date_col, 'open_year']].copy()
    if close_date_col in df.columns:
        branch_timeline['close_year'] = df[close_date_col].dt.year
        branch_timeline['is_active'] = df[close_date_col].isna()  # 是否仍在营业
    else:
        branch_timeline['close_year'] = np.nan
        branch_timeline['is_active'] = True
    
    # 分析4: 每个县每年的网点数量
    print("计算每个县每年的网点数量...")
    county_branch_counts = []
    
    for year in years:
        for county in df[county_id_col].unique():
            county_branches = df[df[county_id_col] == county]
            
            # 计算该年该县的活跃网点数
            active_branches = county_branches[county_branches['open_year'] <= year]
            
            if close_date_col in df.columns and not df[close_date_col].isna().all():
                # 如果有关业日期，排除已关业的网点
                active_branches = active_branches[
                    (active_branches[close_date_col].isna()) | 
                    (active_branches[close_date_col].dt.year > year)
                ]
            
            county_branch_counts.append({
                county_id_col: county,
                'year': year,
                'branch_count': len(active_branches),
                'cumulative_openings': len(county_branches[county_branches['open_year'] <= year])
            })
    
    county_branch_counts = pd.DataFrame(county_branch_counts)
    
    # 汇总统计信息
    print("\n=== 分析结果汇总 ===")
    print(f"总网点数: {len(df)}")
    print(f"涉及县数: {df[county_id_col].nunique()}")
    print(f"时间跨度: {df['open_year'].min()} - {df['open_year'].max()}")
    print(f"第一个有网点的县出现在: {first_branch_year['first_branch_year'].min()}年")
    print(f"最后一个有网点的县出现在: {first_branch_year['first_branch_year'].max()}年")
    print(f"最终覆盖县数: {annual_cumulative['cumulative_counties_count'].iloc[-1]}")
    
    if branch_types:
        print(f"网点类型: {branch_types}")
        type_dist = df['branch_type_code'].value_counts()
        for btype, count in type_dist.items():
            print(f"  类型{btype}: {count}个网点")
    
    return {
        'first_branch_year': first_branch_year,
        'annual_cumulative': annual_cumulative,
        'branch_timeline': branch_timeline,
        'county_branch_counts': county_branch_counts,
        'summary_stats': {
            'total_branches': len(df),
            'total_counties': df[county_id_col].nunique(),
            'time_span': (df['open_year'].min(), df['open_year'].max()),
            'final_coverage': annual_cumulative['cumulative_counties_count'].iloc[-1]
        }
    }


def plot_diffusion_analysis(results, save_path=None, figsize=(15, 10)):
    """
    绘制网点扩张分析图表
    
    参数:
        results: analyze_psbc_diffusion函数的返回结果
        save_path: 保存路径，如果为None则显示图表
        figsize: 图表大小
    """
    if results is None:
        print("无结果数据，无法绘图")
        return
    
    fig, axes = plt.subplots(2, 2, figsize=figsize)
    fig.suptitle('PSBC网点扩张分析', fontsize=16, fontweight='bold')
    
    # 图1: 累积县数量时间序列
    ax1 = axes[0, 0]
    annual_data = results['annual_cumulative']
    ax1.plot(annual_data['year'], annual_data['cumulative_counties_count'], 
             marker='o', linewidth=2, markersize=4)
    ax1.set_title('累积覆盖县数量')
    ax1.set_xlabel('年份')
    ax1.set_ylabel('累积县数量')
    ax1.grid(True, alpha=0.3)
    
    # 图2: 每年新增县数量
    ax2 = axes[0, 1]
    ax2.bar(annual_data['year'], annual_data['new_counties_count'], alpha=0.7)
    ax2.set_title('每年新增网点覆盖县数量')
    ax2.set_xlabel('年份')
    ax2.set_ylabel('新增县数量')
    ax2.grid(True, alpha=0.3)
    
    # 图3: 第一次出现网点年份分布
    ax3 = axes[1, 0]
    first_year_data = results['first_branch_year']
    year_counts = first_year_data['first_branch_year'].value_counts().sort_index()
    ax3.bar(year_counts.index, year_counts.values, alpha=0.7)
    ax3.set_title('各年份首次开设网点的县数量')
    ax3.set_xlabel('年份')
    ax3.set_ylabel('县数量')
    ax3.grid(True, alpha=0.3)
    
    # 图4: 网点数量分布箱线图
    ax4 = axes[1, 1]
    county_counts = results['county_branch_counts']
    latest_year = county_counts['year'].max()
    latest_counts = county_counts[county_counts['year'] == latest_year]['branch_count']
    
    ax4.hist(latest_counts, bins=20, alpha=0.7, edgecolor='black')
    ax4.set_title(f'{latest_year}年各县网点数量分布')
    ax4.set_xlabel('网点数量')
    ax4.set_ylabel('县数量')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存到: {save_path}")
    else:
        plt.show()


def save_results(results, output_dir='analysis_results'):
    """
    保存分析结果到文件
    
    参数:
        results: analyze_psbc_diffusion函数的返回结果
        output_dir: 输出目录
    """
    import os
    
    if results is None:
        print("无结果数据，无法保存")
        return
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存各个DataFrame
    results['first_branch_year'].to_csv(f'{output_dir}/first_branch_year.csv', index=False)
    results['annual_cumulative'].to_csv(f'{output_dir}/annual_cumulative.csv', index=False)
    results['branch_timeline'].to_csv(f'{output_dir}/branch_timeline.csv', index=False)
    results['county_branch_counts'].to_csv(f'{output_dir}/county_branch_counts.csv', index=False)
    
    # 保存汇总统计
    import json
    with open(f'{output_dir}/summary_stats.json', 'w', encoding='utf-8') as f:
        json.dump(results['summary_stats'], f, ensure_ascii=False, indent=2)
    
    print(f"结果已保存到目录: {output_dir}")


# 示例用法
if __name__ == "__main__":
    # 示例1: 分析所有类型网点
    print("=== 分析所有类型网点 ===")
    results_all = analyze_psbc_diffusion()
    
    if results_all:
        save_results(results_all, 'results_all_branches')
        plot_diffusion_analysis(results_all, 'plots/all_branches_diffusion.png')
    
    # 示例2: 只分析储蓄网点 (A类)
    print("\n=== 分析储蓄网点 (A类) ===")
    results_savings = analyze_psbc_diffusion(branch_types=['A'])
    
    if results_savings:
        save_results(results_savings, 'results_savings_branches')
        plot_diffusion_analysis(results_savings, 'plots/savings_branches_diffusion.png')
    
    # 示例3: 分析贷款相关网点 (L, S, B, U类)
    print("\n=== 分析贷款相关网点 ===")
    results_loan = analyze_psbc_diffusion(branch_types=['L', 'S', 'B', 'U'])
    
    if results_loan:
        save_results(results_loan, 'results_loan_branches')
        plot_diffusion_analysis(results_loan, 'plots/loan_branches_diffusion.png') 