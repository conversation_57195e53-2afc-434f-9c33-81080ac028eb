import geopandas as gpd
import pandas as pd

# 读取PSBC数据
print('加载PSBC银行数据...')
psbc_gdf = gpd.read_file('data/processed/psbc.geojson')
print(f'PSBC数据形状: {psbc_gdf.shape}')

print('\n列名:')
for i, col in enumerate(psbc_gdf.columns):
    print(f'{i}: {col}')

# 查找InstitutionCode相关列
institution_cols = [col for col in psbc_gdf.columns if 'institution' in col.lower() or 'code' in col.lower()]
print(f'\n机构代码相关列: {institution_cols}')

# 分析InstitutionCode
if 'InstitutionCode' in psbc_gdf.columns:
    print('\nInstitutionCode分析:')
    inst_col = psbc_gdf['InstitutionCode']
    non_null = inst_col.notna()
    print(f'非空值数量: {non_null.sum()} / {len(psbc_gdf)} ({non_null.sum()/len(psbc_gdf)*100:.2f}%)')
    
    if non_null.sum() > 0:
        codes = inst_col[non_null].astype(str)
        print(f'代码长度分布: {codes.str.len().value_counts().sort_index()}')
        
        # 分析第六位字符
        print('\n第六位字符分析:')
        codes_6plus = codes[codes.str.len() >= 6]
        if len(codes_6plus) > 0:
            sixth_chars = codes_6plus.str[5]  # 第六位（从0开始索引）
            sixth_char_counts = sixth_chars.value_counts()
            print('第六位字符分布:')
            for char, count in sixth_char_counts.items():
                print(f'  {char}: {count} ({count/len(codes_6plus)*100:.2f}%)')
            
            # 显示示例代码
            print(f'\n示例代码 (共{len(codes_6plus)}个):')
            sample_codes = codes_6plus.sample(min(15, len(codes_6plus)))
            for code in sample_codes:
                print(f'  {code} -> 第六位: {code[5]}')
        
        # 检查B0018A开头的代码
        print('\n代码开头分析:')
        b0018a_codes = codes[codes.str.startswith('B0018A')]
        print(f'B0018A开头的代码数量: {len(b0018a_codes)} ({len(b0018a_codes)/len(codes)*100:.2f}%)')
        
        other_codes = codes[~codes.str.startswith('B0018A')]
        print(f'非B0018A开头的代码数量: {len(other_codes)} ({len(other_codes)/len(codes)*100:.2f}%)')
        
        # 分析不同开头的代码模式
        if len(codes) > 0:
            print('\n不同代码前缀分析:')
            prefixes = codes.str[:6].value_counts().head(10)
            for prefix, count in prefixes.items():
                print(f'  {prefix}: {count}')

# 查看其他可能相关的列
gis_cols = [col for col in psbc_gdf.columns if 'gis' in col.lower()]
if gis_cols:
    print(f'\nGIS相关列: {gis_cols}')
    
    if 'GisCountyID' in psbc_gdf.columns:
        print('\nGisCountyID分析:')
        gis_col = psbc_gdf['GisCountyID']
        non_null_gis = gis_col.notna().sum()
        print(f'非空值数量: {non_null_gis} / {len(psbc_gdf)} ({non_null_gis/len(psbc_gdf)*100:.2f}%)')
        if non_null_gis > 0:
            print(f'唯一值数量: {gis_col.nunique()}')
            print(f'示例值: {gis_col.dropna().sample(min(5, non_null_gis)).tolist()}') 