# ==============================================
# 检查 county_filter_results.pkl 文件
# ==============================================

import pandas as pd
import geopandas as gpd
import pickle
import os

print("=== 检查 county_filter_results.pkl 文件 ===")

# 1. 检查文件是否存在
pkl_file = "notebooks/shared_data/county_filter_results.pkl"
if os.path.exists(pkl_file):
    file_size = os.path.getsize(pkl_file)
    print(f"✅ 找到文件: {pkl_file}")
    print(f"   文件大小: {file_size/1024/1024:.2f} MB")
    
    # 2. 读取文件内容
    try:
        with open(pkl_file, 'rb') as f:
            saved_data = pickle.load(f)
        
        print(f"\n📋 文件内容分析:")
        print(f"   数据类型: {type(saved_data)}")
        
        if isinstance(saved_data, dict):
            print(f"   字典键: {list(saved_data.keys())}")
            
            # 检查每个键的内容
            for key, value in saved_data.items():
                print(f"\n   📄 {key}:")
                print(f"      类型: {type(value).__name__}")
                
                if hasattr(value, '__len__'):
                    print(f"      长度: {len(value)}")
                    
                    if hasattr(value, 'columns'):
                        print(f"      列数: {len(value.columns)}")
                        
                        # 特别关注 filtered_county_gdf
                        if 'filtered' in key.lower():
                            print(f"      ⭐ 这可能是原始的 filtered_county_gdf!")
                            print(f"      列名: {list(value.columns)[:10]}...")
                            
                            if 'data_source' in value.columns:
                                print(f"      ⚠️  包含 'data_source' 列")
                                print(f"      data_source 分布: {value['data_source'].value_counts().to_dict()}")
                            else:
                                print(f"      ✅ 没有 'data_source' 列，应该是原始版本")
                        
                        # 检查其他相关数据
                        elif 'removed' in key.lower():
                            print(f"      📝 这可能是 removed_districts_info")
                            
                elif isinstance(value, list):
                    print(f"      列表长度: {len(value)}")
                    if len(value) > 0:
                        print(f"      第一个元素类型: {type(value[0]).__name__}")
        
        elif hasattr(saved_data, 'columns'):
            # 如果直接是一个DataFrame
            print(f"   记录数: {len(saved_data)}")
            print(f"   列数: {len(saved_data.columns)}")
            print(f"   列名: {list(saved_data.columns)[:10]}...")
            
            if 'data_source' in saved_data.columns:
                print(f"   ⚠️  包含 'data_source' 列")
                print(f"   data_source 分布: {saved_data['data_source'].value_counts().to_dict()}")
            else:
                print(f"   ✅ 没有 'data_source' 列")
        
        # 3. 提供恢复方案
        print(f"\n🔧 恢复方案:")
        
        if isinstance(saved_data, dict):
            # 寻找 filtered_county_gdf
            filtered_keys = [k for k in saved_data.keys() if 'filtered' in k.lower()]
            if filtered_keys:
                filtered_key = filtered_keys[0]  # 取第一个匹配的键
                filtered_data = saved_data[filtered_key]
                
                print(f"   找到可能的 filtered_county_gdf: '{filtered_key}'")
                print(f"   记录数: {len(filtered_data)}")
                
                if len(filtered_data) < 1600:  # 合理的数量
                    print(f"   ✅ 数量合理，可以恢复")
                    print(f"   ```python")
                    print(f"   # 从保存文件中恢复")
                    print(f"   with open('{pkl_file}', 'rb') as f:")
                    print(f"       saved_data = pickle.load(f)")
                    print(f"   filtered_county_gdf = saved_data['{filtered_key}']")
                    print(f"   print(f'恢复后: {{len(filtered_county_gdf)}} 条记录')")
                    print(f"   ```")
                    
                    # 如果有data_source列，提供清理方案
                    if hasattr(filtered_data, 'columns') and 'data_source' in filtered_data.columns:
                        print(f"   ")
                        print(f"   # 如果需要清理data_source列")
                        print(f"   if 'data_source' in filtered_county_gdf.columns:")
                        print(f"       if 'original_filtered' in filtered_county_gdf['data_source'].values:")
                        print(f"           filtered_county_gdf = filtered_county_gdf[")
                        print(f"               filtered_county_gdf['data_source'] == 'original_filtered'")
                        print(f"           ].drop(columns=['data_source']).copy()")
                        print(f"       else:")
                        print(f"           filtered_county_gdf = filtered_county_gdf.drop(columns=['data_source'])")
                else:
                    print(f"   ⚠️  数量异常，可能也是错误版本")
            
            # 寻找其他相关数据
            other_keys = [k for k in saved_data.keys() if k not in filtered_keys]
            if other_keys:
                print(f"   ")
                print(f"   其他可恢复的数据:")
                for key in other_keys:
                    value = saved_data[key]
                    if hasattr(value, '__len__'):
                        print(f"     {key}: {len(value)} 条记录")
        
        # 4. 一键恢复脚本
        print(f"\n🚀 一键恢复脚本:")
        print(f"   ```python")
        print(f"   import pickle")
        print(f"   with open('{pkl_file}', 'rb') as f:")
        print(f"       saved_data = pickle.load(f)")
        print(f"   ")
        print(f"   # 恢复所有变量")
        print(f"   for key, value in saved_data.items():")
        print(f"       globals()[key] = value")
        print(f"       print(f'恢复 {{key}}: {{len(value) if hasattr(value, \"__len__\") else type(value).__name__}}')")
        print(f"   ```")
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")

else:
    print(f"❌ 文件不存在: {pkl_file}")
    
    # 检查是否在其他位置
    possible_paths = [
        "shared_data/county_filter_results.pkl",
        "notebooks/county_filter_results.pkl",
        "county_filter_results.pkl"
    ]
    
    print(f"\n🔍 检查其他可能位置:")
    for path in possible_paths:
        if os.path.exists(path):
            print(f"   ✅ 找到: {path}")
            pkl_file = path
            break
        else:
            print(f"   ❌ 不存在: {path}")

print(f"\n=== 检查完成 ===") 