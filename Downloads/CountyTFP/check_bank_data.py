from drafts.load_geo_data import load_geo_data
import pandas as pd

print('加载数据...')
gdf = load_geo_data()
print(f'数据形状: {gdf.shape}')

print('\n银行相关列:')
bank_cols = [col for col in gdf.columns if 'institu' in col.lower() or 'code' in col.lower() or 'bank' in col.lower() or 'psbc' in col.lower()]
print(bank_cols)

print('\n所有列名:')
for i, col in enumerate(gdf.columns):
    print(f'{i}: {col}')

# 检查InstitutionCode
if 'InstituionCode' in gdf.columns:
    print('\nInstituionCode列分析:')
    non_null = gdf['InstituionCode'].notna()
    print(f'非空值数量: {non_null.sum()}')
    if non_null.sum() > 0:
        codes = gdf[non_null]['InstituionCode'].astype(str)
        print(f'代码长度分布: {codes.str.len().value_counts().head()}')
        print('示例代码:')
        sample_codes = codes.sample(min(10, len(codes)))
        for code in sample_codes:
            print(f'  {code}')
            # 分析第六位
            if len(code) >= 6:
                print(f'    第六位: {code[5]}')

# 检查其他银行相关列
for col in bank_cols:
    if col in gdf.columns:
        print(f'\n{col}列信息:')
        non_null = gdf[col].notna().sum()
        print(f'非空值数量: {non_null} ({non_null/len(gdf)*100:.2f}%)')
        if non_null > 0:
            print(f'唯一值数量: {gdf[col].nunique()}')
            if gdf[col].dtype == 'object':
                print(f'示例值: {gdf[col].dropna().sample(min(5, non_null)).tolist()}') 